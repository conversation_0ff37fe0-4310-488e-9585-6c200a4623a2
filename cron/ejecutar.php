
<?php
date_default_timezone_set('Europe/Madrid'); 
require_once(__DIR__ . "/funciones_cron.php");
require_once(__DIR__ . "/../pdo.php");

$pdo = new PDO($dsn, $user_db, $pass_db, $pdo_options); 
$tarea = $argv[1];
$modomantenimiento = file_exists("httpdocs/mantenimiento_1.txt") ? true : false; 
$dominio = "citas10.es"; // usado por copyFiles para scraping
$date = date('Y-m-d (H:i:s)'); 
$logfile_ruta = __DIR__ . "/logs/" . date('Y-m-d') . ".txt";

// RUTA DE DESTINO
$directorio = $tarea == "mantenimiento" ? "httpdocs/$tarea" : "backups/$tarea"; 
 
// ¿ LA BASE DE DATOS CAMBIÓ DESDE LA ÚLTIMA BACKUP ?
$datefiletime = datefileRead($directorio); // (Y-m-d_H-i) 
if ( !dbUpdateAfter( $datefiletime ) OR $modomantenimiento) {  
    echo "No se realizó tarea $tarea porque desde la última no hubo cambios o porque porque se esta en modo mantenimiento";
    exit();
}
/**------------------------------------------------------------------------
 *      mslog
 *------------------------------------------------------------------------**/ 
    // Contenido previo si existe lo mantenemos
    $contenido = ""; 
    if( file_exists($logfile_ruta) ){
        $contenido = file_get_contents($logfile_ruta);
    } 
    // Abre el archivo o lo crea vacio sin contenido previo
    $logfile = fopen($logfile_ruta, "w"); 
    fwrite( $logfile, "$contenido \n ==================  INICIA EL CRON $tarea en $date ===================  \n" );
        
/**------------------------------------------------------------------------
 *                  EJECUCIÓN DE LA TAREA
 *------------------------------------------------------------------------**/ 
 $tarea($directorio);
 datefileUpdate($directorio);   
 fclose($logfile);