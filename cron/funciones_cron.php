
<?php
 
// delete all files and sub-folders from a folder
function deleteAll($dir) {

    foreach(glob($dir . '/*') as $file) {
        if(is_dir($file))
            deleteAll($file);
        else
            unlink($file);
    }
        
    rmdir($dir);

  } 

function makeAll($dir, $mode = 0777, $recursive = true)
 {
     if (is_null($dir) || $dir === "") {
         return false;
     }
     
     if (is_dir($dir) || $dir === "/") {
         return true;
     }
     if (makeAll(dirname($dir), $mode, $recursive)) {
         return mkdir($dir, $mode);
     }
     return false;
 }


 
 function smartCopy($source, $dest, $options=array('folderPermission'=>0755,'filePermission'=>0755))
 {
     $result=false;
     
     //For Cross Platform Compatibility
     if (!isset($options['noTheFirstRun'])) {
         $source=str_replace('\\', '/', $source);
         $dest=str_replace('\\', '/', $dest);
         $options['noTheFirstRun']=true;
     }
     
     if (is_file($source)) {
         if ($dest[strlen($dest)-1]=='/') {
             if (!file_exists($dest)) {
                 makeAll($dest, $options['folderPermission'], true);
             }
             $__dest=$dest."/".basename($source);
         } else {
             $__dest=$dest;
         }
         if (!file_exists($__dest)) {
             $result=copy($source, $__dest);
             chmod($__dest, $options['filePermission']);
         }
     } elseif (is_dir($source)) {
         if ($dest[strlen($dest)-1]=='/') {
             if ($source[strlen($source)-1]=='/') {
                 //Copy only contents
             } else {
                 //Change parent itself and its contents
                 $dest=$dest.basename($source);
                 @mkdir($dest);
                 chmod($dest, $options['filePermission']);
             }
         } else {
             if ($source[strlen($source)-1]=='/') {
                 //Copy parent directory with new name and all its content
                 @mkdir($dest, $options['folderPermission']);
                 chmod($dest, $options['filePermission']);
             } else {
                 //Copy parent directory with new name and all its content
                 @mkdir($dest, $options['folderPermission']);
                 chmod($dest, $options['filePermission']);
             }
         }
 
         $dirHandle=opendir($source);
         while ($file=readdir($dirHandle)) {
             if ($file!="." && $file!="..") {
                 $__dest=$dest."/".$file;
                 $__source=$source."/".$file;
                 //echo "$__source ||| $__dest<br />";
                 if ($__source!=$dest) {
                     $result=smartCopy($__source, $__dest, $options);
                 }
             }
         }
         closedir($dirHandle);
     } else {
         $result=false;
     }
     return $result;
 }


/* Funcion que compara si la DB fue actualizada tras una fecha dada */

function dbUpdateAfter($date){ // (Y-m-d_H-i)
    global $pdo;  

    $anuncios_update = "SELECT update_time FROM information_schema.tables WHERE table_name = 'anuncios'";
    $fotos_update =  "SELECT update_time FROM information_schema.tables WHERE table_name = 'fotos'";
    $horarios_anuncios_update = "SELECT update_time FROM information_schema.tables WHERE table_name = 'horarios_anuncios'";
    
    $anuncios_update = $pdo->query($anuncios_update)->fetch()->update_time;
    $fotos_update = $pdo->query($fotos_update)->fetch()->update_time;
    $horarios_anuncios_update = $pdo->query($horarios_anuncios_update)->fetch()->update_time;

    // Conversión a formato de objeto fecha
    $anuncios_update = date_create($anuncios_update);
    $fotos_update = date_create($fotos_update);
    $horarios_anuncios_update = date_create($horarios_anuncios_update);
    
    if(  $date > $anuncios_update AND $date > $fotos_update AND $date > $horarios_anuncios_update ){ 
        return false;
    }else{ 
        return true;
    }  

}

function datefileUpdate($path){
    // archivo de fecha 
     $datefile = fopen($path . "/datefile.txt", "w");
     fwrite($datefile, date('Y-m-d_H-i'));
     fclose($datefile);
}

function datefileRead($path){
    $fullpath = $path . "/datefile.txt";

    if(file_exists( $fullpath) ){
        $fecha_backup = trim( file_get_contents($fullpath) );
        $fecha_backup = date_create_from_format("Y-m-d_H-i", $fecha_backup);
        return $fecha_backup;
    }else{  
        $fecha_backup = date_create_from_format("Y-m-d_H-i", "2023-01-01_00-00");
        return $fecha_backup;
    }
  
}

function removeOlds($globArg, $num = 5)
{

    $contenido = glob($globArg);
    rsort($contenido);
    $log = "\n";
    foreach ($contenido as $key => $filename) {
        
        if( $filename == "datefile.txt") continue;

        if ($key > $num) {
            if (is_dir($filename)) {
                $log .= " Carpeta $filename (Se borrará) \n";
                exec("rm -rf $filename");
            } else {
                $log .= " Zip: $filename (Se borrará) \n";
                unlink($filename);
            }
        }
    }
}



function msglog($msg){
    global $logfile_ruta;
    $date = date('Y-m-d (H:i:s)'); 

    // Contenido previo si existe lo mantenemos
    $contenido = ""; 
    $contenido = file_get_contents($logfile_ruta);  
    $contenido .= "$date : $msg\n";

    // Abre el archivo o lo crea vacio sin contenido previo
    $logfile = fopen($logfile_ruta, "w"); 
     
    // Escribimos en el archivo el contenido
    fwrite( $logfile, "$contenido"); 
}

function mantenimiento($directorio){

// DB Conexión
    global $pdo; 
    global $dominio;

    msglog("Inicia la tarea de mantenimiento"); 
 
 /**----------------------------------------------
  *     CREARA UN ZIP CADA HORA
  *---------------------------------------------**/
    if ( file_exists("httpdocs/mantenimiento/datefile.txt") ){ 
        
        $dateBackup = datefileRead($directorio); // Fecha de la última realización
          // Recuperamos la fecha de la última copia 
 
        $hora = date_format($dateBackup, 'H');

        // Si la hora actual cambia se crea un ZIP
        if (date('H') == $hora AND file_exists("httpdocs/mantenimiento_backups/". date('Y-m-d_H') .".tar.gz") ){ 
            msglog("Son las ". date('H') ." y el backup también se realizo a las $hora H, no se creara un ZIP de la copia hasta la próxima hora");
        }else{ 
            smartCopy("httpdocs/mantenimiento", "httpdocs/mantenimiento_backups/". date('Y-m-d_H'));
            exec("tar -zcvf httpdocs/mantenimiento_backups/". date('Y-m-d_H') .".tar.gz httpdocs/mantenimiento");
            msglog("Se creará un ZIP, ya que el último creado se hizo a las $hora horas");  
        }

         // Elimina backups antiguos 
        removeOlds("httpdocs/mantenimiento_backups/*", 8); 

        deleteAll('httpdocs/mantenimiento');
    }
 
    // Nueva copia
    
    if (!file_exists("httpdocs/mantenimiento")) mkdir("httpdocs/mantenimiento");
    $anuncios = $pdo->query("SELECT * FROM anuncios WHERE estado_anuncio=1")->fetchAll(); // Crea OBJ con los datos para listar en html  
  
     // Creamos carpetas
     $ruta_mantenimiento = "httpdocs/mantenimiento";
     if (!file_exists($ruta_mantenimiento)) mkdir($ruta_mantenimiento); 

     $ruta_uploads = "httpdocs/mantenimiento/uploads";
     if (!file_exists($ruta_uploads)) mkdir($ruta_uploads); 

     $ruta_anuncio = "httpdocs/mantenimiento/anuncio";
     if (!file_exists($ruta_anuncio)) mkdir($ruta_anuncio); 

     $ruta_inicio = "httpdocs/mantenimiento/inicio";
     if (!file_exists($ruta_inicio)) mkdir($ruta_inicio); 

     $ruta_fotos = "httpdocs/mantenimiento/uploads/fotos";
     if (!file_exists($ruta_fotos)) mkdir($ruta_fotos); 

     $ruta_maps = "httpdocs/mantenimiento/uploads/maps";
     if (!file_exists($ruta_maps)) mkdir($ruta_maps); 

     $ruta_videos = "httpdocs/mantenimiento/uploads/videos";
     if (!file_exists($ruta_videos)) mkdir($ruta_videos); 

     $ruta_mantenimiento_backups = "httpdocs/mantenimiento_backups";
     if (!file_exists($ruta_mantenimiento_backups)) mkdir($ruta_mantenimiento_backups); 

     
    // Copia actualizada de carpetas de archivos recursivos 
    smartCopy(__DIR__."/../js", "httpdocs/mantenimiento/js");
    smartCopy(__DIR__."/../css", "httpdocs/mantenimiento/css");
    smartCopy(__DIR__."/../files", "httpdocs/mantenimiento/files");
    
    /**---------------------------------------------------------------------
     * 
     *    ARCHIVO DE REDIRECCIÓN DE /mantenimiento a /mantenimiento/inicio
     * 
     *-------------------------------------------------------------------**/

    $contenido = 
    "
    <html>
        <head>
            <script>
                let url = window.location.href;
                console.log(url);
                window.location.replace(url+'inicio');
            </script>
        </head> 
    </html>
    "; 

    // $file_redirect = fopen("mantenimiento/index.html", "w");
    file_put_contents("httpdocs/mantenimiento/index.html", $contenido);


    /**------------------------------------------------------------------------------------------------
     * *                      
     *          GENERACIÓN DE LA PORTADA INDEX
     *   
     *------------------------------------------------------------------------------------------------**/
  
        /**--------------------------------------------
         *   Altera mediante JS el CSS ocultando diversos elementos
        *   y mostrando un texto de aviso de que esta el modo mantenimiento
        *  ----------
        *  @param el contenido html capturado con file_get_contents(url)
        *  
        *---------------------------------------------**/
    
    function addScript($content){

        $script = "
        <script>
        /* ---------- SELECTORES ------------ */
        let interface = document.querySelector('.interface'); 
        let botones = document.querySelector('.botones'); 
        let ubicacion = document.querySelector('.ubicacion'); 

        /* ---------  OCULTACIÓN DE ELEMENTOS ------------ */ 
        interface.style.display = 'none'; 
        botones.classList.remove('d-flex');
        botones.style.display = 'none';  
        ubicacion.style.display = 'none';

        /* ------------- SELECTORES -------------------- */
    
        let nav = document.querySelector('.nav-max');
        let mov = document.querySelector('.mov-max');

        /* ------------- NODOS ------------------------ */
        let h2 = document.createElement('h2');
        let h2mov = document.createElement('h2');

        /* ---------------- AGREGAR CONTENIDO PC ------------- */
        nav.style.justifyContent = 'start';
        h2.innerText = 'EN MANTENIMIENTO, MENÚ NO DISPONIBLE';
        h2.style.marginLeft = '30px';
        h2.style.fontSize = '1.5rem';
        h2.style.lineHeight = '2.6';
        h2.style.textAlign = 'center';
        nav.appendChild(h2);
    
        /* ---------------- AGREGAR CONTENIDO MOVIL ------------- */
        mov.style.justifyContent = 'start';
        h2mov.innerText = 'EN MANTENIMIENTO, MENÚ NO DISPONIBLE';
        h2mov.style.fontSize = '1rem'; 
        h2mov.style.marginLeft = '10px'; 
        mov.appendChild(h2mov);

        /* ------------------- FIN ------------------------ */
        </script>  
        </body>
        ";

        return str_replace("</body>", $script, $content);

    }

     $html = file_get_contents("http://$dominio/inicio");
     $html = addScript($html);
     file_put_contents("httpdocs/mantenimiento/inicio/index.html", $html);
      
  
    /**------------------------------------------------------------------------------------------------
     * *                      
     *          ANUNCIOS INDEX CREACIÓN PUBLICADOS FICHAS
     *   
     *------------------------------------------------------------------------------------------------**/


     function addScript2($content){ 
        $script = "
        <script> 
        let navbar = document.querySelector('.contenido a');
        navbar.href = '../../';   
        </script>  
        </body>
      ";
      return str_replace("</body>", $script, $content);
    }

    foreach($anuncios as $anuncio){
       
        // Si no existe carpeta la creamos
        $ruta = "httpdocs/mantenimiento/anuncio/$anuncio->id";
        if (!file_exists($ruta)) mkdir($ruta);
        // Leemos index y lo guardamos
        $html = file_get_contents("http://$dominio/anuncio/$anuncio->id/");
        $html = addScript2($html);
        file_put_contents("httpdocs/mantenimiento/anuncio/$anuncio->id/index.html", $html);
  
        // ------------------------   Archivos de anuncio ----------------------.
        $fotos = $pdo->query("SELECT * FROM fotos WHERE id_anuncio=$anuncio->id ORDER BY pos ASC")->fetchAll(); 

         // Fotos 
         foreach($fotos as $foto){
             $origen = __DIR__."/../uploads/fotos/$foto->filename";
             $destino = "httpdocs/mantenimiento/uploads/fotos/$foto->filename";  
             copy($origen, $destino);
         }

         // Mapa
         if(file_exists(__DIR__."/../uploads/maps/$anuncio->id.jpg")){
             copy(__DIR__."/../uploads/maps/$anuncio->id.jpg", "httpdocs/mantenimiento/uploads/maps/$anuncio->id.jpg");
         }
         // video
         if(file_exists(__DIR__."/../uploads/videos/$anuncio->id.mp4")){
            copy(__DIR__."/../uploads/videos/$anuncio->id.mp4", "httpdocs/mantenimiento/uploads/videos/$anuncio->id.mp4");
        }
 
    }

    

    $N_anuncios = count($anuncios);
    msglog("Copiado de archivos finalizado, se encontrarón $N_anuncios anuncios");

    $date = date('Y-m-d_H');
    exec("touch httpdocs/mantenimiento.tar.gz");
    exec("tar -zcvf httpdocs/mantenimiento.tar.gz httpdocs/mantenimiento");
    // msglog("Se comprimio la versión de mantenimiento");
 
    msglog("Finaliza la tarea de mantenimiento"); 

}


function database($directorio){
    global $pdo;

    //Datos de la base de datos
    $mysqlDatabaseName = 'citas10-2022';
    $mysqlUserName = 'citas10';
    $mysqlPassword = 'bremen@1381';
    $host = 'localhost';

    $directorio = "backups/database/" . date('Y-m-d_H-i') . ".sql"; 
    $command = 'mysqldump --opt -h' . $host . ' -u' . $mysqlUserName . ' -p' . $mysqlPassword . ' ' . $mysqlDatabaseName . ' > ' . $directorio;
    exec($command); 
    // Elimina backups antiguos 
    removeOlds("backups/database/*", 100); 
}


function uploads($directorio){

    $date = date("Y-m-d_H-i");  
    $origen = "httpdocs/uploads";
    $destino = "backups/uploads/uploads__$date.tar.gz";
    exec("tar -zcvf $destino $origen"); 
   // Elimina backups antiguos 
   removeOlds("backups/uploads/*", 5); 
}