
<?php

 
 $pdo_options = [
    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES 'utf8mb4'", // especificar un comando inicial para la conexión.
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_OBJ, //establecer el formato en que se devuelven los datos
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
   // PDO::ATTR_PERSISTENT => true
  ];
 
  if ( PHP_OS == 'WINNT' ){
    $host = "localhost";
    $user_db = "root";
    $pass_db = "";
    $db_name = "citas10-2022";
  }else{
    $user_db = "citas10-2022"; //"citas10-2022"
    $host = "localhost";
    $pass_db = "bremen@1381"; // "bremen@1381" 
    $db_name = "citas10-2022"; 
  }

  $dsn = "mysql:host=$host;dbname=$db_name;charset=utf8mb4"; 

  // backup mods
   $host_backup = "";
   $dsn_backup = "mysql:host=$host;dbname='replica';charset=utf8mb4";

  Class Replicate extends \PDO{

       
        public $pdo_backup; 

        public function __construct($dsn, $username = null, $password = null, $pdo_options = null) {
            
            global $dsn_backup;
             
            // CONEXIÓN DB BACKUP
            $this->pdo_backup = new PDO($dsn_backup, $username, $password, $pdo_options); 

            parent::__construct($dsn, $username, $password, $pdo_options);
        }  

        public function backup(){

            return $this->pdo_backup;
        }

        public function query( $query, $fetchMode = null, ...$fetchModeArgs){
  
            $result = parent::query( $query, $fetchMode = null, ...$fetchModeArgs);
            $this->pdo_backup->query( $query);
            return $result;
        }


        /* 
        
        try {    
            $pdo = new PDO($dsn, $user_db, $pass_db, $pdo_options) ; 
     
        } catch (Exception $e) { 

        } 

        */

  
  } 

  $pdo = new Replicate( $dsn, $dsn_backup, $user_db, $pass_db, $pdo_options); 

  $resultado = $pdo->query("SELECT * FROM usuarios")->fetchAll();
    
  var_dump($resultado);