<?php 
session_start(); 
include_once "models/Models.php"; 
// Forzar redirección de vistas con / al final x

if( !isset($_GET['buscar']) or !isset($_GET['search']) ){ 
  $request = $_SERVER['REQUEST_URI'];
  $requestNoGet = explode("&", $request)[0];
  // ---------- es necesario ? ---------------
  //  if ( substr($requestNoGet, -1) != '/' ) header("Location: $requestNoGet/");
}


//---------------------------------

// Solo se cargará una vez la instancia de Flex
require_once "includes/Flex.php";
 include_once "includes/phprouter.php"; 

require_once 'vendor/autoload.php';


// key apis <NAME_EMAIL>
$MAPKEY = 'AIzaSyDwD7qDAQ2FhG0zILfd0Z_EjLItMckPUqM'; 
// FAKER
$faker = Faker\Factory::create('es_ES');

// zipfile "nelexa/zip": "^4.0"
$zipFile = new \PhpZip\ZipFile();

//----------------- APP INIT AND CONFIG
include_once "includes/framework.php";
$opciones = [ "dev" => "auto" ];
// Set Enviroment and init
app\init($opciones);


// Email notifications function
include_once "includes/mail.php";

// --------------------- BASE DE DATOS CREDENCIALES Y CONNECTION
$debugbar = new DebugBar\StandardDebugBar();
require_once "includes/pdo.php";

// ------------------------------------------ SET LAST ANUNCIO ID ( BETA )  ----------------------------------------------

// Si no se ha seteado establecemos el valor por defecto
if (!isset($_SESSION["lastid"])) $_SESSION["lastid"] = false;

// Si GET modifica el valor de SESSION se setea nuevo
if (isset($_GET['id']) and $_GET['id'] != $_SESSION["lastid"]) {
    $_SESSION["lastid"] = $_GET['id'];
}

$id = $_SESSION["lastid"]; 


// ------------------------------------------ FIN SET LAST ANUNCIO ID ( BETA )  ----------------------------------------------

include_once 'includes/smartcopy.php';
include_once 'includes/helpers.php';
include_once 'includes/funciones.php';
include_once 'includes/mantenimiento.php';

$debugbarRenderer = $debugbar->getJavascriptRenderer();
  
checkEvents();

if( !isset($_SESSION['userGPS']) ) userGPS(); //LOCALIZAR POR IP LA CIUDAD

 

/* ------------------------ AUTH -------------------------------------------------------- */

  include_once 'includes/auth.php';
  
/* ------------------------- CARGA DE CONTROLADORES DINAMICA --------------------- */

   // recorrer el path en busca de controladores desde zona a ultima path heredando controladores superiores conservando path
   // Ejemplo: /inicio -> paginas/inicioController.php, /inicio/servicios -> paginas/inicio/serviciosController.php, /inicio/servicios/nuevos -> paginas/inicio/servicios/nuevosController.php
   
   flex()->controllers = [];
   if (is_numeric(flex()->uri(-1))){
    // guardar el array sin el ultimo elemento
    $items = array_slice(flex()->uri("a"), 0, -1);  
   }else{
    $items = flex()->uri("a");
   }
   // $profundiad = is_numeric(flex()->uri(-1)) ? array_pop(flex()->uri("a")) : $items = flex()->uri("a") : $items = flex()->uri("a");
   // eliminar el ultimo elemento de un array
 
     // Si la vista es un index de una carpeta, agrega el controlador de la carpeta
   flex()->controllers[] = "paginas/" . flex()->zone . "/" . flex()->zone . "Controller.php";

    $folder = "";
   foreach($items as $item){ 
     flex()->controllers[] = "paginas/" . $folder.$item . "Controller.php";
     $folder .= "$item/";
   }
   // eliminar el segundo valor del array 
   array_splice(flex()->controllers, 1, 1); 
 
   // Si existe controlador de vista lo carga
   foreach(flex()->controllers as $controller){
     if( file_exists($controller) ) include $controller;  
     flex()->debugbarInfo($controller." loaded");
   }
     
 

/* --------------------------- FUNCTION EXECUCTION ------------------------------------ */


include_once 'includes/funciones_fun.php';
 
// USO: panel/editar/x&fun[]=sum(a:1,b:1)&fun[]=escribe(a:mensaje+para+ti)

if ( isset($_GET['fun'])) {
  $funciones = $_GET['fun'];
  if (is_array($funciones)) {
     // si en las funciones existe la funcion redirect la eliminamos y insertamos al final
     if (in_array("redirect", $funciones)) {
      $funciones = array_diff($funciones, ["redirect"]);
      $funciones[] = "redirect";
     } 
      foreach ($funciones as $funcion) {
        fun($funcion);
      } 
  }else{ 
    fun($funciones); 
   }
}  

/* ------------------------- CARGA DE HEAD / NAV DINAMICO --------------------- */
  
   if (flex()->is_page) {
    // HEAD 
    flex()->head = "paginas/$flex->zone/head.php";
    if( file_exists(flex()->head) ) include flex()->head; 
    // NAV
    flex()->nav = "paginas/$flex->zone/nav.php";
    if( file_exists(flex()->nav) ) include flex()->nav; 
     echo $debugbarRenderer->renderHead();
  }else{
    if(isset($_GET['d']))
    echo $debugbarRenderer->renderHead();
  } 
  if(isset($_GET['api']))
  include "partials/head_api.php";

 

/* ------------------ |  PAGINA - Router View Load | ------------------------  */

 include_once 'includes/routes.php';

/* --> auto  */
if (file_exists( $flex->view_path ) && !isset($_GET['action']) && !isset($_GET['api'])) {
  include $flex->view_path; 

} else {
   http_response_code(404);
  $flex->debugbarInfo("No se encuentra".$flex->view_path);
  //  header("Location: /inicio/"); 
}
 
/* ------------------------- CARGA DE FOOTER DINAMICO --------------------- */
  
if (flex()->is_page) { 
  // FOOTER
  flex()->footer = "paginas/$flex->zone/footer.php";
  if( file_exists(flex()->footer) ) include flex()->footer; 
} 


/* ------------------------- fin contenido ------------------------------------------------*/
  $debugbar->addCollector(new DebugBar\DataCollector\ConfigCollector( app\debug_config(), "Config"));
   // $debugbar->addCollector(new DebugBar\DataCollector\ConfigCollector( $_SESSION['logbar'] ?? [] , "logbar"));
  $debugbar->addCollector(new DebugBar\DataCollector\ConfigCollector( array_reverse($_SESSION['logg']) ?? [] , "logg"));
  $dev = [
      "Route"=> "uri:".$flex->uri().", view: ". $flex->view.", zone:$flex->zone",
      "view_path & zones"=> $flex->view_path. ", zones: ". json_encode($flex->zones),
      "Partials"=> " head: partials/head_$flex->zone.php, footer: partials/footer_$flex->zone.php",
      "user"=> json_encode(flex()->user),
      "Flex()->route"=> $flex->route . " args: " .  $flex->args 
  ];

  $debugbar->addCollector(new DebugBar\DataCollector\ConfigCollector(  $dev , "Dev"));
  $debugbar->addCollector(new DebugBar\DataCollector\ConfigCollector(  (array) flex(), "Flex" ));
 
if ( ($flex->user->rol >=5 && $flex->is_page) || $flex->is_page && $flex->dev() || isset($_GET['d'])) echo $debugbarRenderer->render();
showAlerts();

?>