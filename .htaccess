#---------| Activate rewrite |---------------------- 

RewriteEngine On
RewriteBase /
 

#---------| add / in the end of request not GET request |----------
 # RewriteCond %{REQUEST_URI} !.*:[^/]*$
 # RewriteCond %{REQUEST_FILENAME} !-d
 # RewriteCond %{REQUEST_FILENAME} !-f
 # RewriteCond %{REQUEST_URI} /+[^\.]+$
 # RewriteCond %{REQUEST_URI} !&
  # -- <PERSON><PERSON><PERSON><PERSON> pero sin perder el request method
 # RewriteRule ^(.+[^/])$ $1/ [L,PT]

 #---------| add / at the end of URLs (except files/dirs) |----------
 RewriteCond %{REQUEST_FILENAME} !-d
 RewriteCond %{REQUEST_FILENAME} !-f
 RewriteCond %{REQUEST_URI} !\.[a-zA-Z0-9]{2,6}$
 RewriteCond %{REQUEST_URI} !/$
 RewriteCond %{REQUEST_URI} !&
 RewriteRule ^(.+?)(\?.*)?$ /$1/ [R=301,L]

#---------| REDIRECT WWW TO NON-WWW FOR PRODUCTION URL/DOMAIN |--------#

RewriteCond %{HTTP_HOST} !^(.*)(\.test|\.dev|localhost)$ [NC]
RewriteCond %{HTTP_HOST} ^(.*)(test.|dev.|local.)$ [NC]
RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
RewriteRule ^(.*)$ https://%1/$1 [L,PT]

#---------| REDIRECT HTTP => HTTPS ONLY IN .COM DOMAIN |---------------  

 RewriteCond %{HTTPS} off
 RewriteCond %{HTTP_HOST} !^(.*)(\.test)$ [NC]
 RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,PT]
 
#---------|  FRONT CONTROLLER OPTION 1: Root directory |----------------
  
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^(.*)$ /?url=$1 [QSA,L]
 