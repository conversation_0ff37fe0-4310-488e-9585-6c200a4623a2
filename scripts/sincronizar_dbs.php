
<?php

/**------------------------------------------------------------------------------------------------
 *        <PERSON><PERSON><PERSON><PERSON> bases de datos & set DBSync si se perdio sincronicidad 
 *      ¿Contar las filas ?
 * 
 *------------------------------------------------------------------------------------------------**/

function getLastUpdateDB($pdo)
{ // (Y-m-d_H-i)  
  $lastupdate = "SELECT value FROM options WHERE name = 'lastupdate'";
  $resultado = $pdo->query($lastupdate)->fetch()->value; // date("Y-m-d H:i:s"); 2001-03-10 17:16:18 (el formato DATETIME de MySQL)
  $datetime = date_create($resultado);
  return $datetime;
}

/* Si estan sincronizadas */
function isSync()
{
  $pdo1 = getPDO1();
  $pdo2 = getPDO2();
  $update1 = getLastUpdateDB($pdo1);
  $update2 = getLastUpdateDB($pdo2);
  $config_file = json_decode(file_get_contents("config.json"));
  $ahora = new DateTime(date("Y-m-d H:i:s"));
   
  /* fecha de notificaciones */
  $lastnotify = date_create($config_file->lastnotify);
  $diff = $lastnotify->diff($ahora);

  /* Comparar fechas */
  $diff2 = date_diff($update1, $update2);
   
  if ($diff2->i >1) {
    $config_file->DBSync = 0;
    if ($diff->i > 1) {
      sendMail('Sincronización perdida', "se ha perdido la conexión con la 2º base de datos");
      $config_file->lastnotify = date("Y-m-d H:i:s");
    } 
  }else {
    if ($config_file->DBSync == 0){ 
      $config_file->DBSync = 1;
      sendMail('Las DBS vuelven a estar sincronizadas', "Todo va bien y el proyecto esta en 2 DBS");
      $config_file->lastnotify = date("Y-m-d H:i:s"); 
    }else{
        if ($diff->h > 12) {
        sendMail('Las DBS permanencen conectadas', "Todo va bien y el proyecto esta en 2 DBS");
        $config_file->lastnotify = date("Y-m-d H:i:s");
      }
    } 
  }

  file_put_contents('config.json', json_encode($config_file, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
}

isSync();


 /* SUGERENCIA A EXPERIMENTAR 
    En la fecha de ultima escritura tabla options en notes poner la SQL ejecutada.
    si coinciden la db deberia estar sincronizada, ver como meter la consulta
    mediante json o algo parecido.
 
 */


/* Cuando ocurre un error que cambia la DB origen, este no es sincronizado y si la base de datos de origen no es igual ya a la destino
  se enviará una notificación y no se sincronizará hasta una manual reparación del problema */

