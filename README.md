

Table anuncio {
id integer [primary key]
}

Table caracteristicas_terminos{
id integer [primary key]
title varchar
body text [note: 'Content of the post']
status varchar
}

Table caracteristicas_valores{
id interger [primary key]
termino_id interger
}

Ref: caracteristicas_valores.id > caracteristicas_terminos.id


## REQUERIMIENTOS
- PHP 8.3 PROBADO 
- COMPOSER
- APACHE 2.4.62-24
- MariaDB 10.
## APIS
- Google Maps cuenta: 
- Hero map cuenta:


## CONSULTAS UTILES

UPDATE anuncios SET telefono2 = REPLACE(telefono2, ' ', '');


## Funciones URL URL 2023

Diferencia entre fun() y getfun()

getfun(): Recupera valores para luego hacer loop o para imprimirlos en pantalla.
- Usar en código para recuperar valores de la DB.
No realiza redirección.


fun(): no retorna valores sino que realiza alguna acción, para enlaces a o action de forms

""REALIZA REDIRECCIÓN""
- Links y enlaces.
- Form action.
- URL test.


# Ejecución de funciones a través de la URL

Para activar esta funcionalidad se debe pasar el parametro GET &f
EJEMPLO:

/&f=imprimir(vercolores:verde,rojo;margin-top:10px;orden:az,price)

DONDE:

f = imprimir = Nombre de la función
(array_multidimensional)

opt=array_opciones
Ten en cuenta que actualmente solo funciona en /anuncios controller


---
logbar():


# otro enfoque de posición, virtuales y se gestione las fechas de publicación

# ordenar(dest)

##  npm install -g sass browser-sync localtunnel concurrently 
 
array_count_values — Cuenta todos los valores de un array
n_anuncios = array_count_values($anuncios) // total de resultados de ciudad y activos 

if(dest and fijo){
update anuncios set destacados_pos 
} 

for (i=1, anuncios < n_anuncios, i++) orderby pos 

// Repetidos

-- updatime contra = fecha de publicacion
-- updatime contra = actualizarse al cambiar una foto
-- update contra = anuncios pueden pagar por subir

    if( anuncios[i]->pos == anuncios[+1]->pos{

        anuncios[i]->updatetime > anuncios[+1]->updatetime {
            anuncio->pos = +i 
        }

    } 

|| !destacado_fijo)
anuncios[3]updatetime > anunicos[4] 
continue


1) Dime los repetidos

carolina - 7
Maria - 7

7 vs 7 carloina->updatetime > maria->updatatime
carolia ->
maria -> ret = +1 = 8

2) Dime los fijos activos de ciudad

 -> Hacer 2 loops uno pos (Todos consecutivos, orden por updatetime) y otro fijos
a) se colocan los posicion_fija
b) se colocan los posicion no fija saltando fijas


a2) se colocan los no fijos
b2) se colocan los poscicion fija y hacen +1 al resto no fija


## SOBRE RUTAS Y PROBELMAS DE GET/POST REPETIR PROCESOS Y MEJORAR ACCIONES

/anuncios/delete anunciosController@delete{id}

/anuncios/ver anunciosController@ver{id}

anuncios/&city=3&eliminar=23 //-> Problema de repetir la acción de forma involuntaria

/anunciosEliminar&id=32

/anuncios -> include paginas/anuncios.php (carga en medio)

paginas: listar, mostrar-uno, crear-editar form, 
 

/anuncio/&accion=eliminar&id=2

------- opcion C ajax / 
Subir, Eliminar, Caducar 

// campo de fotos de portadas y destcados para web publica
{
portada: ['/anuncios/foto/3023.jpg', '/anuncios/fotos/322.jpg'], 
destacado: ['/anuncios/foto/33.jpg', '/anuncios/foto/3rff3.jpg']
}

portadasImg = 3.jpg, 4jpg
destacadosImg = 1.jpg, 2.jpg


//

martes 4 -> plaza castaños 11 -> avenida comercial
martes 5 -> plaza del castillo
martes 6 -> zizur



use cleoadmsimple;
select a.id,

min(TIMESTAMPDIFF(SECOND,CURRENT_TIMESTAMP(),ha.hora_inicio) ) as timestamp_iniciar,
min(TIMESTAMPDIFF(SECOND,CURRENT_TIMESTAMP(),ha.hora_fin) ) as timestamp_finalizar,
min (ha.dia - DAYOFWEEK(now()) ) diff_dia
FROM anuncios a
INNER JOIN horarios_anuncios ha
ON a.id=ha.id_anuncio WHERE a.id_ciudad=1
AND ha.dia>= DAYOFWEEK(now())
group by a.id
ORDER BY timestamp_iniciar ASC, timestamp_finalizar ASC, diff_dia asc;


SELECT *, ROW_NUMBER() 
OVER (
    PARTITION BY a.id ORDER BY min(
        TIMESTAMPDIFF(SECOND,CURRENT_TIMESTAMP(),ha.hora_inicio)) ASC, 
        min(TIMESTAMPDIFF(SECOND,CURRENT_TIMESTAMP(),ha.hora_fin) ) ASC, 
        min (ha.dia - DAYOFWEEK(now()) ) ASC 
    )  
FROM anuncios a INNER horarios_anuncios ha
ON a.id=ha.id_anuncio WHERE a.id_ciudad=1
AND ha.dia>= DAYOFWEEK(now())