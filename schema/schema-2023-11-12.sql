

 SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

CREATE TABLE `admin_destacados_programados` (
`id` int(11)
,`id_usuario` int(11)
,`casa` tinyint(1)
,`id_categoria` tinyint(1)
,`id_ciudad` int(11)
,`id_pais` char(2)
,`titulo` varchar(100)
,`estado_anuncio` int(1)
,`estado_destacado` int(1)
,`fecha_creacion` timestamp
,`fecha_actualizacion` timestamp
,`fecha_caducidad` timestamp
,`fecha_publicacion` timestamp
,`fecha_posicionado` timestamp
,`fecha_posicionado_destacado` timestamp
,`salidas` tinyint(1)
,`nombre` varchar(50)
,`edad` int(2)
,`pais` varchar(50)
,`portadas` longtext
,`portada_mensaje` varchar(30)
,`destacado_tipo` int(1)
,`hayVideo` tinyint(1)
,`bizum` tinyint(1)
,`visa` tinyint(1)
,`telefono` varchar(50)
,`telefono2` varchar(50)
,`hayWhatsapp1` tinyint(1)
,`hayWhatsapp2` tinyint(1)
,`duracion` int(11)
,`descripcion` varchar(1000)
,`notas_admin` varchar(1000)
,`servicios` varchar(200)
,`notas` longtext
,`gps` point
,`fecha_destacar` timestamp
);

CREATE TABLE anuncios (
  id int(11) NOT NULL,
  id_usuario int(11) DEFAULT 1,
  casa tinyint(1) DEFAULT 0,
  id_categoria tinyint(1) NOT NULL DEFAULT 1,
  id_ciudad int(11) NOT NULL DEFAULT 1,
  id_pais char(2) DEFAULT NULL,
  titulo varchar(100) DEFAULT '',
  estado_anuncio int(1) DEFAULT 0,
  estado_destacado int(1) DEFAULT 0,
  fecha_creacion timestamp NOT NULL DEFAULT current_timestamp(),
  fecha_actualizacion timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  fecha_caducidad timestamp NOT NULL DEFAULT current_timestamp(),
  fecha_publicacion timestamp NOT NULL DEFAULT current_timestamp(),
  fecha_posicionado timestamp NOT NULL DEFAULT current_timestamp(),
  fecha_posicionado_destacado timestamp NOT NULL DEFAULT current_timestamp(),
  salidas tinyint(1) DEFAULT 0,
  nombre varchar(50) DEFAULT '',
  edad int(2) DEFAULT NULL,
  pais varchar(50) DEFAULT NULL,
  portadas longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(portadas)),
  portada_mensaje varchar(30) DEFAULT '',
  destacado_tipo int(1) DEFAULT NULL,
  hayVideo tinyint(1) DEFAULT 0,
  bizum tinyint(1) DEFAULT 0,
  visa tinyint(1) DEFAULT 0,
  telefono varchar(50) DEFAULT '',
  telefono2 varchar(50) DEFAULT '',
  hayWhatsapp1 tinyint(1) DEFAULT 0,
  hayWhatsapp2 tinyint(1) DEFAULT 0,
  duracion int(11) DEFAULT NULL,
  descripcion varchar(1000) DEFAULT '',
  notas_admin varchar(1000) DEFAULT '',
  servicios varchar(200) DEFAULT '',
  notas longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(notas)),
  gps point NOT NULL DEFAULT point(-1.6458160117659297,42.81269525952281)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

CREATE TABLE categorias (
  id tinyint(1) NOT NULL,
  nombre varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

CREATE TABLE ciudades (
  id int(11) NOT NULL,
  nombre varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

CREATE TABLE enlaces_anuncios (
  id int(11) NOT NULL,
  anuncio1 int(11) DEFAULT NULL,
  anuncio2 int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

CREATE TABLE eventos (
  id int(11) NOT NULL,
  id_anuncio int(11) DEFAULT NULL,
  fecha_creacion timestamp NOT NULL DEFAULT current_timestamp(),
  fecha_ejecucion timestamp NOT NULL DEFAULT current_timestamp(),
  tipo enum('destacado','anuncio') DEFAULT NULL,
  accion enum('publicar','despublicar') DEFAULT NULL,
  estado tinyint(1) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

CREATE TABLE fotos (
  id int(11) NOT NULL,
  id_anuncio int(11) DEFAULT NULL,
  estado int(1) DEFAULT 0,
  filename varchar(50) DEFAULT NULL,
  pos int(2) DEFAULT 0,
  destacado tinyint(1) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

CREATE TABLE horarios_anuncios (
  id int(11) NOT NULL,
  id_anuncio int(11) DEFAULT NULL,
  active int(1) DEFAULT NULL,
  num_rango int(1) DEFAULT 0,
  dia_inicio int(1) DEFAULT 0,
  dia_fin int(1) DEFAULT 6,
  hora_inicio time DEFAULT '00:00:00',
  hora_fin time DEFAULT '23:59:59'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

CREATE TABLE `options` (
  option_id int(11) NOT NULL,
  name varchar(50) NOT NULL,
  value varchar(50) NOT NULL,
  notes varchar(150) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE paises (
  alpha_2 char(2) NOT NULL,
  name varchar(75) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

CREATE TABLE roles (
  id int(11) NOT NULL,
  nombre varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

CREATE TABLE usuarios (
  id int(11) NOT NULL,
  id_rol int(11) DEFAULT NULL,
  email varchar(50) DEFAULT NULL,
  pass varchar(50) DEFAULT NULL,
  activo tinyint(1) DEFAULT NULL,
  tel varchar(14) DEFAULT NULL,
  token varchar(80) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
CREATE TABLE `view_portadas` (
`dia_inicio` int(1)
,`dia_fin` int(1)
,`dia_inicio_diff` bigint(13)
,`dia_fin_diff` bigint(13)
,`hora_inicio` time
,`hora_fin` time
,`timestamp_inicio` bigint(21)
,`timestamp_fin` bigint(22)
,`activo` int(1)
,`id` int(11)
,`id_usuario` int(11)
,`casa` tinyint(1)
,`id_categoria` tinyint(1)
,`id_ciudad` int(11)
,`id_pais` char(2)
,`titulo` varchar(100)
,`estado_anuncio` int(1)
,`estado_destacado` int(1)
,`fecha_creacion` timestamp
,`fecha_actualizacion` timestamp
,`fecha_caducidad` timestamp
,`fecha_publicacion` timestamp
,`fecha_posicionado` timestamp
,`fecha_posicionado_destacado` timestamp
,`salidas` tinyint(1)
,`nombre` varchar(50)
,`edad` int(2)
,`pais` varchar(50)
,`portadas` longtext
,`portada_mensaje` varchar(30)
,`destacado_tipo` int(1)
,`hayVideo` tinyint(1)
,`bizum` tinyint(1)
,`visa` tinyint(1)
,`telefono` varchar(50)
,`telefono2` varchar(50)
,`hayWhatsapp1` tinyint(1)
,`hayWhatsapp2` tinyint(1)
,`duracion` int(11)
,`descripcion` varchar(1000)
,`notas_admin` varchar(1000)
,`servicios` varchar(200)
,`notas` longtext
,`gps` point
);
DROP TABLE IF EXISTS `admin_destacados_programados`;

CREATE ALGORITHM=UNDEFINED DEFINER=cpses_ru0gl89b02@localhost SQL SECURITY DEFINER VIEW admin_destacados_programados 
 AS WITH eventos_destacar AS (SELECT eventos.id_anuncio AS `id_anuncio`, 
 eventos.fecha_ejecucion AS `fecha_destacar` FROM eventos WHERE eventos.tipo = 'destacado' AND eventos.accion = 'publicar' 
 AND eventos.estado = 1) SELECT anuncios.`id` AS `id`, anuncios.id_usuario AS `id_usuario`, anuncios.casa AS `casa`, anuncios.id_categoria AS
  `id_categoria`, anuncios.id_ciudad AS `id_ciudad`, anuncios.id_pais AS `id_pais`, anuncios.titulo AS `titulo`, anuncios.estado_anuncio AS
   `estado_anuncio`, anuncios.estado_destacado AS `estado_destacado`, anuncios.fecha_creacion AS `fecha_creacion`, anuncios.fecha_actualizacion
    AS `fecha_actualizacion`, anuncios.fecha_caducidad AS `fecha_caducidad`, anuncios.fecha_publicacion AS `fecha_publicacion`, anuncios.fecha_posicionado 
    AS `fecha_posicionado`, anuncios.fecha_posicionado_destacado AS `fecha_posicionado_destacado`, anuncios.salidas AS `salidas`, anuncios.nombre AS 
    nombre`, anuncios.edad AS `edad`, anuncios.pais AS `pais`, anuncios.portadas AS `portadas`, anuncios.portada_mensaje AS `portada_mensaje`, anuncios.destacado_tipo AS `destacado_tipo`, 
    anuncios.hayVideo AS `hayVideo`, anuncios.bizum AS `bizum`, anuncios.visa AS `visa`, anuncios.telefono AS `telefono`, anuncios.telefono2 AS `telefono2`, anuncios.hayWhatsapp1 AS `
    hayWhatsapp1`, anuncios.hayWhatsapp2 AS `hayWhatsapp2`, anuncios.duracion AS `duracion`, anuncios.descripcion AS `descripcion`, anuncios.notas_admin AS `notas_admin`, 
    anuncios.servicios AS `servicios`, anuncios.notas AS `notas`, anuncios.gps AS `gps`, eventos_destacar.fecha_destacar AS `fecha_destacar` FROM 
    (eventos_destacar join anuncios on(anuncios.`id` = eventos_destacar.id_anuncio)) WHERE anuncios.estado_destacado = 2;



DROP TABLE IF EXISTS `view_portadas`;

CREATE ALGORITHM=UNDEFINED DEFINER=cpses_ru0gl89b02@localhost SQL SECURITY DEFINER VIEW view_portadas  AS WITH t1 AS (SELECT horarios_anuncios.`id` AS `id`, horarios_anuncios.id_anuncio AS `id_anuncio`, horarios_anuncios.active AS `active`, horarios_anuncios.num_rango AS `num_rango`, horarios_anuncios.dia_inicio AS `dia_inicio`, horarios_anuncios.dia_fin AS `dia_fin`, horarios_anuncios.hora_inicio AS `hora_inicio`, horarios_anuncios.hora_fin AS `hora_fin`, timestampdiff(SECOND,current_timestamp(),horarios_anuncios.hora_inicio) AS `timestamp_inicio`, CASE WHEN horarios_anuncios.hora_fin < horarios_anuncios.hora_inicio THEN timestampdiff(SECOND,current_timestamp(),cast(current_timestamp() as date) + interval 1 day) + timestampdiff(SECOND,cast(cast(current_timestamp() as date) + interval 1 day as datetime),cast(current_timestamp() as date) + interval 1 day + interval time_to_sec(cast(horarios_anuncios.hora_fin as time)) second) ELSE timestampdiff(SECOND,current_timestamp(),horarios_anuncios.hora_fin) END AS `timestamp_fin`, CASE WHEN weekday(current_timestamp()) not between horarios_anuncios.dia_inicio and horarios_anuncios.dia_fin THEN CASE WHEN horarios_anuncios.dia_fin < horarios_anuncios.dia_inicio OR horarios_anuncios.dia_fin < weekday(current_timestamp()) OR horarios_anuncios.dia_inicio < weekday(current_timestamp()) THEN 6 - weekday(current_timestamp()) + horarios_anuncios.dia_fin + 1 ELSE horarios_anuncios.dia_fin- weekday(current_timestamp()) END ELSE 0 END AS `dia_fin_diff`, CASE WHEN weekday(current_timestamp()) not between horarios_anuncios.dia_inicio and horarios_anuncios.dia_fin THEN CASE WHEN horarios_anuncios.dia_inicio < weekday(current_timestamp()) THEN 6 - weekday(current_timestamp()) + horarios_anuncios.dia_inicio + 1 ELSE horarios_anuncios.dia_inicio- weekday(current_timestamp()) END ELSE 0 END AS `dia_inicio_diff` FROM horarios_anuncios), t2 AS (SELECT t1.`id` AS `id`, t1.id_anuncio AS `id_anuncio`, t1.active AS `active`, t1.num_rango AS `num_rango`, t1.dia_inicio AS `dia_inicio`, t1.dia_fin AS `dia_fin`, t1.hora_inicio AS `hora_inicio`, t1.hora_fin AS `hora_fin`, t1.timestamp_inicio AS `timestamp_inicio`, t1.timestamp_fin AS `timestamp_fin`, t1.dia_fin_diff AS `dia_fin_diff`, t1.dia_inicio_diff AS `dia_inicio_diff`, CASE WHEN t1.timestamp_inicio <= 0 AND t1.timestamp_fin >= 0 AND t1.dia_inicio - weekday(current_timestamp()) <= 0 AND t1.dia_fin - weekday(current_timestamp()) >= 0 THEN 1 ELSE 0 END AS `activo` FROM t1), t3 AS (SELECT t2.`id` AS `id`, t2.id_anuncio AS `id_anuncio`, t2.active AS `active`, t2.num_rango AS `num_rango`, t2.dia_inicio AS `dia_inicio`, t2.dia_fin AS `dia_fin`, t2.hora_inicio AS `hora_inicio`, t2.hora_fin AS `hora_fin`, t2.timestamp_inicio AS `timestamp_inicio`, t2.timestamp_fin AS `timestamp_fin`, t2.dia_fin_diff AS `dia_fin_diff`, t2.dia_inicio_diff AS `dia_inicio_diff`, t2.activo AS `activo`, row_number() over ( partition by t2.id_anuncio order by t2.activo desc,t2.dia_inicio,case t2.activo when 1 then t2.timestamp_fin else t2.timestamp_inicio end,case t2.activo when 1 then t2.timestamp_inicio else t2.timestamp_fin end) AS `rank` FROM t2) SELECT t3.dia_inicio AS `dia_inicio`, t3.dia_fin AS `dia_fin`, t3.dia_inicio_diff AS `dia_inicio_diff`, t3.dia_fin_diff AS `dia_fin_diff`, t3.hora_inicio AS `hora_inicio`, t3.hora_fin AS `hora_fin`, t3.timestamp_inicio AS `timestamp_inicio`, t3.timestamp_fin AS `timestamp_fin`, t3.activo AS `activo`, anuncios.`id` AS `id`, anuncios.id_usuario AS `id_usuario`, anuncios.casa AS `casa`, anuncios.id_categoria AS `id_categoria`, anuncios.id_ciudad AS `id_ciudad`, anuncios.id_pais AS `id_pais`, anuncios.titulo AS `titulo`, anuncios.estado_anuncio AS `estado_anuncio`, anuncios.estado_destacado AS `estado_destacado`, anuncios.fecha_creacion AS `fecha_creacion`, anuncios.fecha_actualizacion AS `fecha_actualizacion`, anuncios.fecha_caducidad AS `fecha_caducidad`, anuncios.fecha_publicacion AS `fecha_publicacion`, anuncios.fecha_posicionado AS `fecha_posicionado`, anuncios.fecha_posicionado_destacado AS `fecha_posicionado_destacado`, anuncios.salidas AS `salidas`, anuncios.nombre AS `nombre`, anuncios.edad AS `edad`, anuncios.pais AS `pais`, anuncios.portadas AS `portadas`, anuncios.portada_mensaje AS `portada_mensaje`, anuncios.destacado_tipo AS `destacado_tipo`, anuncios.hayVideo AS `hayVideo`, anuncios.bizum AS `bizum`, anuncios.visa AS `visa`, anuncios.telefono AS `telefono`, anuncios.telefono2 AS `telefono2`, anuncios.hayWhatsapp1 AS `hayWhatsapp1`, anuncios.hayWhatsapp2 AS `hayWhatsapp2`, anuncios.duracion AS `duracion`, anuncios.descripcion AS `descripcion`, anuncios.notas_admin AS `notas_admin`, anuncios.servicios AS `servicios`, anuncios.notas AS `notas`, anuncios.gps AS `gps` FROM (t3 join anuncios on(anuncios.`id` = t3.id_anuncio)) WHERE t3.rank = 11  ;


ALTER TABLE anuncios
  ADD PRIMARY KEY (id),
  ADD SPATIAL KEY gps (gps),
  ADD KEY id_usuario (id_usuario),
  ADD KEY id_categoria (id_categoria),
  ADD KEY id_ciudad (id_ciudad),
  ADD KEY id_pais (id_pais);

ALTER TABLE categorias
  ADD PRIMARY KEY (id);

ALTER TABLE ciudades
  ADD PRIMARY KEY (id);

ALTER TABLE enlaces_anuncios
  ADD PRIMARY KEY (id),
  ADD KEY anuncio1 (anuncio1),
  ADD KEY anuncio2 (anuncio2);

ALTER TABLE eventos
  ADD PRIMARY KEY (id),
  ADD KEY id_anuncio (id_anuncio);

ALTER TABLE fotos
  ADD PRIMARY KEY (id),
  ADD KEY id_anuncio (id_anuncio);

ALTER TABLE horarios_anuncios
  ADD PRIMARY KEY (id),
  ADD KEY id_anuncio (id_anuncio);

ALTER TABLE options
  ADD PRIMARY KEY (option_id);

ALTER TABLE paises
  ADD PRIMARY KEY (alpha_2);

ALTER TABLE roles
  ADD PRIMARY KEY (id);

ALTER TABLE usuarios
  ADD PRIMARY KEY (id),
  ADD KEY id_rol (id_rol);


ALTER TABLE anuncios
  MODIFY id int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE categorias
  MODIFY id tinyint(1) NOT NULL AUTO_INCREMENT;

ALTER TABLE ciudades
  MODIFY id int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE enlaces_anuncios
  MODIFY id int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE eventos
  MODIFY id int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE fotos
  MODIFY id int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE horarios_anuncios
  MODIFY id int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE options
  MODIFY option_id int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE roles
  MODIFY id int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE usuarios
  MODIFY id int(11) NOT NULL AUTO_INCREMENT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
