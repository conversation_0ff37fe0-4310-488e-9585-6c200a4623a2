-- SQLBook: Code

DROP DATABASE IF EXISTS `citas10-2022`;

CREATE DATABASE `citas10-2022`;

USE `citas10-2022`;

CREATE TABLE
    roles(
        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        nombre varchar(50)
    );

CREATE TABLE
    usuarios(
        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        id_rol INT(11),
        email varchar(50),
        pass varchar(50),
        activo tinyint(1),
        tel varchar(14),
        token varchar(80),
        index(id_rol),
        foreign key(id_rol) references roles(id)
    );

create table
    ciudades(
        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        nombre varchar(50)
    );

create table
    categorias(
        id tinyint(1) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        nombre varchar(50)
    );

CREATE TABLE
    paises (
        `alpha_2` char(2) PRIMARY KEY,
        `name` varchar(75) NOT NULL DEFAULT ''
    );

create table
    anuncios(
        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        id_usuario int(11) default 1,
        casa boolean default 0,
        id_categoria tinyint(1) default 1 NOT NULL,
        id_ciudad int(11) default 1 NOT NULL,
        id_pais char(2),
        titulo varchar(100) default '',
        estado_anuncio int(1) default 0,
        estado_destacado int(1) default 0,
        fecha_creacion timestamp default CURRENT_TIMESTAMP,
        fecha_actualizacion timestamp default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        fecha_caducidad timestamp default CURRENT_TIMESTAMP,
        fecha_publicacion timestamp default CURRENT_TIMESTAMP,
        fecha_posicionado timestamp default CURRENT_TIMESTAMP,
        fecha_posicionado_destacado timestamp default CURRENT_TIMESTAMP,
        salidas boolean default 0,
        nombre varchar(50) default '',
        edad int(2),
        pais varchar(50),
        portadas json,
        portada_mensaje varchar(30) default '',
        destacado_tipo int(1),
        hayVideo boolean default 0,
        bizum boolean default 0,
        visa boolean default 0,
        telefono varchar(50) default '',
        telefono2 varchar(50) default '',
        hayWhatsapp1 boolean default 0,
        hayWhatsapp2 boolean default 0,
        duracion int(11),
        descripcion varchar(5000) default '',
        notas_admin varchar(1000) default '',
        servicios varchar(200) default '', 
        notas JSON,
        gps point NOT NULL default POINT(
            -1.6458160117659297,
            42.81269525952281
        ),
        spatial index(gps),
        index(id_usuario),
        foreign key(id_usuario) references usuarios(id),
        index(id_categoria),
        foreign key(id_categoria) references categorias(id),
        index(id_ciudad),
        foreign key(id_ciudad) references ciudades(id),
        index(id_pais),
        foreign key (id_pais) references paises(alpha_2)
    );
 
create table
    enlaces_anuncios(
        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        anuncio1 INT(11),
        anuncio2 INT(11),
        index(anuncio1),
        foreign key(anuncio1) references anuncios(id) ON DELETE CASCADE,
        index(anuncio2),
        foreign key(anuncio2) references anuncios(id) ON DELETE CASCADE
    );

create table
    fotos(
        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        id_anuncio int(11),
        estado int(1) default 0,
        filename varchar(50),
        pos int(2) default 0,
        destacado boolean,
        index(id_anuncio),
        foreign key(id_anuncio) references anuncios(id) ON DELETE CASCADE
    );

create table
    eventos(
        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        id_anuncio int(11),
        fecha_creacion timestamp default CURRENT_TIMESTAMP,
        fecha_ejecucion timestamp default CURRENT_TIMESTAMP,
        tipo enum('destacado', 'anuncio'),
        accion enum('publicar', 'despublicar'),
        estado boolean,
        index(id_anuncio),
        foreign key(id_anuncio) references anuncios(id) ON DELETE CASCADE -- se borra anuncio se borran sus eventos
    );

create table
   horarios_anuncios(
        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        id_anuncio int(11),
        active int(1),
        num_rango int(1) default 0,
        dia_inicio int(1) default 0,
        dia_fin int(1) default 6,
        hora_inicio TIME default '00:00:00',
        hora_fin TIME default '23:59:59',
        index(id_anuncio),
        foreign key(id_anuncio) references anuncios(id) ON DELETE CASCADE
    );

-- ----------------------------------------------------------------------------------

-- INSERT into roles (nombre) VALUES ('root');

-- ----------------------------------------------------------------------------------

-- roles demo

INSERT into roles (id, nombre) VALUES (NULL, 'administrador');

INSERT into roles (id, nombre) VALUES (0, 'editor');

INSERT into roles VALUES (0, 'anunciante');

INSERT into roles (nombre) VALUES ('cliente');

-- ciudades demo

INSERT into ciudades VALUES (0, 'navarra');

INSERT into ciudades VALUES (0, 'madrid');

INSERT into ciudades VALUES (0, 'barcelona');

-- categorias demo

INSERT into categorias VALUES (1, 'Chica');

INSERT into categorias VALUES (2, 'Chico');

INSERT into categorias VALUES (3, 'Alojamiento');

INSERT into categorias VALUES (4, 'Transgénero');

-- usuarios demo

INSERT into
    usuarios (id_rol, email, pass, activo, tel)
VALUES (
        1,
        '<EMAIL>',
        '123',
        1,
        '*********'
    ), (
        1,
        '<EMAIL>',
        '456',
        1,
        '*********'
    ), (
        1,
        '<EMAIL>',
        '789',
        0,
        '*********'
    );

-- countries

-- DEFAULT CHARSET=utf8;

INSERT INTO
    paises (`alpha_2`, `name`)
VALUES ('af', 'Afganistán'), ('al', 'Albania'), ('de', 'Alemania'), ('ad', 'Andorra'), ('ao', 'Angola'), ('ag', 'Antigua y Barbuda'), ('sa', 'Arabia Saudita'), ('dz', 'Argelia'), ('ar', 'Argentina'), ('am', 'Armenia'), ('au', 'Australia'), ('at', 'Austria'), ('az', 'Azerbaiyán'), ('bs', 'Bahamas'), ('bd', 'Bangladés'), ('bb', 'Barbados'), ('bh', 'Baréin'), ('be', 'Bélgica'), ('bz', 'Belice'), ('bj', 'Benín'), ('by', 'Bielorrusia'), ('bo', 'Bolivia'), ('ba', 'Bosnia y Herzegovina'), ('bw', 'Botsuana'), ('br', 'Brasil'), ('bn', 'Brunéi'), ('bg', 'Bulgaria'), ('bf', 'Burkina Faso'), ('bi', 'Burundi'), ('bt', 'Bután'), ('cv', 'Cabo Verde'), ('kh', 'Camboya'), ('cm', 'Camerún'), ('ca', 'Canadá'), ('qa', 'Catar'), ('td', 'Chad'), ('cl', 'Chile'), ('cn', 'China'), ('cy', 'Chipre'), ('co', 'Colombia'), ('km', 'Comoras'), ('kp', 'Corea del Norte'), ('kr', 'Corea del Sur'), ('ci', 'Costa de Marfil'), ('cr', 'Costa Rica'), ('hr', 'Croacia'), ('cu', 'Cuba'), ('dk', 'Dinamarca'), ('dm', 'Dominica'), ('ec', 'Ecuador'), ('eg', 'Egipto'), ('sv', 'El Salvador'), (
        'ae',
        'Emiratos Árabes Unidos'
    ), ('er', 'Eritrea'), ('sk', 'Eslovaquia'), ('si', 'Eslovenia'), ('es', 'España'), ('us', 'Estados Unidos'), ('ee', 'Estonia'), ('et', 'Etiopía'), ('ph', 'Filipinas'), ('fi', 'Finlandia'), ('fj', 'Fiyi'), ('fr', 'Francia'), ('ga', 'Gabón'), ('gm', 'Gambia'), ('ge', 'Georgia'), ('gh', 'Ghana'), ('gd', 'Granada'), ('gr', 'Grecia'), ('gt', 'Guatemala'), ('gn', 'Guinea'), ('gw', 'Guinea-Bisáu'), ('gq', 'Guinea Ecuatorial'), ('gy', 'Guyana'), ('ht', 'Haití'), ('hn', 'Honduras'), ('hu', 'Hungría'), ('in', 'India'), ('id', 'Indonesia'), ('iq', 'Irak'), ('ir', 'Irán'), ('ie', 'Irlanda'), ('is', 'Islandia'), ('mh', 'Islas Marshall'), ('sb', 'Islas Salomón'), ('il', 'Israel'), ('it', 'Italia'), ('jm', 'Jamaica'), ('jp', 'Japón'), ('jo', 'Jordania'), ('kz', 'Kazajistán'), ('ke', 'Kenia'), ('kg', 'Kirguistán'), ('ki', 'Kiribati'), ('kw', 'Kuwait'), ('la', 'Laos'), ('ls', 'Lesoto'), ('lv', 'Letonia'), ('lb', 'Líbano'), ('lr', 'Liberia'), ('ly', 'Libia'), ('li', 'Liechtenstein'), ('lt', 'Lituania'), ('lu', 'Luxemburgo'), ('mk', 'Macedonia del Norte'), ('mg', 'Madagascar'), ('my', 'Malasia'), ('mw', 'Malaui'), ('mv', 'Maldivas'), ('ml', 'Malí'), ('mt', 'Malta'), ('ma', 'Marruecos'), ('mu', 'Mauricio'), ('mr', 'Mauritania'), ('mx', 'México'), ('fm', 'Micronesia'), ('md', 'Moldavia'), ('mc', 'Mónaco'), ('mn', 'Mongolia'), ('me', 'Montenegro'), ('mz', 'Mozambique'), ('mm', 'Birmania'), ('na', 'Namibia'), ('nr', 'Nauru'), ('np', 'Nepal'), ('ni', 'Nicaragua'), ('ne', 'Níger'), ('ng', 'Nigeria'), ('no', 'Noruega'), ('nz', 'Nueva Zelanda'), ('om', 'Omán'), ('nl', 'Países Bajos'), ('pk', 'Pakistán'), ('pw', 'Palaos'), ('pa', 'Panamá'), ('pg', 'Papúa Nueva Guinea'), ('py', 'Paraguay'), ('pe', 'Perú'), ('pl', 'Polonia'), ('pt', 'Portugal'), ('gb', 'Reino Unido'), (
        'cf',
        'República Centroafricana'
    ), ('cz', 'República Checa'), ('cg', 'República del Congo'), (
        'cd',
        'República Democrática del Congo'
    ), ('do', 'República Dominicana'), ('rw', 'Ruanda'), ('ro', 'Rumania'), ('ru', 'Rusia'), ('ws', 'Samoa'), (
        'kn',
        'San Cristóbal y Nieves'
    ), ('sm', 'San Marino'), (
        'vc',
        'San Vicente y las Granadinas'
    ), ('lc', 'Santa Lucía'), ('st', 'Santo Tomé y Príncipe'), ('sn', 'Senegal'), ('rs', 'Serbia'), ('sc', 'Seychelles'), ('sl', 'Sierra Leona'), ('sg', 'Singapur'), ('sy', 'Siria'), ('so', 'Somalia'), ('lk', 'Sri Lanka'), ('sz', 'Suazilandia'), ('za', 'Sudáfrica'), ('sd', 'Sudán'), ('ss', 'Sudán del Sur'), ('se', 'Suecia'), ('ch', 'Suiza'), ('sr', 'Surinam'), ('th', 'Tailandia'), ('tz', 'Tanzania'), ('tj', 'Tayikistán'), ('tl', 'Timor Oriental'), ('tg', 'Togo'), ('to', 'Tonga'), ('tt', 'Trinidad y Tobago'), ('tn', 'Túnez'), ('tm', 'Turkmenistán'), ('tr', 'Turquía'), ('tv', 'Tuvalu'), ('ua', 'Ucrania'), ('ug', 'Uganda'), ('uy', 'Uruguay'), ('uz', 'Uzbekistán'), ('vu', 'Vanuatu'), ('ve', 'Venezuela'), ('vn', 'Vietnam'), ('ye', 'Yemen'), ('dj', 'Yibuti'), ('zm', 'Zambia'), ('zw', 'Zimbabue');

CREATE VIEW IF NOT EXISTS view_portadas AS 
	WITH t1 AS(
	        SELECT
	            *,
	            TIMESTAMPDIFF(
	                SECOND,
	                CURRENT_TIMESTAMP(),
	                hora_inicio
	            ) timestamp_inicio,
	            CASE
	                WHEN hora_fin < hora_inicio THEN TIMESTAMPDIFF(
	                    SECOND,
	                    CURRENT_TIMESTAMP(), (
	                        DATE(CURRENT_TIMESTAMP()) + INTERVAL 1 DAY
	                    )
	                ) + TIMESTAMPDIFF(
	                    SECOND,
	                    TIMESTAMP( ( (
	                                DATE(CURRENT_TIMESTAMP()) + INTERVAL 1 DAY
	                            )
	                        )
	                    ), ( (
	                            DATE(CURRENT_TIMESTAMP()) + INTERVAL 1 DAY
	                        ) + INTERVAL TIME_TO_SEC(TIME(hora_fin)) SECOND
	                    )
	                )
	                ELSE TIMESTAMPDIFF(
	                    SECOND,
	                    CURRENT_TIMESTAMP(),
	                    hora_fin
	                )
	            END AS timestamp_fin,
	            CASE
	                WHEN WEEKDAY(now()) NOT BETWEEN dia_inicio AND dia_fin THEN CASE
	                    WHEN dia_fin < dia_inicio
	                    OR dia_fin < WEEKDAY(now())
	                    OR dia_inicio < WEEKDAY(now()) THEN (6 - WEEKDAY(now())) + dia_fin + 1
	                    ELSE dia_fin - WEEKDAY(now())
	                END
	                ELSE 0
	            END AS dia_fin_diff,
	            CASE
	                WHEN WEEKDAY(now()) NOT BETWEEN dia_inicio AND dia_fin THEN CASE
	                    WHEN dia_inicio < WEEKDAY(now()) THEN (6 - WEEKDAY(now())) + dia_inicio + 1
	                    ELSE dia_inicio - WEEKDAY(now())
	                END
	                ELSE 0
	            END AS dia_inicio_diff
	        FROM
	            horarios_anuncios
	    ),
	    t2 AS (
	        SELECT
	            *,
	            CASE
	                WHEN timestamp_inicio <= 0
	                AND timestamp_fin >= 0
	                AND dia_inicio - WEEKDAY(now()) <= 0
	                AND dia_fin - WEEKDAY(now()) >= 0 THEN 1
	                ELSE 0
	            END AS activo
	        FROM t1
	    ), t3 AS(
	        SELECT
	            *,
	            ROW_NUMBER() OVER (
	                PARTITION BY id_anuncio
	                ORDER BY
	                    t2.activo DESC,
	                    dia_inicio ASC,
	                    CASE t2.ACTIVO
	                        WHEN 1 THEN timestamp_fin
	                        ELSE timestamp_inicio
	                    END ASC,
	                    CASE t2.ACTIVO
	                        WHEN 1 THEN timestamp_inicio
	                        ELSE timestamp_fin
	                    END ASC
	            ) AS rank
	        FROM t2
	    )
	SELECT
	    t3.dia_inicio,
	    t3.dia_fin,
	    t3.dia_inicio_diff,
	    t3.dia_fin_diff,
	    t3.hora_inicio,
	    t3.hora_fin,
	    t3.timestamp_inicio,
	    t3.timestamp_fin,
	    t3.activo,
	    anuncios.*
	FROM t3
	    INNER JOIN anuncios ON anuncios.id = t3.id_anuncio
	WHERE rank = 1;

CREATE VIEW admin_destacados_programados AS 
	WITH eventos_destacar AS(
	        SELECT
	            id_anuncio,
	            fecha_ejecucion as fecha_destacar
	        FROM eventos
	        WHERE
	            tipo = 'destacado'
	            AND accion = 'publicar'
	    )
	SELECT
	    anuncios.*,
	    eventos_destacar.fecha_destacar
	FROM eventos_destacar
	    INNER JOIN anuncios ON anuncios.id = eventos_destacar.id_anuncio
	WHERE
	    anuncios.estado_destacado = 2;