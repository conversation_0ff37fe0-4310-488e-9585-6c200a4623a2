/* external css: flickity.css */
* {
  box-sizing: border-box;
}

body {
  font-family: sans-serif;
}

.carousel {
  background: black;
}

.carousel-cell {
  width: 90%;
  height: 510px;
  /* flex-box, center image in cell */
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  align-items: center;
}

.carousel-cell img {
  display: block;
  max-width: 100%;
  max-height: 100%;
  /* dim unselected */
  opacity: 0.7;
  -webkit-transform: scale(0.85);
  transform: scale(0.85);
  /*   -webkit-filter: blur(5px);
            filter: blur(5px); */
  -webkit-transition: opacity 0.3s, -webkit-transform 0.3s, transform 0.3s, -webkit-filter 0.3s, filter 0.3s;
  transition: opacity 0.3s, transform 0.3s, filter 0.3s;
}

/* brighten selected image */
.carousel-cell.is-selected img {
  opacity: 1;
  -webkit-transform: scale(1);
  transform: scale(1);
  -webkit-filter: none;
  filter: none;
}

@media screen and (max-width: 768px) {
  ol.flickity-page-dots {
    bottom: -25px;
    margin-bottom: 10px;
  }
}
/* buttons, no circle */
.flickity-prev-next-button {
  width: 60px;
  height: 60px;
  background: transparent;
  opacity: 0.6;
}

.flickity-prev-next-button:hover {
  background: transparent;
  opacity: 1;
}

/* arrow color */
.flickity-prev-next-button .arrow {
  fill: white;
}

.flickity-prev-next-button.no-svg {
  color: white;
}

/* closer to edge */
.flickity-prev-next-button.previous {
  left: 0;
}

.flickity-prev-next-button.next {
  right: 0;
}

/* hide disabled button */
.flickity-prev-next-button:disabled {
  display: none;
}

/* mods */
.flickity-prev-next-button .arrow {
  fill: white;
}

button.flickity-button.flickity-prev-next-button {
  background: red;
  border: solid 2px white;
  width: 40px;
  height: 40px;
}

img {
  border: solid 2px red;
}

body {
  background: black;
}

div.carousel.js-flickity.flickity-enabled.is-draggable > ol > li {
  color: rgb(109, 3, 3);
}

div.carousel.js-flickity.flickity-enabled.is-draggable > ol.flickity-page-dots .dot {
  background: rgb(250, 8, 8);
}
 