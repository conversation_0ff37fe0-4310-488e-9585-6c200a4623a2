#loading {
    display: inline-block;
    width: 50px;
    height: 50px;
    border: 6px solid rgba(241, 120, 7, 0.3);
    border-radius: 50%;
    border-top-color: #f2faa6;
    animation: spin 1s ease-in-out infinite;
    -webkit-animation: spin 1s ease-in-out infinite;
    scale: .5;
  }
  
  @keyframes spin {
    to { -webkit-transform: rotate(360deg); }
  }
  @-webkit-keyframes spin {
    to { -webkit-transform: rotate(360deg); }
  }