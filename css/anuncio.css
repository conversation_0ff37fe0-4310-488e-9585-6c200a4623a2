@charset "UTF-8";
* {
  box-sizing: border-box;
}

/* LAYOUT */
body {
  background: black;
  color: white;
  font-size: 14px;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
  line-height: 1.15;
}

.texto {
  padding: 10px;
}

nav .contenido h4.logo {
  font-size: 1.2rem;
  margin: 0px !important;
  margin-bottom: 0px !important;
  color: white;
}

nav {
  background-color: rgb(168, 8, 8);
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
  display: flex;
  height: 50px;
  margin-bottom: 60px;
  padding: 0.5rem 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
}

nav .contenido {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 98%;
  max-width: 1400px;
  text-align: center;
}

nav .contenido a {
  text-decoration: none;
  color: white;
}

nav .contenido .nav-nombre {
  font-size: 1.6rem;
  margin-left: 5px;
  font-family: "Black Ops One", cursive;
}

nav .contenido h4 {
  /* font-family: 'Merienda', cursive; */
  /* font-family: 'Monoton', cursive; */
  font-family: "Quantico", sans-serif;
  font-size: 1.4rem;
}

nav i {
  padding: 13px;
  border-radius: 25px;
}

ul {
  padding-inline-start: 5px;
}

nav h4 {
  font-size: 1rem;
}

nav .boton {
  width: 50px;
}

h1, h2, h3 {
  text-align: center;
  background: linear-gradient(to bottom, #da0505 0%, #920202 100%);
  color: white;
}

p {
  padding: 5px 10px;
  margin: 5px 5px;
}

h3 {
  line-height: 1.5rem;
  border-top: solid 1px white;
  border-bottom: 4px solid #5f0303;
  font-size: 1.2rem;
  font-weight: bold;
}

h4 {
  font-size: 1.4rem;
}

.principal {
  max-width: 1400px;
  margin: 20px auto;
  display: flex;
  flex-direction: column;
  margin-top: 70px;
}

/****************************** VIDEO MOVIL O SIN DEFINIR  *********/
#cajavideo {
  margin-top: 80px;
  display: flex;
  justify-content: center;
}

#cajavideo video {
  max-width: 90%;
  max-height: 60vh;
  border: solid 2px #910303;
}

/****************************** GALERIA  MOVIL O SIN DEFINIR  *********/
.galeria {
  max-width: 100%;
  padding: 5px;
}

.galeria h3 {
  margin: 0;
}

/* DATOS */
.datos1, .datos2 {
  margin-right: 10px;
  padding-right: 10px;
}

.informacion {
  margin-top: 30px;
}

.datos p {
  border-bottom: solid 1px #910303;
}

.telefonos {
  display: flex;
  justify-content: flex-start;
  flex-wrap: nowrap;
  padding-left: 10px;
}

a#botontel, a#botontel2, a#compartir-whatsapp {
  color: white;
  text-decoration: none;
}

#botontel p, #botontel2 p, a#compartir-whatsapp p {
  background-color: green;
  border-radius: 5px;
  border: none;
  border: 1px solid white;
  font-weight: bold;
  font-size: 1rem;
  padding: 3px 5px;
}

/* HORARIO EN MOVIL */
.horario {
  display: flex;
  justify-content: flex-start;
}

.lv, .sabados, .domingos {
  flex: 1;
  text-align: center;
}

.lv h3, .sabados h3, .domingos h3 {
  padding: 0px 10px;
  line-height: 1.3rem;
  font-size: 1rem;
  margin-left: 2px;
  margin-top: 5px;
  background: linear-gradient(to bottom, #464343 0%, #222121 100%);
  border-bottom: solid 3px rgb(48, 47, 47);
}

/* SALIDAS */
/* SERVICIOS */
.servicios {
  flex-wrap: wrap;
  padding: 5px;
  text-align: center;
}

.servicios ul {
  display: flex;
  flex-wrap: wrap;
  padding: 6px;
}

.servicios ul li {
  list-style-type: none;
  display: inline;
  background-color: #a30707;
  box-shadow: 0px 3px 3px rgb(243, 108, 108);
  margin: 5px;
  padding: 3px 10px;
  border-radius: 5px;
  font-size: 0.9rem;
}

/* adicional */
.adicional {
  padding: 10px;
  padding-top: 20px;
  display: flex;
  flex-direction: column;
}

.adicional h3 {
  line-height: 1.3rem;
  font-size: 1rem;
  margin-top: 5px;
  background: linear-gradient(to bottom, #464343 0%, #222121 100%);
  border-bottom: solid 3px rgb(48, 47, 47);
  border-radius: 5px;
}

.adicional ul li {
  list-style-type: none;
  text-align: left;
  margin: 15px;
}

/* whatsapp boton y banner */
img.wp {
  top: 3px;
  left: 3px;
  margin-right: 10px;
}

.popover-body {
  padding: 0.2rem 0.5rem;
  color: white;
  font-weight: 500;
  background: red;
  font-size: 0.8rem;
}

.popover {
  border-radius: 3px;
  border: solid 1px white;
}

/**************************************************************************************
                            MEDIA QUERIES PARA VERSIÃ“N ESCRITORIO 
 ************************************************************************************/
@media (min-width: 968px) {
  h3 {
    /* border-radius: 0px 15px 0px 0px; */
    border-bottom: 3px solid #910303;
    display: inline;
    padding: 2px 30px;
    border-radius: 5px 5px 0 0;
    font-size: 1rem;
  }
  .lv h3, .sabados h3, .domingos h3 {
    border-radius: 5px;
  }
  /* galeria*/
  .galeria {
    position: relative;
    height: 580px;
    flex: 0 0 40%;
    padding: 0px;
    top: 53px;
  }
  .galeria h3 {
    position: relative;
    top: -29px;
    left: -3px;
  }
  .informacion {
    flex: 0 0 60%;
    margin: 10px;
    margin-top: 30px;
  }
  .principal {
    flex-direction: row;
    padding: 15px;
    margin: 60px auto;
  }
  .galeria, .datos, .horario, .servicios, .adicional, .texto, .relacionados {
    border: solid 3px #910303;
    margin-bottom: 10px;
  }
  h3.video {
    display: none;
  }
  #cajavideo video {
    max-width: 480px;
    display: flex;
    justify-content: center;
    flex: 1;
    max-height: 50vh;
    border: solid 3px #910303;
  }
  /* DATOS */
  .datos {
    display: flex;
    padding-top: 0;
    margin-bottom: 15px;
  }
  .datos1 {
    flex: 1 0 40%;
  }
  .datos2 {
    flex: 1 0 60%;
  }
  .telefonos {
    display: flex;
    justify-content: space-around;
    max-width: 380px;
  }
  .tele1, .tele2 {
    display: flex;
  }
  /* HORARIO */
  .lv h3, .sabados h3, .domingos h3 {
    display: inline;
    padding: 2px 20px;
    font-size: 0.9rem;
  }
  .horario {
    margin-bottom: 15px;
    padding-top: 20px;
  }
  /* SERVICIOS */
  .servicios {
    margin-top: 0;
    padding-top: 0;
    margin-bottom: 10px;
  }
  .servicios p {
    margin-top: 0;
    padding-top: 15px;
    margin-bottom: 0px;
  }
  /* ADICIONAL */
  .notas {
    flex: 3;
  }
  .salidas {
    flex: 2;
  }
  .adicional h3 {
    border-radius: 5px;
    display: block;
    max-width: 90%;
    text-align: center;
    margin-left: 15px;
  }
  .adicional {
    flex-direction: row;
    padding-top: 20px;
  }
  /* TEXTO */
  .texto {
    margin-top: 0;
    padding-top: 0;
    margin-bottom: 10px;
  }
}
/* MEDIA QUERIES PARA VERSIÃ“N MOVIL */
@media (max-width: 780px) {
  .informacion {
    margin-top: 20px;
  }
}
@media (max-width: 780px) and (max-width: 425px) {
  nav .contenido h4.logo {
    font-size: 1rem;
  }
  nav .contenido span.nav-nombre {
    font-size: 0.9rem;
  }
}
@media (max-width: 780px) {
  h3 {
    font-size: 1rem;
  }
}
@media (max-width: 780px) {
  #botontel2 {
    text-align: center;
  }
}
@media (max-width: 780px) {
  #botontel p, #botontel2 p {
    font-size: 0.9rem;
  }
}
@media (max-width: 780px) {
  .telefonos {
    justify-content: flex-start;
    flex-wrap: nowrap;
    border-bottom: solid 1px #910303;
    min-width: 98%;
    margin: 0 auto;
  }
}
@media (max-width: 780px) {
  .telefonos p {
    margin: 0 0 auto;
  }
}
@media (max-width: 780px) {
  img.wp {
    top: -2px;
    left: 3px;
  }
}
@media (max-width: 780px) {
  .telefonos {
    width: 300px;
  }
}
@media (max-width: 780px) {
  .datos {
    margin-bottom: 50px;
  }
}
 