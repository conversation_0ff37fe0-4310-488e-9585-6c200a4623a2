aside {
  width: 285px;
}

.anuncios {
  padding-bottom: 50px;
}

.admin .metabox {
  box-shadow: 2px 2px 4px rgb(204, 204, 204);
  border: solid 1px rgb(163, 162, 162);
}

.main.admin {
  background-color: white; 
}
 
.admin h1, h2, h3, h4, h5 {
  color: black;
}

#swal2-html-container {
  padding-top: 10px;
}

.pac-container {
  z-index: 99999 !important;
}

h2 {
  color: #ffffff;
}

.contenedor {
  margin-top: 20px;
}

/* formAnuncio */
.form-check-input:checked {
  background-color: #198754;
  border-color: #198754;
}

.form-check-input:focus {
  border-color: #007c00;
}

.form-check-input:active, .form-check-input:focus {
  border-color: #007c00;
}

.form-check-input:focus {
  box-shadow: 0 0 0 0.25rem rgba(24, 173, 4, 0.25);
}

.form-switch .form-check-input:focus {
  background-image: url("/files/checkbox.svg");
}

.form-switch .form-check-input {
  transform: scale(1.8);
  margin-right: -2px;
  margin-left: 3px;
  float: none;
  min-width: 2rem;
  margin-top: 0;
}

.form-switch {
  display: flex;
  align-items: center;
  font-size: 1.1rem;
  padding: 10px 10px;
}

/* Footer */
.footer {
  height: 35px;
  background-color: #7e0000;
  color: white;
  position: fixed;
  bottom: 0px;
  width: 100%;
  border-top: 1px solid rgba(255, 255, 255, 0.8);
}

.footer ul {
  list-style-type: none;
  line-height: 2rem;
  min-width: 300px;
  display: flex;
  justify-content: center;
  padding-left: 0;
}

.footer ul li a {
  text-decoration: none;
  color: white;
}

.footer ul li {
  padding: 0 10px;
  text-align: center;
}

.footer ul li:first-child {
  border-left: 1px solid rgba(255, 255, 255, 0.6);
}

.footer ul li:first-child {
  border-right: 1px solid rgba(255, 255, 255, 0.6);
}

.footer ul li:last-child {
  border-right: 1px solid rgba(255, 255, 255, 0.6);
}

hr {
  margin: 0;
}

/*# sourceMappingURL=main.css.map */
