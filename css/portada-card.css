div {
  box-sizing: border-box;
}

.destacado {
  box-shadow: 0px 0px 6px 1px #fff630;
}

.interno {
  min-height: 355px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  width: 100%;
  align-items: center;
  position: absolute;
  top: 42px;
}

.stars {
  width: 60px;
  position: relative;
  top: 2px;
}

.destacado .cajanegra {
  margin-bottom: 20px;
}

.normal .tags {
  padding-bottom: 20px;
}

.padre-portada {
  position: relative;
}

.portada-contenedor {
  height: 515px;
  position: relative;
  border: 6px solid #7e0000;
  width: 354px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  margin: 0 auto;
}
.portada-contenedor .titulo {
  background-color: #7e0000;
  color: white;
  display: flex;
  justify-content: space-between;
  line-height: 1.5em;
  width: 100%;
  padding: 0px 10px;
  height: 35px;
  border: 3px solid #7e0000;
  position: relative;
  font-size: 1rem;
  top: -5px;
}
.portada-contenedor .titulo .nombre {
  font-size: 1.1rem;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-transform: uppercase;
  font-family: "Courier New", Courier, monospace;
  line-height: 1.1rem;
  text-align: center;
}
.portada-contenedor .imgbox {
  height: fit-content;
  display: flex;
  justify-content: space-between;
  position: relative;
  align-items: center;
  overflow: hidden;
}
.portada-contenedor .foto {
  max-width: 330px;
  max-height: 330px;
  border: 3px solid #7e0000;
  margin-top: 0px;
}
.portada-contenedor .bandera {
  position: absolute;
  align-self: flex-end;
  width: 25px;
  height: 25px;
  right: 8px;
  bottom: 8px;
}
.portada-contenedor .disponibilidad {
  position: absolute;
  align-self: flex-end;
  left: 3px;
  bottom: 8px;
  font-size: 0.85em;
  background-color: rgb(0, 128, 0);
  color: white;
  padding: 3px 10px;
}
.portada-contenedor .nodisponible {
  background-color: red;
}
.portada-contenedor .video {
  position: absolute;
  display: inline-block;
  align-self: flex-start;
  right: 0px;
  display: block;
  background-color: #7e0000;
  color: white;
  margin: 10px 0px;
  font-size: 1em;
  padding: 1px 10px;
  font-size: 0.85rem;
  line-height: 1.8em;
  top: 8px;
}
.portada-contenedor .video .value {
  padding: 0 0 0 10px;
}
.portada-contenedor .msg-dest {
  color: rgba(255, 255, 0, 0.7);
  background-color: #7e0000;
  width: 100%;
  text-align: center;
  position: relative;
  line-height: 1.7rem;
}
.portada-contenedor .msg-dest .texto {
  display: block;
  position: relative;
  font-weight: lighter;
}
.portada-contenedor .msg-normal {
  height: 22px;
}
.portada-contenedor .destacado {
  box-shadow: inset 0 0 1px 1px rgb(172, 172, 2);
}

.llamar {
  position: absolute;
  bottom: -10px;
  right: 5%;
}

.llamar img {
  width: 35px;
}

@media (min-width: 900px) {
  .llamar {
    right: 11%;
  }
  .llamar img {
    width: 50px;
  }
}
@media (min-width: 1200px) {
  .llamar {
    right: 2%;
  }
}
.sello {
  position: absolute;
  z-index: 10;
  left: -54px;
  top: 20px;
  width: 95px;
}

.sello img {
  max-width: 100%;
}

.tags {
  --bs-gutter-x: 15px;
}
.tags .a > span,
.tags .b > span {
  display: block;
  background-color: #7e0000;
  color: white;
  border-radius: 0px;
  margin: 5px 2px;
  font-size: 1em;
  padding: 2px 6px;
  line-height: 1.8em;
}
.tags i {
  padding: 2px 3px;
}
.tags .salida {
  background-color: white;
  display: static;
  color: black;
  padding: 0px 4px;
  line-height: 1em;
  font-weight: bold;
  border-radius: 3px;
}
.tags .row {
  --bs-gutter-x: 0;
}

.animacion_izquierda {
  white-space: nowrap;
  overflow: hidden;
  margin: 0;
  /*   animation: parpadeo 2s linear 0s infinite;
   */
}

.animacion_derecha {
  white-space: nowrap;
  overflow: hidden;
  width: 80px;
  margin: 0;
  animation: derecha 3s linear 0s infinite;
}

@keyframes izquierda {
  40% {
    width: 160px;
    text-indent: 0px;
  }
  50% {
    width: 30px;
    text-indent: -122px;
  }
  90% {
    width: 30px;
    text-indent: -122px;
  }
}
@keyframes derecha {
  40% {
    width: 80px;
  }
  60% {
    width: 30px;
  }
  90% {
    width: 30px;
  }
}
@keyframes parpadeo {
  40% {
    opacity: 1;
  }
  49% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  90% {
    opacity: 0;
  }
  99% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@media (min-width: 770px) {
  .contenedor > * {
    margin-bottom: 40px;
  }
}
:root {
  --vw: 100vw;
  --zoom: calc( var(--vw) / 930);
  --fix: 0.8;
}

@media (max-width: 770px) {
  .contenedor > * {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 470px;
  }
  .portada-contenedor {
    transform: scale(0.9);
    margin-top: 1rem;
  }
}
@media (max-width: 670px) {
  .portada-contenedor {
    transform: scale(0.77);
  }
  .contenedor > * {
    height: 400px;
  }
}
@media (max-width: 570px) {
  .portada-contenedor {
    transform: scale(0.63);
  }
  .contenedor > * {
    height: 335px;
  }
}
@media (max-width: 470px) {
  .portada-contenedor {
    transform: scale(0.54);
  }
  .contenedor > * {
    height: 305px;
  }
}
@media (max-width: 410px) {
  .portada-contenedor {
    transform: scale(0.4);
  }
  .contenedor > * {
    height: 225px;
  }
}
 