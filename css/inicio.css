body {
  background-color: black;
}

main.public {
  padding-top: 50px;
}

@media (min-width: 769px) {
  main.public {
    padding-top: 110px;
  }
}
.public h1, h2, h3, h4, h5 {
  color: white;
}

/* SOLO MOVIL */
@media (min-width: 769px) {
  .navbtn {
    border-radius: 6px;
  }
  .navbar-nav > .anunciarse-btn {
    margin-left: 8px;
  }
  .navbar-nav .active {
    line-height: 2rem;
  }
  .pc {
    display: inline-block !important;
  }
}
@media (max-width: 1200px) {
  nav {
    border-bottom: solid 1px white;
  }
  .navbar-dark .navbar-nav .nav-link {
    box-shadow: none !important;
  }
  .navbtn {
    border: none !important;
    border-radius: none !important;
    text-align: left;
  }
  .navbtn:hover {
    background: none;
    color: white !important;
  }
  .navbar-dark .navbar-toggler {
    border: none !important;
  }
  .navbar-nav > form > .btn {
    border: none !important;
  }
  .navbar-nav > li {
    padding-bottom: 5px;
    padding-top: 5px;
  }
  .navbar-nav > li,
  .navbar-nav > span {
    padding-left: 20px;
  }
  .navbar-nav > span {
    padding-bottom: 14px;
    padding-top: 12px;
    border-radius: 0 !important;
  }
  .dropdown-menu {
    background: none !important;
    border: none !important;
    border-radius: none !important;
  }
  .dropdown-item {
    color: white !important;
  }
  .dropdown-item:hover {
    background: none;
  }
  .anunciarse-btn button {
    display: block;
    width: 100%;
    margin: 0 auto;
  }
}
/* ----------- navbar ------------------- */
nav {
  background-color: #7e0000;
  position: fixed !important;
  width: 100vw !important;
  z-index: 10;
  border-bottom: 1px solid rgba(255, 255, 255, 0.8);
}

.navbar-nav > * {
  color: white;
}

.navbar-dark .navbar-nav .nav-link {
  color: white;
}

.navbar-nav .active {
  color: yellow;
}

.navbar-dark .navbar-toggler {
  color: white;
}

.navbar-toggler:focus {
  box-shadow: none;
}

nav .fa-xmark {
  position: relative;
  font-size: 1rem;
  line-height: 2rem;
  padding: 4px 10px;
  left: -40px;
  top: 0px;
}

.dropdown-menu {
  background-color: #7e0000;
}

.dropdown-item {
  color: white;
}

.nav-item:first-child .dropdown-toggle::after {
  color: yellow;
}

.active .dropdown-toggle::after {
  color: yellow;
}

/*# sourceMappingURL=inicio.css.map */
