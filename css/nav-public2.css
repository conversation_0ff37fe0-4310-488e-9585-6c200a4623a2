.nav {
  background-color: #7e0000;
  height: 60px;
  position: fixed;
  top: 0px;
  z-index: 20;
  font-size: 0.8rem;
  border-bottom: solid 1px rgba(255, 255, 255, 0.706);
  transform: scale(1);
  transform-origin: top;
  width: 100%;
}

.main .container-lg {
  padding-top: 65px;
}

.mov-max {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  margin: 0 auto;
  width: 100%;
}

.botones {
  height: 100%;
  justify-content: space-around;
  align-items: center;
  width: 110px;
}

.botones .box {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 55px;
}

.botones .box img {
  width: 30px;
  max-height: 40px;
}

.botones .box {
  border-left: 1px solid black;
}

.nav-max {
  width: 835px;
  display: none;
  height: 70px;
  justify-content: space-between;
  background: #7e0000;
  margin: 0 auto;
}

.interface {
  color: white;
  display: flex;
  cursor: pointer;
  justify-content: space-between;
}

.logo {
  color: #FFD347;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  border-right: 1px solid black;
  height: 100%;
  padding-right: 5px;
}

.logo span {
  font-size: 1rem;
  line-height: 1rem;
  font-weight: lighter;
}

.interface img.dropdown-svg {
  width: 1.1rem;
}

.ubicacion {
  display: flex;
  flex-grow: 1;
  height: 100%;
  align-items: center;
  justify-content: center;
}

.ubicacion .valor {
  justify-content: center;
  align-items: center;
  margin: 0;
  height: calc(100% - 15px);
  width: calc(100% - 10px);
  color: #7e0000;
  padding: 0px !important;
}

#direccionStrPc {
  margin-left: 5px;
  margin-right: 5px;
}

#direccionStrMovil {
  margin-left: 5px;
  margin-right: 26px;
}

.ubicacion .box img {
  display: inline;
  margin-right: 3px;
  margin-left: 2px;
}

.ubicacion .dropdown-locale {
  position: absolute;
  top: 17px;
  right: 2px;
}

.ubicacion .valor:hover {
  color: #7e0000 !important;
}

.ubicacion .box {
  background-color: white;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: start;
  border-radius: 4px;
  border: solid outset 2px #520101;
  font-size: 0.7rem;
  position: relative;
  line-height: 0.7rem;
}

/*
.ubicacion input {
  display: block; 
  margin: 0 auto; 
  height: 24px;
  border-radius: 0;
  font-size: 0.7rem;
  color: #7e0000;
}

 */
.interface img {
  width: 1.4rem;
  padding: 2px;
}

.valor .icon {
  margin-left: 2px;
  padding-bottom: 4px;
  max-height: 2.1rem;
}

.interface .valor {
  margin-top: 3px;
}

.interface > * {
  line-height: 2rem;
  border-right: 1px solid black;
}

.interface > * > *:last-child {
  padding-right: 5px;
}

.desc img,
.desc img {
  margin-left: 2px;
  margin-bottom: 1px;
}

.interface .valor:hover {
  color: #ffd347;
}

.interface .active {
  color: #ffd347;
}

.interface .active img.icon,
.interface .active img.dropdown-svg {
  filter: invert(42%) sepia(93%) saturate(1352%) hue-rotate(12deg) brightness(119%) contrast(119%);
}

.interface .valor:hover img.icon,
.interface .valor:hover img.dropdown-svg {
  filter: invert(42%) sepia(93%) saturate(1352%) hue-rotate(12deg) brightness(119%) contrast(119%);
}

.orden {
  min-width: 125px;
}

.interface {
  width: 100%;
}

.filtros .filtro {
  width: 100px;
}

.interface .desc {
  border-bottom: solid 1px black;
}

.interface .ubicacion .desc {
  border: none;
}

.filtros .desc {
  display: flex;
  width: 100%;
}

.filtros .filtro img {
  margin-left: 3px;
}

.filtros .buscar {
  border-left: 1px solid black;
  width: 35px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.filtros .buscar img {
  display: block;
  width: 1.5rem;
  margin: 0 auto;
}

#modalFiltro .modal-content {
  border-radius: 0;
  background-color: black;
  color: white;
  border: solid 1px rgba(255, 255, 255, 0.555);
}

.modal-title {
  color: white;
}

#modalFiltro .modal-body {
  background-color: #7e0000;
  padding-left: 0;
  padding-bottom: 0;
  padding-right: 0;
  padding-top: 10px;
}

#modalFiltro .modal-header h5 {
  font-weight: normal;
  font-size: 1.3rem;
  margin-left: 8px;
}

#modalFiltro .modal-header .icon {
  margin-right: 11px;
}

#modalFiltro .btn-close {
  background: none;
  opacity: 1;
  height: auto;
  padding: 10px 0px;
  padding-left: 30px;
}

#modalFiltro .btn-close img {
  width: 1.3rem;
  position: relative;
  left: -15px;
}

#modalFiltro .icon {
  margin-right: 7px;
  width: 1.7rem;
}

#modalFiltro .modal-body ul {
  list-style-type: none;
  padding-left: 0;
  padding-bottom: 0;
  margin-block-end: 0;
}

#modalFiltro .modal-body a {
  color: white;
  text-decoration: none;
}

#modalFiltro .modal-body li {
  line-height: 3rem;
}

#modalFiltro form {
  display: flex;
  min-width: 80px;
  position: relative;
  align-items: center;
}

#modalFiltro form a {
  background-color: none !important;
  position: absolute;
  right: 110px;
  color: grey;
  border: none;
}

#modalFiltro form button {
  width: 100px;
  color: rgba(255, 255, 255, 0.9);
  padding: 5px;
  border: solid 1px;
  line-height: 1.8rem;
  margin-left: 5px;
  margin-right: 5px;
}

#modalFiltro form {
  margin-top: 15px;
  margin-bottom: 18px;
}

.menu-icon {
  display: inline-block;
  padding-left: 20px;
  padding-right: 10px;
  width: 60px;
}

#modalFiltro form .menu-icon .icon {
  width: 24px;
  margin-left: 5px;
}

#modalFiltro .modal-body li {
  border-top: 1px solid rgba(0, 0, 0, 0.445);
  padding-left: 10px;
  font-size: 1.2rem;
}

#modalFiltro .modal-body li:hover, #modalFiltro .modal-body ul a:hover {
  background-color: rgba(0, 0, 0, 0.842);
}

.menu-icon img {
  height: 34px;
  margin-bottom: 2px;
  max-width: 26px;
}

#modalFiltro li:hover,
#modalFiltro li.active,
#modalFiltro button:hover {
  color: #ffd347;
}

#modalFiltro li.active {
  background-color: rgb(90, 0, 0);
}

#modalFiltro .especial {
  background-color: black;
}

#modalFiltro li:hover img,
#modalFiltro li.active img, .active img {
  filter: invert(42%) sepia(93%) saturate(1352%) hue-rotate(12deg) brightness(119%) contrast(119%);
}

.ciudad-dropdown .ciudad {
  color: #ffd347;
}

/* ORDENAR POR MODAL */
#modalOrden .modal-content, #modalModoGeo .modal-content, #modalDireccion .modal-content {
  border-radius: 0 !important;
  border: 1px solid white;
}

#modalOrden h5 {
  font-weight: normal;
  font-size: 1.3rem;
  margin-left: 8px;
}

#modalOrden .modal-header, #modalModoGeo .modal-header, #modalDireccion .modal-header {
  background-color: black;
  color: white;
  border-radius: 0 !important;
}

#modalModoGeo .modal-content p, #modalDireccion .modal-content p {
  color: white;
}

#modalOrden .modal-body, #modalModoGeo .modal-body, #modalDireccion .modal-body {
  background-color: #7e0000;
  padding-left: 0;
  padding-bottom: 0;
  padding-right: 0;
  padding-top: 0px;
}

#modalOrden .btn-close, #modalModoGeo .btn-close, #modalDireccion .btn-close {
  background: none;
  opacity: 1;
  height: auto;
  padding: 10px 0px;
  padding-left: 30px;
}

#modalOrden .btn-close img, #modalModoGeo .btn-close img, #modalDireccion .btn-close img {
  width: 1.3rem;
  position: relative;
  left: -15px;
}

#modalOrden .icon {
  margin-right: 7px;
  width: 1.7rem;
}

#modalOrden .modal-body ul {
  list-style-type: none;
  padding-left: 0;
  padding-bottom: 0;
  margin-block-end: 0;
}

#modalOrden .modal-body a {
  color: white;
  text-decoration: none;
}

#modalOrden .modal-body li {
  border-bottom: 1px solid rgba(0, 0, 0, 0.445);
  padding-left: 10px;
  font-size: 1.2rem;
  line-height: 3rem;
}

#modalOrden .modal-body li:hover, #modalOrden .modal-body a:hover {
  background-color: rgba(0, 0, 0, 0.842);
}

#modalOrden li:hover img, #modalOrden li.active img, .active img {
  filter: invert(42%) sepia(93%) saturate(1352%) hue-rotate(12deg) brightness(119%) contrast(119%);
}

#modalOrden li:hover, #modalFiltro li.active, #modalOrden button:hover {
  color: #ffd347;
}

#modalOrden li:hover, #modalOrden li.active, #modalOrden button:hover {
  color: #ffd347;
}

#modalOrden li.active {
  background-color: #5a0000;
}

#modalOrden .especial {
  background-color: black;
}

.mov-max .botones .box img.active {
  filter: invert(42%) sepia(93%) saturate(1352%) hue-rotate(12deg) brightness(119%) contrast(119%) !important;
}

/* Modal modogeo */
#modalModoGeo .modal-body {
  padding-top: 15px;
  padding-bottom: 15px;
}

/* ------------------------- pc  ----------------------------------- */
@media (min-width: 768px) {
  .nav {
    height: 72px;
  }
  .nav-max {
    display: flex;
  }
  .mov-max {
    display: none;
  }
  #modalFiltro form button {
    margin-left: 15px;
    margin-right: 15px;
  }
  .ubicacion {
    min-width: 30%;
  }
  .ubicacion .box img {
    width: 25px;
  }
  .ubicacion .valor {
    height: calc(100% - 20px);
  }
  .ubicacion .valor .box {
    font-size: 1rem;
    line-height: 1.2rem;
    padding-right: 40px;
  }
  .logo span {
    padding-left: 5px;
    font-size: 1.8rem;
    line-height: 1.9rem;
    font-weight: lighter;
  }
}
@media (min-width: 900px) {
  .nav {
    transform: scale(1.1);
  }
  .main .container-lg {
    padding-top: 90px;
  }
}
@media (min-width: 1000px) {
  .nav {
    transform: scale(1.2);
  }
  .main .container-lg {
    padding-top: 110px;
  }
}
@media (min-width: 1100px) {
  .nav {
    transform: scale(1.3);
  }
  .main .container-lg {
    padding-top: 130px;
  }
}
@media (min-width: 1200px) {
  .nav {
    transform: scale(1.4);
  }
  .main .container-lg {
    padding-top: 110px;
  }
}

/*# sourceMappingURL=nav-public2.css.map */
