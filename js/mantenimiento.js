// const { resume } = require("browser-sync");

console.log('cargado mantenimiento.js')

function previewMantenimiento(){

    // Agregar preview a los links si tiene preview en inicio (MODO MANTENIMIENTO)
   
   let urlstr = String(window.location.href);

   if ( urlstr.includes("#preview") ){
       let anunciosLinks = document.querySelectorAll('.imgbox a');

       anunciosLinks.forEach( function(valor, indice, array) {
               valor.href = valor.href + '#preview';
           }
       );
   } 

}
 
 
function fetchMantenimientoAPI(){

    if (window.location.hash ==  "#preview"){
        console.log("Preview mode")
        return false
    } 

  fetch('https://citas10.es/inicio/&mantenimiento_activo&api') 
    .then( res => res.json() )
    .then( res => isActiveMantenimiento(res.message)) 
     
}

function isActiveMantenimiento(status){ 
   
    console.log("Mantenimiento estado es: "+ status)
    
    const _href = window.location.href;

    // Si esta activo el mantenimiento y no esta redirigido
    if ( status && !_href.includes('mantenimiento') ) {  
        console.log(' Esta activo el mantenimiento y no esta en mantenimiento ')
            const redirect = `https://${window.location.host}/mantenimiento${window.location.pathname}` 
            console.log(redirect); 
            window.location.href = redirect; 
    } 
    // Si no esta activo el mantenimiento y esta redirigido a mantenimiento
    
    if ( !status && _href.includes('mantenimiento') ) {   
        let uri = window.location.href
        let redirigir = uri.replace('/mantenimiento', '');
            console.log('No esta activo el mantenimiento y esta en mantenimiento, redirigir a: ') 
            console.log(redirigir)
            window.location.href = redirigir; 
    }

    // Si no esta activo el mantenimiento y se visualiza el sitio normal
    if ( !status && !_href.includes('mantenimiento') ) {   
        console.log('No esta activo mantenimiento y no esta en mantenimiento, no redirigir'); 
    } 

}