
console.log('footer-bs5.js')

function btnOpenAside(e){
   // console.log(e);
}

function paddingLeftMain(){
    console.log('open aside launch');
    el = document.querySelector('.main');
    console.log(el)
    
    if( el.classList.contains('open') ){
          el.classList.remove('open');
        el.removeAttribute('style')
    }else{
           el.classList.add('open');
        el.style.paddingLeft ="280px"
       
    }
    
}


/* ACTIVAR LOS ENLACES DE DOS FORMAS active() abría que ejecutarla y la otra no IIFE 'Immediately invoked function expression' */

function active(){
    let url = window.location.pathname
    var el = navbar.querySelectorAll('.navbar-nav a') 
    el.forEach( item => {
        let href = item.getAttribute('href')
        if(url == href){
        item.classList.add('active')
        }
    })
     //  href = href.replace('/', '');
   // let url = window.location.pathname.split("/");
   //  url.shift()
}



( function(){
    let url = window.location.pathname
    var el = document.querySelectorAll('.navbar-nav a') 
    el.forEach( item => {
        let href = item.getAttribute('href')
        if(url == href){
        item.classList.add('active')
        }
    })
} )()



