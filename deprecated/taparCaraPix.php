<?php
// citas10-2022.test/anuncios/&fun=webtohtml()
 
// https://dzone.com/articles/detect-and-blur-faces-programmatically
// MEDIANTE AJAX POST 
function taparCara($opt = []){

    global $pix;

    $filename = $_POST['filename'];
    $path = "uploads/fotos/$filename";
    $path_bk = "uploads/fotos/$filename.bk";
    $domain = $_SERVER['HTTP_HOST'];
    $img = "https://$domain/uploads/fotos/$filename";
    // Restaurar
    if ( file_exists($path_bk) ){
        unlink($path);
        rename($path_bk, $path);
        echo json_encode(["message" => "cara destapada"]); 
        return true;
    } 
        copy($path, $path_bk); // crear backup
 
    
    /* Invoke facedetect first  */
    if( !$pix->get('facedetect',array('img' => $img)) ){ 
         
    }
    /* Grab the total number of detected faces */ 
    $faces = $pix->json->faces;

    if( !$pix->post('mogrify', ['img' => $img,'cord' => $faces]) ){
	 
	}else{
		$link = $pix->json->link;
	}

    $link = file_get_contents($link);
    file_put_contents($path, $link);

    echo json_encode(["message" => "cara tapada $link"]); 
    
}
