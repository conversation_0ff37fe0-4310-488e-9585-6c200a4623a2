<!-- SOY EL CONTROLADOR DE anunciosFormAll.php -->

<script src="https://maps.googleapis.com/maps/api/js?key=<?= $MAPKEY ?>&libraries=places&callback=initMap&v=weekly"
    defer></script>

<?php
$pamplona = "{lat:42.81269525952281,lng:-1.6458160117659297}";
$current_gps = $pamplona;


$id = isset($_GET["id"]) ? $_GET["id"] : false;



$metodos_pago= [];
if ($id) {  // SI TIENE VALOR (ID) ES UNA EDICION 
    $anuncio = $pdo->query("SELECT * FROM anuncioFormEdit WHERE id=$id")->fetch();
    $metodos_pago= json_decode($anuncio->metodos_pago,true);
    $current_gps = $anuncio->gps;
    $coordinates = unpack('x/x/x/x/corder/Ltype/dlat/dlng', $current_gps);
    $current_gps = json_encode([
        "lat" => $coordinates['lng'],
        "lng" => $coordinates['lat']
    ]);
    
 

    $fotos = $pdo->query("SELECT * FROM fotos WHERE id_anuncio=$id")->fetchAll();

    
    $horarios = $pdo->query("SELECT * FROM horarios_anuncios WHERE id_anuncio=$id ORDER BY num_rango ASC")->fetchAll();

    $rango1 = [
        $horarios[0]->dia_inicio,
        $horarios[0]->dia_fin,
        convertirHorario($horarios[0]->hora_inicio),
        convertirHorario($horarios[0]->hora_fin)
    ];

    $rango2 = isset($horarios[1]) ? [
        $horarios[1]->dia_inicio,
        $horarios[1]->dia_fin,
        convertirHorario($horarios[1]->hora_inicio),
        convertirHorario($horarios[1]->hora_fin)
    ] : false;
    $rango3 = isset($horarios[2]) ? [
        $horarios[2]->dia_inicio,
        $horarios[2]->dia_fin,
        convertirHorario($horarios[2]->hora_inicio),
        convertirHorario($horarios[2]->hora_fin)
    ] : false;

    $notas = json_decode($anuncio->notas, true);

}

$ciudades = $pdo->query("SELECT * FROM ciudades ORDER BY nombre ASC")->fetchAll(); // Crea OBJ con los datos para listar en html
$categorias = $pdo->query("SELECT * FROM categorias ORDER BY nombre ASC")->fetchAll(); // Crea OBJ con los datos para listar en html
$paises = $pdo->query("SELECT name as label, alpha_2 as value FROM paises")->fetchAll();
$paisesJS = json_encode($paises);


 
?>