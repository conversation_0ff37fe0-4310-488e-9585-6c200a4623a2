
<?php

function anuncioActualizarAuto($opt = false){

// anuncios/&fun=anuncioActualizar("id":2,"gps":"42.52672942549892,-1.6781052256503868")

global $pdo;


$id = isset($_POST['id']) ? $_POST['id'] : $opt['id'];

$opt = $opt ? $opt : $_POST;

/* Inicio */
$sql = "UPDATE anuncios SET"; 

/* Campos opcionales */
  $i = 1;
  $camposN = count($opt) - 1;

forEach($opt as $key=>$value){

    if ($key == 'id') continue;

    if($key == 'gps'){ 
        $value = explode(",", $value); 
        $value = "POINT(".$value[1] . "," . $value[0] .")"; 
    }

    /* Agregamos campo al sql */
    if ( $key == 'gps' or $key = 'id_categoria' or is_numeric($value) ) {  
        $sql .= " $key = $value";
    }else{
        $sql .= " $key = '$value'";
    }
    
    if ($i < $camposN){
        $sql .= ',';
    }else{
        /* Final */
        $sql .= " WHERE id = $id";
    }
    $i++;
}

//$pdo->query($sql);


return $sql;


}