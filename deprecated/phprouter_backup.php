<?php

function get($route, $callback)
{
    if ($_SERVER['REQUEST_METHOD'] == 'GET') {
        route($route, $callback);
    }
}
function post($route, $callback)
{
    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        route($route, $callback);
    }
}
function put($route, $callback)
{
    if ($_SERVER['REQUEST_METHOD'] == 'PUT') {
        route($route, $callback);
    }
}
function patch($route, $callback)
{
    if ($_SERVER['REQUEST_METHOD'] == 'PATCH') {
        route($route, $callback);
    }
}
function delete($route, $callback)
{
    if ($_SERVER['REQUEST_METHOD'] == 'DELETE') {
        route($route, $callback);
    }
}
function any($route, $callback)
{
    route($route, $callback);
}
function route($route, $callback)
{
    /* ------------- | EJECUCIÓN SOLO SI LA RUTA ES LA MISMA | ----------------- */

    /* 
    if ( strpos( $route, '$') !== false) {
        $route_clean = explode('$', $route)[0];
        //elimina el ultimo slash si existe
        $route_clean = rtrim($route_clean, '/');
        if ( strpos($route_clean, flex()->route) !== false) {
            flex()->routes[] = $route;
            // capturar los valores de la url precedidos por $
            preg_match_all('/\$(\w+)/', $route, $matches);
            foreach($matches[1] as $match){
                if (!strpos(flex()->route, $match) !== false) { 
                    flex()->routes[] = $route;
                    return;
                }
            }
            flex()->route = $route; 
            
        }
 
    }else{
        if ( !Flex()->uri("$route") ) return;
        $route_clean = $route;
        flex()->routes[] = $route;
    }

    */
    $route_clean = $route;
      
    //flex()->routeclean = $route_clean;
       

    /* ------- | EJECUCIÓN SOLO SI TIENE LOS MISMOS PARAMETROS Y ES CALLABLE | -------- */

    // capturar la url
    $request_url = filter_var(flex()->path, FILTER_SANITIZE_URL);
    // quita todas las barras ('/') que haya al final de la cadena
    $request_url = rtrim($request_url, '/');
    // quita el string de la url que haya despues de la ?
    $request_url = strtok($request_url, '?');
    // quita el string de la url que haya despues de la &
    $request_url = strtok($request_url, '&'); 
    // eliminar el primer / si existe en el string
    $request_url = ltrim($request_url, '/');

    // eliminar el primer / si existe en el string
    $route = ltrim($route, '/'); 

    // creacion de arrays
    $route_parts = explode('/', $route);  
    $request_url_parts = explode('/', $request_url);   
    if ($route_parts[0] == '' && count($request_url_parts) == 0) { 
        if (is_callable($callback)) {
            call_user_func_array($callback, []);
        }
        return;
    }
    if (count($route_parts) != count($request_url_parts) ) {
        return;
    }
 
    
    /* ------- |  LOGICA DEL OBJETO | -------- */
/*
    if ( strpos( $route, '$') !== false){
        // captura las variables del string $route con formate $variable, con regexp.
        preg_match_all('/\$(\w+)/', $route, $matches);
        $arg = $matches[1][0];
        Flex()->route = str_replace("/$arg", "", $route_clean);
        flex()->view = ltrim(flex()->route, '/'); // Elimina del string el primer slash si existe

    }else{
        Flex()->route = $route_clean;
        flex()->view = ltrim(flex()->route, '/'); // Elimina del string el primer slash si existe
    }
  */

    // Elimina el ultimo slash y su valor a la derecha

    //  flex()->route = substr($route_clean, 0, strrpos($route_clean, '/'));
 
    flex()->router_route = $route;

    $parameters = [];
    for ($__i__ = 0; $__i__ < count($route_parts); $__i__++) {
        $route_part = $route_parts[$__i__];
        if (preg_match("/^[$]/", $route_part)) {
            $route_part = ltrim($route_part, '$');
            array_push($parameters, $request_url_parts[$__i__]);
            $$route_part = $request_url_parts[$__i__];
        } else if ($route_parts[$__i__] != $request_url_parts[$__i__]) {
            return;
        }
    }
    if (is_callable($callback)) {
        call_user_func_array($callback, $parameters);
  
        // De una ruta capturar las variables y retornar un array ejemplo id/$id/nombre/$nombre => ["id", "nombre"]
        $route_parts = explode('/', $route);
        $route_parts = array_filter($route_parts, function ($part) {
            return preg_match("/^[$]/", $part);
        });
        $route_parts = array_map(function ($part) {
            return ltrim($part, '$');
        }, $route_parts);
        // Combinar keys con valores
        flex()->router_parameters = array_combine($route_parts, $parameters);
 
    }

    
}
function out($text)
{
    echo htmlspecialchars($text);
}

function set_csrf()
{
    session_start();
    if (!isset($_SESSION["csrf"])) {
        $_SESSION["csrf"] = bin2hex(random_bytes(50));
    }
    echo '<input type="hidden" name="csrf" value="' . $_SESSION["csrf"] . '">';
}

function is_csrf_valid()
{
    session_start();
    if (!isset($_SESSION['csrf']) || !isset($_POST['csrf'])) {
        return false;
    }
    if ($_SESSION['csrf'] != $_POST['csrf']) {
        return false;
    }
    return true;
}