<?php

$id = app\uri(1);
 
?>
 

<h2>Mapa 1</h2>

<script src="https://maps.googleapis.com/maps/api/js?key=<?= $MAPKEY ?>&libraries=places&callback=initMap&v=weekly" defer></script>

<div class="mb-3 col-6">
    <form action="" method="post">
        
            <label for="dirección" class="form-label">Dirección: </label>
            <input type="text" class="form-control" name="direccion" id="direccion" placeholder="Escribe dirección">
            <label for="coords" class="form-label">Coordenadas: </label>
            <input class="form-control" id="coords" name="coords" type="text" style="pointer-events:none"> <br>

            <input class="btn btn-success" type="submit" value="Enviar">
        
    </form>
</div>
<div id="map" style="width:500px;height:500px"></div>

    <script>
        let map;
        let marker;
        let autocomplete;
        const initMap = () => {
            const placeMarker = (position) => {
                if (marker == null) {
                    marker = new google.maps.Marker({
                        position,
                        map
                    });
                } else {
                    marker.setPosition(position)
                }
            }
            const $direccion = document.getElementById('direccion');
            const options = {
                componentRestrictions: {
                    country: "es"
                },
                fields: ["address_components", "geometry", "icon", "name"],
                strictBounds: false
            }
            const $divMap = document.getElementById("map");
            map = new google.maps.Map($divMap, {
                center: {
                    lat: 42.8033075,
                    lng: -1.6891741
                },
                zoom: 12
            })
            const geocodeLatLng= async (latlng)=>{
                const geocoder= new google.maps.Geocoder();
                const response= await geocoder.geocode({location:latlng});
                if(response.results[0]){
                    return response.results[0].formatted_address;
                }
                return "No se encontró ubicación";
            }
            map.addListener('click', async (e) => {

                const position = e.latLng;
                const {
                    lat,
                    lng
                } = position;
                const positionJSON= {lat:lat(),lng:lng()};
                const positionText = `${lat()},${lng()}`
                const $coords = document.getElementById('coords');
                console.log(positionJSON);
                const direccion= await geocodeLatLng(positionJSON);
                $direccion.value= direccion;
                $coords.value = positionText;
                placeMarker(position)
            })
            autocomplete = new google.maps.places.Autocomplete($direccion, options)
            autocomplete.addListener("place_changed", () => {
                const place = autocomplete.getPlace();
                const position = place.geometry.location;
                const {
                    lat,
                    lng
                } = position;
                const positionJSON = `${lat()},${lng()}`
                console.log(positionJSON);
                placeMarker(position);
                map.setCenter(position);
                map.setZoom(17);
                const $coords = document.getElementById('coords');
                $coords.value = positionJSON;
            })
     
        }
        window.initMap = initMap
    </script>

