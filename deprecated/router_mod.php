<?php

 /**
 * +------------------------------------------+
 * |             NO USADA                     |
 * +------------------------------------------+
 * |                                          |
 * | Esta versión no se esta usando           |
 * |                                          |
 * +------------------------------------------+
 */


    function get($route, $path_to_include)
    {
        if ($_SERVER['REQUEST_METHOD'] == 'GET') {
            route($route, $path_to_include);
        }
    }
    function post($route, $path_to_include)
    {
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            route($route, $path_to_include);
        }
    }
    function put($route, $path_to_include)
    {
        if ($_SERVER['REQUEST_METHOD'] == 'PUT') {
            route($route, $path_to_include);
        }
    }
    function patch($route, $path_to_include)
    {
        if ($_SERVER['REQUEST_METHOD'] == 'PATCH') {
            route($route, $path_to_include);
        }
    }
    function delete($route, $path_to_include)
    {
        if ($_SERVER['REQUEST_METHOD'] == 'DELETE') {
            route($route, $path_to_include);
        }
    }
    function any($route, $path_to_include)
    {
        route($route, $path_to_include);
    }
    function route($route, $callback)
    {
         /* --------------- SOLO SE EJECUTARÁ SI LA RUTA ES LA MISMA ---------------
          --------------------  LIMPIANDO LOS ARGUMENTOS   -----------------------*/

        if ( strpos( $route, '$') !== false){
            /* ---------- Primer argumento ------------ */
            $dollar = explode('$', $route);
            $primer_arg = explode( "/", $dollar[1])[0];

            /* --------- Separando la vista -------------- */
            $vista = explode("/$primer_arg/", $route)[0];
            $vista2 = explode("/$primer_arg/", flex()->route)[0];

            $ruta = explode('/$',  $route)[0];
            $ruta = substr($ruta, 0, strrpos($ruta, '/'));
            if ($vista != $vista2 ) return false;
            # --------- path
            flex()->debugbarInfo(["$"=> "contiene", "route"=>$route, "ruta"=>$ruta,"flex()->route: "=>flex()->route,"primer_arg"=>$primer_arg, "vista:"=>$vista,"vista2:"=>$vista2]);
             $path = explode('/$',  $route)[0];

        } else{
            if ($route != flex()->route) return false;
            $vista2 = flex()->route;
        }

        echo "<h2> route: $route </h2>";
        // Eliminando el primer caracter si es slash
        flex()->view = ltrim($vista2, '/');
        flex()->route = $vista2;

        flex()->view_path();

        $request_url = filter_var(flex()->uri(), FILTER_SANITIZE_URL);
        $request_url = rtrim($request_url, '/');
        $request_url = strtok($request_url, '?');
        $route_parts = explode('/', $route);
        $request_url_parts = explode('/', $request_url);
        array_shift($route_parts);
        array_shift($request_url_parts);
        if ($route_parts[0] == '' && count($request_url_parts) == 0) {
            // Callback function
            if (is_callable($callback)) {
                call_user_func_array($callback, []);
               // exit();flex()->router_route
            }
        }
        if (count($route_parts) != count($request_url_parts)) {

        }
        $parameters = [];
        for ($__i__ = 0; $__i__ < count($route_parts); $__i__++) {
            $route_part = $route_parts[$__i__];
            if (preg_match("/^[$]/", $route_part)) {
                $route_part = ltrim($route_part, '$');
                array_push($parameters, $request_url_parts[$__i__]);

                $$route_part = $request_url_parts[$__i__];
            } else if ($route_parts[$__i__] != $request_url_parts[$__i__]) {
                return;
            }
        }
            call_user_func_array($callback, $parameters);
            Flex()->router_parameters = $parameters;

            // De una ruta capturar las variables y retornar un array ejemplo id/$id/nombre/$nombre => ["id", "nombre"]
            $route_parts = explode('/', $route);
            $route_parts = array_filter($route_parts, function ($part) {
                return preg_match("/^[$]/", $part);
            });
            $route_parts = array_map(function ($part) {
                return ltrim($part, '$');
            }, $route_parts);
            // Combinar keys con valores
              flex()->router_parameters = array_combine($route_parts, $parameters);

            // Si la ruta contiene caracter $
            if (strpos( $route, '$') !== false) {
                /* Eliminando los parametros de la ruta */
                $route_clean = explode('/$',  $route)[0];
                // eliminar el texto tras el ultimo slash / del string $params de forma simple
                $route_clean = substr($route_clean, 0, strrpos($route_clean, '/'));
                flex()->router_route = $route_clean;

                // seleccionar el ultimo valor tras / no númerico ni que incluya dos puntos con regexp
                preg_match('/\/([^\/]*)$/', $route_clean, $matches);


            }else{
               flex()->router_route = flex()->route;
                // eliminar el texto tras el ultimo slash / del string $params de forma simple
                preg_match('/\/([^\/]*)$/', flex()->router_route, $matches);

            }



    }
    function out($text)
    {
        echo htmlspecialchars($text);
    }

    function set_csrf()
    {
        session_start();
        if (!isset($_SESSION["csrf"])) {
            $_SESSION["csrf"] = bin2hex(random_bytes(50));
        }
        echo '<input type="hidden" name="csrf" value="' . $_SESSION["csrf"] . '">';
    }

    function is_csrf_valid()
    {
        session_start();
        if (!isset($_SESSION['csrf']) || !isset($_POST['csrf'])) {
            return false;
        }
        if ($_SESSION['csrf'] != $_POST['csrf']) {
            return false;
        }
        return true;
    }
