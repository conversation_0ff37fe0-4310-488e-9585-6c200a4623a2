
<?php

/* 
  
  // $_SESSION['debug']['PDO'] = ['PDO'=>$pdo,'DSN'=>$dsn,'pdo_pass'=>$pdo_pass];
  //$_SESSION['debug']['Config'] = app\debug_config(); 
  if ( !isset($_SESSION['debug']) ) $_SESSION['debug']  = []; 
  $debugbar->addCollector(new DebugBar\DataCollector\ConfigCollector( $_SESSION['debug'], "Debug"));


// $debugbar->addCollector(new DebugBar\DataCollector\ConfigCollector($_SESSION['debug'], 'Debug'));

  */


function debugBarTabs($array){

// Crear tabs de valores debugBarTabs($_GET);
// Crear tabs de valores debugBarTabs($_SESSION['debug']);
global $debugbar;  

foreach($array as $key=>$value){
    
    if(is_array($value)){ 
        if(key($value) == "PHPDEBUGBAR_STACK_DATA"){
            continue;
        }
         $debugbar->addCollector(new DebugBar\DataCollector\ConfigCollector((array) $value, $key));
    }else{
       // $_SESSION["SESSION"] = json_encode($value);
    }
}    
return $array;

}    


function crearEvento($opt = []){

    global $pdo;
    $id_anuncio = $opt['id'];
    $fecha_creacion = $opt['fecha_creacion'];
    $fecha_ejecucion = $opt['fecha_ejecucion'];
    $tipo = $opt['tipo'];
    $accion = $opt['accion'];
    $estado = $opt['estado'];


    if (strtotime($fecha_publicacion) >= strtotime($hoy)) {
        $sqlEvento = "INSERT INTO eventos VALUES(0,$id_anuncio,'$hoy','$fecha_publicacion','anuncio','publicar',true)";
        
    }else{ 
        $sqlEvento = "INSERT INTO eventos VALUES(0,$id_anuncio,'$hoy','$fecha_caducidad','anuncio','despublicar',true)";
    }
    $pdo->query($sqlEvento);


 }
 
function sessiondebug($ar = []){ // ["sql"=>'inser into...']
    $date = date("d_H:i:s");
    $limit = 10; 
    // Si no existe se crea el array por primera vez
      
    if ( !isset($_SESSION['debug']) ) $_SESSION['debug'] = []; 
    if ( !isset($_SESSION['debug']['Mensajes']) ) $_SESSION['debug']['Mensajes'] = [];

    $key = is_array($ar) ? key($ar) : $ar;
    $value = $ar;
 
    if ( !is_array($ar) ) $ar = [$value=>$value];
    $_SESSION['debug']['Mensajes'] = [$key=>$value]; 
 
   // $_SESSION['debug'] = $ar; 
   // Borrado y saca fuera se esta en producción 
    // Si esta vacio el input se retorna los valores hasta el momento 
   /* if(count($ar) < 1 ){ 
            return $_SESSION['debug'];
        }  
        $count_history = count( $_SESSION['debug'][$key] ); 
        // Elimina uno de la lista el mas viejo
        if($count_history >= $limit){
        array_shift($_SESSION['debug'][$key]) ;  
        }
    */ 
    // Escribe el valor
    $_SESSION['debug'][$date][$key] = $value;  
    // Se guarda los valores limitando a limit 
    return true; 
} 