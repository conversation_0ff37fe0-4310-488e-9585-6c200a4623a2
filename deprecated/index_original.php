<?php 
session_start(); 
 
if( !isset($_GET['url']) ) {
header("Location: /inicio/");  
 // echo "<script> location.href='/inicio/'; </script>";
}else{
  // Forzar redirección de vistas con / al final

  if( !isset($_GET['buscar']) or !isset($_GET['search']) ){ 
    $request = $_SERVER['REQUEST_URI'];
    $requestNoGet = explode("&", $request)[0];
    // ---------- es necesario ? ---------------
  //  if ( substr($requestNoGet, -1) != '/' ) header("Location: $requestNoGet/");
  }
} 
// tapar cara libreria ia
require_once 'pixlab.php';
$pixlab_key = "4babdf23733beb2cb9109d667a857dfc";
$pix = new Pixlab($pixlab_key);
//---------------------------------

require_once 'vendor/autoload.php';  
// key apis gerar
$MAPKEY = 'AIzaSyDwD7qDAQ2FhG0zILfd0Z_EjLItMckPUqM'; 
// FAKER
$faker = Faker\Factory::create('es_ES');

// zipfile "nelexa/zip": "^4.0"
$zipFile = new \PhpZip\ZipFile();

//----------------- APP INIT AND CONFIG
include_once "framework.php";
$opciones = [ "dev" => "auto" ]; 
// Set Enviroment and init 
app\init($opciones);  

$page = app\getPageName();

 
// --------------------- BASE DE DATOS CREDENCIALES Y CONNECTION
    include_once "pdo.php";

    $debugbar = new DebugBar\StandardDebugBar();  
  
   if( !app\api() )  $debugbarRenderer = $debugbar->getJavascriptRenderer();
  
   
try {    
    $pdo = new DebugBar\DataCollector\PDO\TraceablePDO(new PDO($dsn, $user_db, $pass_db, $pdo_options));
    $debugbar->addCollector(new DebugBar\DataCollector\PDO\PDOCollector($pdo));
     
} catch (Exception $e) {
  if( isset($_SESSION['adm']) ) $debugbar['exceptions']->addException($e);
} 
// Bootstrap debugbar
$dev= app\dev();   

include 'funciones.php';  

// redireccion_mantenimiento();
 
checkEvents();

if( !isset($_SESSION['userGPS']) ) userGPS(); //LOCALIZAR POR IP LA CIUDAD

redirectNoAdmin();

if (app\controller() != false) include app\controller(); // Partes de lógica de vistas

/* ----------------------------- HEADER  -------------------------------------------*/

if ( getfun("getHead()") ) include app\head( getHead() );
 
/* -----------------------------  NAV -------------------------------------------*/
  if(Flex::uri("x")){
    if ( getfun("getNav()") ) include_once app\nav( getNav() );
  }

/* -----------------------------  contenido -------------------------------------------*/

if (!app\api()) include app\page();

  $debugbar->addCollector(new DebugBar\DataCollector\ConfigCollector( app\debug_config(), "Config"));  
  // $debugbar->addCollector(new DebugBar\DataCollector\ConfigCollector( $_SESSION['logbar'] ?? [] , "logbar"));
  $debugbar->addCollector(new DebugBar\DataCollector\ConfigCollector( array_reverse($_SESSION['logg']) ?? [] , "logg"));


// app\console( app\debug_config() );
 
// Footer
if ( !app\api() ){ // Ejemplo para cargar configurar carga de footer
  
  if ($page != 'anuncio')  include app\footer('bs5');   
  
}
  
?>

<?php hookFooter() ?>

