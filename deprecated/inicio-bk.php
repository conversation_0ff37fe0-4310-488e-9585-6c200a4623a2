<?php
 
//app\debug($anunciosPorDisponibilidad);
//$origen = "POINT(-1.6460340021418403, 42.822268923138786)";


$hora = date('H:i:s');

$destacados = isset($_GET['destacados']);

$sql_categoria = $categoriaNumero != 0 ? "AND id_categoria=$categoriaNumero" : '';


$coords = isset($_POST['coords']) ? $_POST['coords'] : false;

if (!$coords && isset($_SESSION['gps'])) $coords = $_SESSION['gps'];

$origen = "POINT(-1.6298523422954048, 42.82970209596372)";
$gpsJS = ["lat" => 42.82970209596372, "lng" => -1.6298523422954048];


if ($coords != false) {
    /* echo "OK"; */
    $_SESSION['gps'] = $coords;
    $coords = explode(",", $coords);
    $origen = "POINT(" . $coords[1] . "," . $coords[0] . ")";


    $gpsJS["lat"] = (float)$coords[0];
    $gpsJS["lng"] = (float)$coords[1];
}



if (isset($_GET['search'])) {
    $value = $_GET['search'];

    if (is_numeric($value)) $value = str_replace(' ', '', $value);

    $sql = "SELECT *, ROUND(st_distance_sphere($origen, gps)/1000,2) as distancia FROM view_portadas WHERE estado_anuncio=1 $sql_categoria AND id_ciudad=$id_ciudad AND (id='$value' OR nombre LIKE '%$value%' OR telefono='$value' OR telefono2='$value') ORDER BY $order_seleccionado";    //app\debug($sql);
    $anuncios = $pdo->query($sql)->fetchAll();
} else {
    $sql = "SELECT *, ROUND(st_distance_sphere($origen, gps)/1000,2) as distancia FROM view_portadas WHERE estado_anuncio=1 $sql_categoria AND id_ciudad=$id_ciudad ORDER BY $order_seleccionado";    //app\debug($sql);

    $anuncios = $pdo->query($sql)->fetchAll(); // Crea OBJ con los datos para listar en html

}


//Agregar WHERE estado=1 a la view_portadas para optimizar INNER JOIN




$superDestacados = array_filter($anuncios, function ($anuncio) {
    return $anuncio->estado_destacado == 1 && $anuncio->destacado_tipo == 1;
});

$destacadosNormales = array_filter($anuncios, function ($anuncio) {
    return $anuncio->estado_destacado == 1 && $anuncio->destacado_tipo == 0;
});

shuffle($superDestacados);
shuffle($destacadosNormales);


$gpsJS = json_encode($gpsJS);
//echo $gpsJS


include app\part('modalFiltro'); 
include app\part('modalDireccion'); 
include app\part('modalModoGeo'); 
include app\part('modalOrden'); 
?>



<div class="main">
    <!-- haciendo padding-left 280px puedes empujar el container y hacer un sidebar menu -->

    <div class="container-lg">

        
        <!-- 
<span style="display: inline-block; background-color: #f0e1b5; padding: 4px; border-radius: 7px; height: 30px">
    Las distancias </span>
<br>


<span>
    <strong>Distancia calculada por : </strong><small style="border: 1px solid grey; border-radius: 5px; padding: 4px"
        id="direccion-dropdown"> ... </small>
</span>

 
 
<div class="dropdown" style="display: inline-block">
    <button class="btn btn-light dropdown-toggle" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown"
        aria-expanded="false">
        <i class="fa-solid fa-location-crosshairs"></i>
        Editar
    </button>
       <form id="dropdown-form" method="post" action="">
 
    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
        <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#modalDistancia">Escribe una
                dirección</a></li>
        <li>
            <form id="dropdown-form" method="post" action="">
                <input id="coords" name="coords" type="hidden" style="pointer-events:none">
                <button type="submit" class="dropdown-item" id="geolocalizacion">Mi posición actual</>
            </form>
        </li>
    </ul>
  </form>

</div>
 -->


        <script>
        let autocomplete;
        let currentGPS = <?= $gpsJS ?>;
        console.log("current", currentGPS);
        const initMap = () => {
            const $direccion = document.getElementById('direccion');
            const options = {
                componentRestrictions: {
                    country: "es"
                },
                fields: ["address_components", "geometry", "icon", "name"],
                strictBounds: false
            }
            autocomplete = new google.maps.places.Autocomplete($direccion, options)
            autocomplete.addListener("place_changed", () => {
                const place = autocomplete.getPlace();
                const position = place.geometry.location;
                const {
                    lat,
                    lng
                } = position;
                const positionJSON = `${lat()},${lng()}`
                console.log(positionJSON);
                const $coords = document.getElementById('coords');
                $coords.value = positionJSON;
            });
            (
                async () => {

                    const direccion = await geocodeLatLng(currentGPS); 
                    const $direccionStrMovil = document.getElementById('direccionStrMovil');
                    const $direccionStrPc = document.getElementById('direccionStrPc');
 
                    direccionCorta = direccion.replace(", España", "").replace(", Navarra", "");
                    $direccionStrMovil.innerHTML = direccionCorta;
                    $direccionStrPc.innerHTML = direccionCorta;
                }
            )()

        }
        window.initMap = initMap
        const geocodeLatLng = async (latlng) => {
            console.log("geocoding", latlng);
            const geocoder = new google.maps.Geocoder();
            const response = await geocoder.geocode({
                location: latlng
            });
            if (response.results[0]) {
                console.log("objGeo",response.results[0])
                const result = {}
                /* const objAddress = response.results[0].address_components.forEach(
                    ({
                        short_name,
                        types
                    }) => {
                        const isStreet = types.includes('street_number');
                        if (isStreet) {
                            result.street = short_name
                            return;
                        }
                        const isRoute = types.includes('route');
                        if (isRoute) {
                            result.route = short_name
                            return;
                        }
                        const isLocality = types.includes('locality');
                        if (isLocality) {
                            result.locality = short_name;
                            return;
                        }
                    }

                );
 */
                const outputAddress = response.results[0].formatted_address;
                return outputAddress;
            }
            return "No se encontró ubicación";
        }
        const getLocation = (callback = null) => {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    async (position) => {
                        console.log(position);
                        const coords = position.coords;
                        const {
                            latitude,
                            longitude
                        } = coords;
                        const positionText = `${latitude},${longitude}`
                        const $coords = document.getElementById('coords');
                        $coords.value = positionText;
                        console.log($coords.value);
                        const direccion = await geocodeLatLng({
                            lat: latitude,
                            lng: longitude
                        });
                        const $direccion = document.getElementById('direccion');
                        $direccion.value = direccion;
                        if (callback) callback();
                    }
                )
            } else {
                console.log("ERROR")
            }
        }
        const $btnGeolocalizacion = document.getElementById("geolocalizacion");
        $btnGeolocalizacion.addEventListener('click', (e) => {
            e.preventDefault();
            console.log("CLICK")
            getLocation(() => {
                $form = document.getElementById("formGPS");
                $form.submit();
            });

        });
        </script>

        <?php 
        if (isset($_GET['search'])) : ?>
        
            <div class="alert alert-warning alert-dismissible fade show col-12 col-md-6 mx-auto mt-md-5 text-center"
                role="alert">
                Mostrando busqueda: <strong> <?= $value ?></strong>
                <a href="/">
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </a>
            </div>
       

        <?php endif ?>

        <!-- <embed type="image/svg+xml" src="image.svg" /> Cargar svg -->

        <?php if ($_SESSION['orden_portadas'] == 'posicion') : ?>
        <div class="row align-items-center contenedor destacados">

            <!-- FOR COMPONENTE SUPERDESTACADO -->

            <?php foreach ($superDestacados as $anuncio) : ?>
            <div class="col-6 col-xl-4" data-aos="zoom-in">
                <?php include app\part('portada'); ?>
            </div> <!-- col-6 portada -->
            <?php endforeach ?>

        </div> <!-- row -->
        <?php endif ?>
        <?php if ($_SESSION['orden_portadas'] == 'posicion') : ?>
        <div class="row align-items-center contenedor destacados">

            <!-- FOR COMPONENTE DESTACADO -->

            <?php foreach ($destacadosNormales as $anuncio) : ?>
            <div class="col-6 col-xl-4" data-aos="zoom-in">
                <?php include app\part('portada'); ?>
            </div> <!-- col-6 portada -->
            <?php endforeach ?>

        </div> <!-- row -->
        <?php endif ?>

        <div class="row align-items-center contenedor anuncios">

            <!-- FOR COMPONENTE PORTADA -->

            <?php foreach ($anuncios as $anuncio) : ?>
            <div class="col-6 col-xl-4" data-aos="zoom-in">
                <?php include app\part('portada'); ?>
            </div> <!-- col-6 portada -->
            <?php endforeach ?>

        </div> <!-- row -->

        <script>
        function alternarFotos() {
            $imgAnuncios = Array.from(document.querySelectorAll(".imgAnuncio"));

            $imgAnuncios.forEach(
                ($imgAnuncio) => {
                    $src = $imgAnuncio.getAttribute("src");

                    $fotos = JSON.parse($imgAnuncio.getAttribute("data-images").replace(/'/g, '"'));
                    $activeIndex = parseInt($imgAnuncio.getAttribute("data-active-image"));
                    $activeFoto = $fotos[$activeIndex];
                    let $newIndex = ($activeIndex >= $fotos.length - 1) ? 0 : $activeIndex + 1;
                    $newSrc = $src.replace($activeFoto, $fotos[$newIndex]);

                    //console.log($src, $newSrc);
                    $imgAnuncio.setAttribute("src", $newSrc);
                    $imgAnuncio.setAttribute("data-active-image", $newIndex);

                }
            )
        }



        setInterval(
            () => {
                alternarFotos();
            }, 2000
        )


        function alternarPortadaMensaje() {
            const $portadaMensajes = Array.from(document.querySelectorAll("#portada_mensaje"));

            $portadaMensajes.forEach(
                ($el) => {

                    let contenido = $el.textContent;
                    const t1 = $el.getAttribute('data-1')
                    const t2 = $el.getAttribute('data-2')
                    if (!t2) return
                    if (t1 == contenido) {
                        $el.textContent = t2;
                    } else {
                        $el.textContent = t1;
                    }

                }
            )

        }
        setInterval(
            () => {
                alternarPortadaMensaje();
            }, 1500
        )



        AOS.init();
        </script>