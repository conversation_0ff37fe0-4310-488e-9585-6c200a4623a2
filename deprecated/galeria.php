<h2>Galeria de fotos</h2>

<?php

$id = $_GET["id"];
$sql = "SELECT * FROM anuncios WHERE id=$id";


// Máximo de 2 fotos destacadas
  
// echo $sql;
$anuncio = $pdo->query($sql)->fetch(); // Crea OBJ con los datos para listar en html 
$fotos = $pdo->query("SELECT * FROM fotos WHERE id_anuncio=$id ORDER BY pos ASC")->fetchAll(); // Crea OBJ con los datos para listar en html

/* ------------- BORRAR FOTO  --------------------- */


if (isset($_GET['borrar'])) {
    $id_foto = $_GET['borrar'];

    foreach ($fotos as $foto) {


        if ($foto->id == $id_foto) {

            $sql = "DELETE FROM fotos WHERE id = $id_foto";
            $sql_ordenar = "UPDATE fotos SET pos=pos-1  WHERE id_anuncio=$id AND pos>$foto->pos";
            $pdo->query($sql);
            $pdo->query($sql_ordenar);

            if (unlink("uploads/fotos/$foto->filename")) {
                echo "Se borro la foto $foto->filename";
            } else {
                echo "Ocurrio un error $foto->filename";
            }
        }
    }


    echo "filename: $foto->filename";
    // Borrado físico de la imágen

    actualizarPortadaAnuncio($anuncio->id);
    if($anuncio->estado_destacado==1) actualizarPortadaDestacado($anuncio->id);
    // ------ Redirección
    $redireccion_url = $uri . "&id=" . $id;
    app\redirect($redireccion_url);
}


/* ------------- SUBIR POSICIÓN DE FOTO  --------------------- */

if (isset($_GET['subir'])) {
    $id_foto = $_GET['subir'];
    $pos_desde = $_GET['de'];

    echo "Se subirá la foto $id_foto desde la posición $pos_desde";
    $sql_primero = "UPDATE FOTOS SET pos=1 WHERE id=$id_foto";
    $sql_ordenar = "UPDATE fotos SET pos=pos + 1  WHERE id_anuncio=$id AND pos<$pos_desde";
    $pdo->query($sql_ordenar);
    $pdo->query($sql_primero);
    
    $foto = $pdo->query("SELECT id_anuncio,filename from fotos WHERE id=$id_foto")->fetch();
    $id_anuncio= $foto->id_anuncio;
    actualizarPortadaAnuncio($id_anuncio);
    $redireccion_url = $uri . "&id=" . $id;
    app\redirect($redireccion_url);
}


?>

<div class="d-grid gap-2 d-md-block">
    <a type="button" href="/anuncios&update=<?= $id ?>" class="btn btn-success">Volver</a>
</div>

<?= "<h2> $anuncio->nombre </h2> " ?>
<!-- GALERIA DEF FOTOS DEL ANUNCIO -->
<?php foreach ($fotos as $foto) : ?>
    <span><?= "filename: $foto->filename" ?> <br></span>
    <img width="200" src="/uploads/fotos/<?= $foto->filename ?>" alt=""> <br>
    <p><?= "<strong>Posición: </strong>" . $foto->pos ?> <br>
        <a href="<?= $uri . '&id=' . $id . '&borrar=' . $foto->id ?>">Borrar</a><br>
    </p>

    <?php if ($anuncio->estado_destacado): ?>
        <input class="destacado" onchange="destacarFoto(<?= $foto->id ?>)" name="destacado" id="destacado<?= $foto->id ?>" <?= $foto->destacado ? 'checked' : '' ?> type="checkbox">
        <label for="destacado<?= $foto->id ?>">Destacado</label> <br>
    <?php endif ?>

    <a href="<?= $uri . '&id=' . $id . '&subir=' . $foto->id . '&de=' . $foto->pos ?>">Subir</a><br></p>
    
    <hr>
<?php endforeach;


?>

<script>
 
    
    async function destacarFoto(id) {
        const method = event.target.checked ? 'POST' : 'DELETE';
        try {
            const response = await fetch(`/destacadoFoto/&api&id=${id}`, { method: method })
            const json = await response.json();
        } catch (err) {
            console.log(err);
        }
      location.reload(); 
    }
</script>