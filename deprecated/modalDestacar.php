<?php 
 $fotos = $pdo->query( "SELECT * FROM fotos WHERE id_anuncio=$anuncio->id ORDER BY pos ASC")->fetchAll(); // Crea OBJ con los datos para listar en html
 $recuperarEventos= "SELECT * FROM eventos WHERE id_anuncio=$anuncio->id AND estado=1 AND tipo='destacado' ORDER BY accion ASC";
 $eventos= $pdo->query($recuperarEventos)->fetchAll();
$fecha_publicacion="";
$fecha_despublicar="";
$dias=1;
 if(count($eventos)==2){
  $fecha_publicacion= $eventos[0]->fecha_ejecucion;
  $fecha_despublicar= $eventos[1]->fecha_ejecucion;
  $dias= (strtotime($fecha_despublicar) - strtotime($fecha_publicacion))/3600/24;
 }
 $fecha_publicacion=date_format(date_create($fecha_publicacion), "Y-m-d\TH:i")
 ?>

<!------------------------ Programar renovar modal -------------------------- !-->
<form action="anuncios&modalDestacar" method="POST">
    <div class="modal face" id="modalDestacar<?=$anuncio->id?>" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">

                <div class="modal-header">
                    <h5 class="modal-title">Destacar a <?="$anuncio->nombre (id: $anuncio->id)"?></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div> <!-- modal-header -->

                <div class="modal-body">

                    <form action="">
                        <div class="mb-3">
                            <label for="dias" class="form-label">Número de días</label>
                            <input type="text" class="form-control" id="dias" value="<?= $dias?>" name="dias">
                        </div> <!-- mb-3 -->
  
                        <div class="form-check form-check-inline">
                            <input type="checkbox" class="form-check-input" id="super" name="super">
                            <label for="super" class="form-check-label">Super destacado</label><br>
                        </div> <!-- form-check-->
 
                        <label for="fecha_a_destacar" class="mt-2"> Seleccione fecha y hora</label>
                        <input type="datetime-local" class="form-control mt-1" id="fecha_a_destacar"
                            name="fecha_a_destacar" value="<?= $fecha_publicacion?>">



                        <!--  --------------------------------------- FOTOS A DESTACAR ----------------------------------------->
                        <div style="max-width: 500px; flex-wrap: wrap" class="d-flex foto_destacar mt-3">

                            <?php foreach($fotos as $foto): ?>

                            <div class="form-check">
                                <label class="form-check-label" for="foto<?=$foto->id?>">
                                    <?=$foto->pos?>
                                    <img width="80" src="/uploads/fotos/<?=$foto->filename?>" alt=""> <br>
                                </label>
                                <input type="checkbox" class="form-check-input" name="foto_destacar[]"
                                    id="foto<?=$foto->id?>" value="<?=$foto->id?>">
                            </div>




                            <?php endforeach ?>
                        </div> <!-- .foto_destacar -->

                        <!-- ------------------------------------- FIN FOTOS A DESTACAR --------------------------------------------->

                        <input type="hidden" name="id" value="<?=$anuncio->id?>">



                </div> <!-- .modal-body -->

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">Guardar</button>
                </div> <!-- modal-footer -->

            </div> <!-- 3 modal-content -->
        </div> <!-- 2 modal dialog -->
    </div> <!-- 1 modal -->


</form>