<?php
 
/* en index.php para que funcione replica estaba esto
  
  try {     
    $pdo = new DebugBar\DataCollector\PDO\TraceablePDO(  new Replicate( dsn($s1), $s1['user'], $s1['pass'], $pdo_options)); 
     
    $debugbar->addCollector(new DebugBar\DataCollector\PDO\PDOCollector($pdo));
     
} catch (Exception $e) {
  if( isset($_SESSION['adm']) ) $debugbar['exceptions']->addException($e);
} 
 

*/




/**------------------------------------------------------------------------------------------------
 *         DATOS PARA CONEXIÓN 
 *------------------------------------------------------------------------------------------------**/

$pdo_options = [
  PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES 'utf8mb4'", // especificar un comando inicial para la conexión.
  PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_OBJ, //establecer el formato en que se devuelven los datos
  PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
  // PDO::ATTR_PERSISTENT => true
];


$prod1 = ["host" => "localhost", "dbname" => "citas10", "user" => "citas10", "pass" => "lean1#4everNB@4710"];
$prod2 = ["host" => "localhost", "dbname" => "replica", "user" => "root", "pass" => ""];
// Developments
$dev1 =  ["host" => "localhost", "dbname" => "citas10-2022", "user" => "root", "pass" => ""];
$dev2 =  ["host" => "localhost", "dbname" => "replica", "user" => "root", "pass" => ""];


if (PHP_OS == 'WINNT') {
  $s1 = $dev1;
  $s2 = $dev2;
} else {
  $s1 = $prod1;
  $s2 = $prod2;
}

/**------------------------------------------------------------------------------------------------
 *         INSTANCIACIÓN DE OBJETOS PDO 
 *------------------------------------------------------------------------------------------------**/


function dsn($server)
{
  $dsn = "mysql:host=" . $server['host'] . ";dbname=" . $server['dbname'] . ";charset=utf8mb4";
  return $dsn;
}

function getPDO1()
{
  global $s1, $pdo_options;
  return new PDO(dsn($s1), $s1['user'], $s1['pass'], $pdo_options);
}

function getPDO2()
{
  global $s2, $pdo_options;
  return new PDO(dsn($s2), $s2['user'], $s2['pass'], $pdo_options);
}

/**------------------------------------------------------------------------------------------------
 *    VALIDAR SI CONECTARÁ Y CONSULTARA A UNA 2º DB 
 *------------------------------------------------------------------------------------------------**/


function do_replication()
{

  /* Leemos el archivos JSON y lo convertimos en un objeto PHP */
  $config_file = json_decode(file_get_contents("config.json"));

  /* Si la sincronización no esta activa */
  if ($config_file->rdbms < 2) return false;

  /* si el mantenimiento esta activado salimos */
  if ($config_file->mantenimiento_status == 1) return false;

  /* Si la sincronización esta desactivada salimos */
  if ($config_file->DBSync == 0) return false;

  /* SI REALIZARA el duplicado de conexion y escrituras DB */
  return true;
}

/**------------------------------------------------------------------------------------------------
 *    HELPER Y CLASE PARA ESCRIBIR LA FECHA DE MODIFICACIÓN
 *------------------------------------------------------------------------------------------------**/

function setUpdateTimeDB($pdo)
{
  $fecha = date('Y-m-d H:i:s');
  $sql = "UPDATE options SET value = '$fecha' WHERE name = 'lastupdate'";
  $pdo->query($sql);
}

/* Detectar si un servidor esta caido y saltar al siguiente en un ciclo donde se puede poner N en loop */


class Replicate extends \PDO
{

  public $backup_connection;

  public function __construct($dsn, $user = null, $pass = null, $pdo_options = null)
  { 

    parent::__construct($dsn, $user, $pass, $pdo_options);

    // CONEXIÓN DB BACKUP
    if (do_replication()) {
      $this->backup_connection = getPDO2();
    }
  }

  public function query($query, $fetchMode = null, ...$fetchModeArgs)
  {
 
  // BASE DE DATOS BACKUP
  if (do_replication()) {
    if (!str_contains($query, 'SELECT')) {
      setUpdateTimeDB( getPDO2() );
      $this->backup_connection->query($query); 
    }
  }
   
  // BASE DE DATOS PRINCIPAL
  if (!str_contains($query, 'SELECT')) {
    setUpdateTimeDB( getPDO1() );
  }

  return parent::query($query, $fetchMode, ...$fetchModeArgs);

  }
}
