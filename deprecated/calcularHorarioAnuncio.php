
<?php
// ------------------- SE ESTA USANDO ??????????? ------------------------------------------- */

function calcularHorarioAnuncio($dia_inicio, $dia_fin, $hora_inicio, $hora_fin, $rango)
{
    $dias = [];
    if ($dia_inicio <= $dia_fin) {
        $dias = range($dia_inicio, $dia_fin);
    } else {
        $dias1 = range($dia_inicio, 6);
        $dias2 = range(0, $dia_fin);
        $dias = array_merge($dias1, $dias2);
    }
    $diasYHoras = [];
    $salida = $hora_fin - 1;

    foreach ($dias as $dia) {
        //
        $recuperar_horario = $dia_inicio == $dia ? $rango . "_" . $dia_inicio . "_" . $dia_fin . "_" . $hora_inicio . "_" . $hora_fin : null;

        if ($hora_inicio >= $hora_fin) { // Lunes - Lunes
            $diasYHoras[] = [
                "dia" => $dia,
                "hora_inicio" => "$hora_inicio:00",
                "hora_fin" => '23:59',
                "rango" => $recuperar_horario
            ];
            if ($hora_fin != '00') {

                $madrugada = $dia == 6 ? 0 : $dia + 1;

                $diasYHoras[] = [
                    "dia" => $madrugada,
                    "hora_inicio" => "00:00",
                    "hora_fin" => "$salida:59",
                    "rango" => null
                ];
            }
        } else {
            $diasYHoras[] = [
                "dia" => $dia,
                "hora_inicio" => "$hora_inicio:00",
                "hora_fin" => "$salida:59",
                "rango" => $recuperar_horario
            ];
        }
    }
    return $diasYHoras;
}