    
   <?php
    $dias = $_POST['dias'];
    //HACER HAYVIDEO

    $horarioCalculado = [];
    $rango = 1;
    foreach ($dias as $diaSeleccionado) {
        @$activo = $diaSeleccionado['activo'];
        if (!$activo) continue;
        $dia_inicio = $diaSeleccionado['dia_inicio'];
        $dia_fin = $diaSeleccionado['dia_fin'];
        $hora_inicio = $diaSeleccionado['hora_inicio'] . ":00";
        $hora_fin = $diaSeleccionado['hora_fin'] . ":00";
        if ($diaSeleccionado['hora_fin'] == 0) {
            $hora_fin = "23:59";
        } else {
            $hora_fin = ($diaSeleccionado['hora_fin'] - 1) . ":59";
        }
        $horarioCalculado[] = [
            "num_rango" => $rango,
            "dia_inicio" => $dia_inicio,
            "dia_fin" => $dia_fin,
            "hora_inicio" => $hora_inicio,
            "hora_fin" => $hora_fin
        ];
        $rango++;
    }

    $estado = isset($_POST['estado']) ? $_POST['estado']  : 2;
    $nombre = $_POST['nombre'];
    
    $portada_mensaje = $_POST['portada_mensaje'];
    $edad = $_POST['edad'];
    $pais =  empty($_POST['pais']) ? "NULL" : "'{$_POST['pais']}'";
    $pais_label = $_POST['pais_label'] ?? "NULL";
    
    $telefono = $_POST['telefono'];
    $telefono2 = $_POST['telefono2'];
    $hayWhatsapp1 = isset($_POST['hayWhatsapp1']) ? 'TRUE' : 'FALSE';
    $hayWhatsapp2 = isset($_POST['hayWhatsapp2']) ? 'TRUE' : 'FALSE';

    $id_categoria = $_POST['categoria'];
    $id_ciudad = $_POST['ciudad'];
    $hayVideo = isset($_POST['hayVideo']) ? 'TRUE' : 'FALSE';
    $esCasa = isset($_POST['esCasa']) ? 'TRUE' : 'FALSE';
    $salidas = isset($_POST['salidas'])?'TRUE':'FALSE';

    $coords = explode(",", $_POST['coords']);
    $coords = "POINT(" . $coords[1] . "," . $coords[0] . ")";

    $hoy = date('Y-m-d H:i:s');
    $duracion = isset($_POST['duracion']) ? $_POST['duracion'] : 1;
    $fecha_publicacion = isset($_POST['fecha']) ? date_format(date_create($_POST['fecha']), 'Y-m-d H:i:s') : $hoy;
    $fecha_caducidad = addTimeToTimestamp($fecha_publicacion, $duracion, "days");
    $duracion = strtotime($fecha_caducidad) - strtotime($fecha_publicacion);
    if (isset($_GET['id'])) { // Update

        $id = $_GET['id'];
        $estado_actual = $_POST['estado_actual'];
        $id_ciudad_actual = $_POST['id_ciudad_actual'];

        $fecha = isset($_POST['fecha']) ? date_format(date_create($_POST['fecha']), 'Y-m-d H:i:s') : null;

        $fecha_actual = isset($_POST['fecha_actual']) ? $_POST['fecha_actual'] : null;

        $fecha_caducidad = $_POST['fecha_caducidad'];
        /* $duracion_actual=$_POST['duracion_actual']; x */
        /* if ($_POST['extender'] > 0) {
            $duracion = $_POST['extender'];
            $fecha_caducidad = addTimeToTimestamp($fecha_caducidad, $duracion,"days");
        } */

        if ($fecha_actual && $fecha && $fecha_actual != $fecha) {
            $sqlBuscarEvento = "SELECT id FROM eventos WHERE id_anuncio=$id AND estado=true AND tipo='anuncio' AND accion='publicar' ORDER BY fecha_creacion DESC LIMIT 1";
            $id_evento = $pdo->query($sqlBuscarEvento)->fetch()->id;
            $sqlEventos = "UPDATE eventos SET fecha_creacion='$hoy', fecha_ejecucion='$fecha', estado=true  WHERE id=$id_evento";
            $pdo->query($sqlEventos);

            $duracion_actual = $_POST['duracion_actual'];
            $fecha_caducidad = addTimeToTimestamp($fecha, $duracion_actual, "seconds");

            $sqlBuscarEvento = "SELECT id FROM eventos WHERE id_anuncio=$id AND estado=true AND tipo='anuncio' AND accion='despublicar' ORDER BY fecha_creacion DESC LIMIT 1";
            $id_evento = $pdo->query($sqlBuscarEvento)->fetch()->id;
            $sqlEventos = "UPDATE eventos SET fecha_creacion='$hoy', fecha_ejecucion='$fecha_caducidad', estado=true  WHERE id=$id_evento";
            $pdo->query($sqlEventos);
        }
        $fecha_posicionado = "";
        if ($id_ciudad != $id_ciudad_actual) $fecha_posicionado = ",fecha_posicionado='$hoy'";

        $sql = "UPDATE anuncios SET esCasa=$esCasa, portada_mensaje='$portada_mensaje',telefono='$telefono',telefono2='$telefono2',hayWhatsapp1=$hayWhatsapp1, hayWhatsapp2=$hayWhatsapp2, hayVideo=$hayVideo, edad=$edad, salidas=$salidas, gps=$coords, fecha_publicacion='$fecha', fecha_caducidad='$fecha_caducidad', nombre = '$nombre', id_pais = $pais, pais= '$pais_label', id_categoria = $id_categoria, id_ciudad = $id_ciudad $fecha_posicionado WHERE id = $id";
        $pdo->query($sql);
        $sql = "DELETE FROM horarios_anuncios WHERE id_anuncio=$id";
        $pdo->query($sql);
    } else {
        if (strtotime($fecha_publicacion) <= strtotime($hoy)) {
            $estado = 1;
        }
        $portadasJSON = [
            "anuncio" => [],
            "destacado" => []
        ];
        $emptyPortadasJSON = json_encode(
            $portadasJSON
        );
        $sql = "INSERT into anuncios (esCasa,telefono,telefono2,hayWhatsapp1,hayWhatsapp2,hayVideo,nombre,portada_mensaje,id_pais, pais, estado_anuncio, id_categoria,fecha_publicacion, fecha_caducidad, id_ciudad,duracion,fecha_posicionado,portadas,gps,salidas,edad) VALUES($esCasa,'$telefono','$telefono2',$hayWhatsapp1,$hayWhatsapp2,$hayVideo, '$nombre', '$portada_mensaje', $pais,'$pais_label', $estado, $id_categoria,'$fecha_publicacion','$fecha_caducidad', $id_ciudad, '$duracion','$hoy', '$emptyPortadasJSON', $coords,$salidas,$edad)";
        //app\debug($sql);
        $pdo->query($sql);
        $id_anuncio = $pdo->lastInsertId();
        if (strtotime($fecha_publicacion) >= strtotime($hoy)) {
            $sqlEvento = "INSERT INTO eventos VALUES(0,$id_anuncio,'$hoy','$fecha_publicacion','anuncio','publicar',true)";
            $pdo->query($sqlEvento);
        }
        $sqlEvento = "INSERT INTO eventos VALUES(0,$id_anuncio,'$hoy','$fecha_caducidad','anuncio','despublicar',true)";
        $pdo->query($sqlEvento);
    }
    @$id_anuncio = $id ? $id : $id_anuncio;
    foreach ($horarioCalculado as $horario) {
        $num_rango = $horario['num_rango'];
        $dia_inicio = $horario['dia_inicio'];
        $dia_fin = $horario['dia_fin'];
        $hora_inicio = $horario['hora_inicio'];
        $hora_fin = $horario['hora_fin'];
        $sql = "INSERT INTO horarios_anuncios VALUES(0,$id_anuncio,$num_rango,$dia_inicio,$dia_fin,'$hora_inicio','$hora_fin')";

        $pdo->query($sql);
    }
    //app\debug($_FILES);
    /* -----SUBIDA DE LAS FOTOS -------------------------------------- */
    $path = $_SERVER['DOCUMENT_ROOT'] . '/' . app\Config::$uploads . '/fotos/';
    if ($_FILES['fotos']['size'][0] > 0) {

        if (isset($_GET['id'])) {
            $id_anuncio = $_GET['id'];
        }
        $num_files = count($_FILES['fotos']['tmp_name']);
        $sql_ordenar = "UPDATE fotos SET pos=pos + $num_files  WHERE id_anuncio=$id_anuncio";
        $pdo->query($sql_ordenar);
        $files_names = [];
        for ($i = 0; $i < $num_files; $i++) {

            $type = $_FILES['fotos']['type'][$i];
            $size = $_FILES['fotos']['size'][$i];

            if ($type != 'image/jpeg') {
                app\console('Formato de archivo no permitido');
            }

            if ($size > 5000000) { // 5mb = 5000000 | 10mb = 10000000
                app\console('El archivo es demasiado grande');
            }
            $filename = $id_anuncio . '-' . $i . '-' . date('YmdHis') . ".jpg";

            if ($i < 2)
                array_push($files_names, $filename);

            $fullpath = $path . $filename;

            if (move_uploaded_file($_FILES['fotos']['tmp_name'][$i], $fullpath)) {
                app\console("El fichero es válido y se subió con éxito");
            } else {
                app\console("¡Posible error");
            }
            // Subida de todas las fotos a la tabla fotos 

            $sql_fotos = "INSERT INTO fotos VALUES(0, $id_anuncio, '1', '$filename', $i+1, 0) ";

            $pdo->query($sql_fotos);
        }
        actualizarPortadaAnuncio($id_anuncio);
    } // if files

    //titulo
    /* -----SUBIR VIDEO -------------------------------------- */
    $pathVideo = '';
    if ($_FILES['video']['size'] > 0) {
        $pathVideo = $_SERVER['DOCUMENT_ROOT'] . '/' . app\Config::$uploads . '/videos/' . $id_anuncio . ".mp4";
        if (isset($_GET['id'])) {
            $id_anuncio = $_GET['id'];
        }

        $type = $_FILES['video']['type'];
        $size = $_FILES['video']['size'];

        if ($size > 80000000) { // 5mb = 5000000 | 10mb = 10000000
            app\console('El archivo es demasiado grande');
            die();
        }
        if ($type != "video/mp4") {
            app\console('El formato no es permitido');
            echo "Formato de archivo no permitido";
            die();
        }


        if (move_uploaded_file($_FILES['video']['tmp_name'], $pathVideo)) {
            app\console("El fichero es válido y se subió con éxito");
        } else {
            app\console("¡Posible error");
        }
    } // if files

    // Si no hay video crea un string vacio


    $titulo = $_POST['titulo'];
    $descripcion = $_POST['descripcion'];
    $notas = $_POST['notas'];
  
    $notas_admin = $_POST['notas_admin'];
    $servicios = $_POST['servicios'];
    $metodos_pago = $_POST['metodos_pago'] ?? [];

    $servicios_JSON = $servicios;
    $metodos_pago_JSON = json_encode($metodos_pago);

    
    $notas_JSON = json_encode($notas);
    
    if (isset($_GET['id'])) {
        $sql_update_detalles_anuncio = "UPDATE detalles_anuncio SET titulo='$titulo', video='$pathVideo', descripcion='$descripcion', notas_admin='$notas_admin', servicios='$servicios_JSON', metodos_pago= JSON_MERGE_PATCH(metodos_pago,'$metodos_pago_JSON' ) , notas= JSON_MERGE_PATCH(notas,'$notas_JSON' ) WHERE id=$id_anuncio";
        //app\debug($sql_update_detalles_anuncio);    
        $pdo->query($sql_update_detalles_anuncio);

    } else {
        $sql_detalles_anuncio = "INSERT INTO detalles_anuncio VALUES($id_anuncio,'$titulo','$pathVideo','$descripcion','$notas_admin', '$servicios_JSON','$metodos_pago_JSON','$notas_JSON')";

        $pdo->query($sql_detalles_anuncio);


        /*      $enlaces=$_POST['enlaces'];
        
        foreach($enlaces as $enlace){
            if(!$enlace) continue;
            $sql_enlace= "INSERT INTO enlaces_anuncios VALUES(0,$id_anuncio,$enlace)";
            $pdo->query($sql_enlace);
        } */
    }



    pushMessage("Anuncio $id_anuncio creado exitosamente");
    //app\redirect('/anuncios');

    die();
