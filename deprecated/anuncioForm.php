<?php 

$paso = $_GET['paso'];
$siguiente =  $paso < 5 ? "/anuncioForm&paso=". $paso + 1 : false; 
$anterior =  $paso != 0 ? "/anuncioForm&paso=". $paso - 1 : false; 

?>

<form action="/anuncioPost<?= $id ? "&id=$id" : "" ?>" id="formAnuncio" enctype="multipart/form-data" method="POST">


<div id="paso0" class="row <?=$paso == 0 ? '' : 'd-none'?>">
<div class="col-md-12 col-lg-8 offset-lg-2 col-xl-6 offset-xl-3">
                    <label for="notas_admin" class="form-label mt-2">Notas admin: </label>
                    <input type="text" name="notas_admin" id="notas_admin" class="form-control"
                        placeholder="notas_admin" value="<?= $anuncio->notas_admin ?? '' ?>">
                </div>

</div> <!-- paso0 -->

    <div id="paso1" class="row <?=$paso == 1 ? '' : 'd-none'?>">

        <h3 class="text-center"><?= @$anuncio->nombre ? "editando $anuncio->nombre" : 'Nuevo anuncio' ?></h3>

        <!-- 1 columna en pc -->

        <div class="col-md-12 col-lg-8 offset-lg-2 col-xl-6 offset-xl-3">

            <!-- SI NUEVO -->


            <!-- NOMBRE -->
            <div class="row">
                <div class="col-9">
                    <label for="nombre" class="form-label mt-2">Nombre: </label>
                    <input type="text" name="nombre" id="nombre" class="form-control" placeholder="Nombre"
                        value="<?= $anuncio->nombre ?? '' ?>">
                </div>
                <div class="col-3">
                    <!-- EDAD -->
                    <label for="edad" class="form-label mt-2">Edad: </label>
                    <input type="number" min="18" max="99" required name="edad" id="edad" class="form-control"
                        placeholder="edad" value="<?= $anuncio->edad ?? '' ?>">
                </div>
            </div>

       
            <div class="row">
             
                <div class="col"> 
                    <label for="portada_mensaje" class="form-label mt-2">Portada mensaje: </label>
                    <input type="text" name="portada_mensaje" id="portada_mensaje" class="form-control"
                        placeholder="Portada mensaje" value="<?= $anuncio->portada_mensaje ?? '' ?>">
                </div>
            </div> <!-- row -->
            <div class="row">
                <div class="col-6">
                    <!-- -----  CATEGORIA ---- -->
                    <label for="categoria" class="form-label mt-2">Categoria: </label>
                    <select class="form-select" name="categoria" id="categoria">

                        <?php foreach ($categorias as $categoria) : ?>
                        <!-- Edición recuperar categoria del actual anuncio -->
                        <?php if ($id) : ?>
                        <option <?= $categoria->id == $anuncio->id_categoria ? 'selected' : '' ?>
                            value="<?= $categoria->id ?>">
                            <?= $categoria->nombre ?>
                            <?php else : ?>
                            <!-- Nuevo poner por defecto a chica -->
                        <option <?= $categoria->nombre == 'chica' ? 'selected' : '' ?> value="<?= $categoria->id ?>">
                            <?= $categoria->nombre ?>
                            <?php endif ?>
                        </option>
                        <?php endforeach ?>
                    </select>
                </div>
                <div class="col-6">
                    <!-- PAIS -->
                    <label for="pais" class="form-label mt-2">Pais: </label>
                    <div class="d-flex flex-column bd-highlight">
                        <div class="input-group">
                            <input type="text" autofocus="false" class="form-control" id="pais" name="pais_label"
                                placeholder="Escribe tu pais ..." autocomplete="off" aria-describedby="helpId"
                                value="<?= $anuncio->pais ?? '' ?>">
                            <input type="hidden" id="pais_value" name="pais" value="<?= $anuncio->id_pais?? '' ?>" />
                        </div>
                    </div>
                    <!-- <input type="text" name="pais" id="pais" class="form-control" placeholder="pais" aria-describedby="helpId" value="<?= $anuncio->pais ?? '' ?>"> -->

                </div>
            </div>


            <div class="row">
                <div class="col-3">
                    <!-- TELÉFONO -->
                    <label for="telefono" class="form-label mt-2">Teléfonos y Whatsapp: </label>
                    <input type="text" name="telefono" id="telefono" class="form-control" placeholder="Teléfono"
                        aria-describedby="helpId" value="<?= $anuncio->telefono ?? '' ?>">
                </div>
                <div class="col-3 d-flex align-items-end">
                    <div class="wa1" style="line-height: 30px">
                        <input class="form-check-input" style="margin-top: 16px" type="checkbox" name="hayWhatsapp1"
                            value="wa1" id="wa1" <?=@$anuncio->hayWhatsapp1 ? 'checked' : ''?>>
                    </div>


                </div>


                <div class="col-3">
                    <!-- TELÉFONO 2-->
                    <input type="text" name="telefono2" id="telefono2" class="form-control"
                        placeholder="Teléfono alternativo" aria-describedby="helpId"
                        value="<?= $anuncio->telefono2 ?? '' ?>">

                </div>

                <div class="col-3 d-flex align-items-end">
                    <div class="wa2 d-flex">
                        <input class="form-check-input" style="margin-top: 16px" type="checkbox" name="hayWhatsapp2"
                            value="wa2" id="wa2" <?=@$anuncio->hayWhatsapp2 ? 'checked' : ''?>>
                    </div>


                </div>
            </div>



            <div class="row">
                <div class="col-8">
                    <!-- FOTOS -->

                    <label for="fotos" class="form-label mt-2">Agregar fotos</label>
                    <input type="file" class="form-control" name="fotos[]" id="fotos" multiple>
                    <?php if (isset($_GET["update"])) : ?>

                    <?php if (count($fotos) > 0) : ?>
                    <a class="mt-4 btn btn-outline-primary btn-xs" href="<?= '/galeria&id=' . $anuncio->id ?>">Gestionar
                        fotos
                        (<?= count($fotos) ?> encontradas)</a> <br>
                    <?php endif ?>

                    <?php endif ?>
                </div>
                <div class="col-4">
                
                </div>
                <!-- VIDEO -->
                <div class="col-8">
                    <!-- FOTOS -->

                    <label for="video" class="form-label mt-2">Agregar video</label>
                    <input type="file" class="form-control" name="video" id="video">

                </div>
                <div class="col-4 align-self-end pb-1">
                    <div class="form-check form-switch">
                        <label class="form-check-label" for="video-check"> Mostrar video </label>
                        <input class="form-check-input" type="checkbox" id="video-check" name="hayVideo"
                            <?=@$anuncio->hayVideo ? 'checked' : ''?>>

                    </div>
                </div>



                <div class="row mt-2">
                    <div class="col-8">
                        <!-- -------------------- FECHA DE PUBLICACIÓN --------------------------- -->
                        <?php if (!$id or $anuncio->estado_anuncio == 2) : // Es nuevo o programado ?>
                        <label for="fecha form-label" class="form-label"> Seleccione fecha y hora</label>
                        <?php if ($id) : // programado 
                            ?>
                        <input type="datetime-local" class="form-control" name="fecha" id="fecha"
                            value="<?= date_format(date_create($anuncio->fecha_publicacion), "Y-m-d\TH:i") ?? '' ?>">
                        <?php else : // nuevo 
                            ?>
                        <input type="datetime-local" class="form-control" name="fecha" id="fecha"
                            value="<?= date("Y-m-d\TH:i") ?>">
                        <!-- 2022-01-01T00:00:00 -->
                        <?php endif ?>

                        <?php endif ?>
                    </div>

                    <div class="col-4">
                        <!-- ----------- DURACIÓN (En la creación Para caducarlo) -------------------------- -->
                        <?php if (!$id) : // Es nuevo xq no se paso ID ?>

                        <label for="duracion" class="form-label"> Días </label>
                        <input type="number" name="duracion" id="duracion" class="form-control" value="7" required
                            placeholder="duracion" aria-describedby="helpId">

                        <?php endif ?>
                    </div>

                </div> <!-- col -->

                <div class="col-12">

                    <label for="titulo" class="form-label">Título</label>
                    <input type="text" class="form-control" name="titulo" id="titulo" placeholder="Escribe un título"
                        value="<?= isset($anuncio->titulo) ? $anuncio->titulo : ''?>">

                </div>

                <div class="col-12">
                    <label for="descripcion" class="form-label">Texto: </label>
                    <textarea class="form-control" name="descripcion" id="descripcion"
                        rows="3"><?=@$anuncio->descripcion?></textarea>

                </div>




                <!-- Servicios -->

                <div>
                    <label for="servicios" class="form-label">Servicios</label>
                    <textarea class="form-control" name="servicios" id="servicios"
                        rows="2"><?=@$anuncio->servicios?></textarea>
                </div>

                <div class="row mt-3 inline-block" style="max-width: 365px; margin: 0 auto">

                    <div class="col-3 text-center">

                        <label for="salidas" class="form-label">Salidas</label>
                        <div class="form-check d-flex justify-content-center">
                            <input class="form-check-input" type="checkbox" name="salidas" value="salidas" id="salidas"
                                <?=@$anuncio->salidas ? 'checked' : ''?>>
                        </div>
                    </div>

                    <div class="col-3 text-center">

                        <label for="esCasa" class="form-label">Casa</label>
                        <div class="form-check d-flex justify-content-center">
                            <input class="form-check-input" type="checkbox" name="esCasa" value="esCasa" id="esCasa"
                                <?=@$anuncio->esCasa ? 'checked' : ''?>>
                        </div>

                    </div>
                    <div class="col-3 text-center">

                        <label for="visa" class="form-label">VISA</label>
                        <div class="form-check d-flex justify-content-center">
                            <input class="form-check-input" type="checkbox" name="metodos_pago[visa]" value="true"
                                id="visa" <?= isset($metodos_pago['visa']) ? 'checked' : ''?>>

                        </div>

                    </div>

                    <div class="col-3 text-center">

                        <label for="bizum" class="form-label">Bizum</label>
                        <div class="form-check d-flex justify-content-center">
                            <input class="form-check-input" type="checkbox" name="metodos_pago[bizum]" value="true"
                                id="bizum" <?= isset($metodos_pago['bizum']) ? 'checked' : ''?>>
                        </div>

                    </div>
                </div>





            </div> <!-- row interno -->

        </div> <!-- ./ col responsive 1 columna -->

        <!-- 2 columna en pc -->
        <div class="col-xxl-4 g-1 justify-content-center">


            <!-- ----------- DURACIÓN (Editar para extender dias) -------------------------- -->

            <?php if ($id) : // Editar 
        ?>

            <input type="hidden" name="fecha_caducidad" id="fecha_caducidad" class="form-control"
                placeholder="fecha_caducidad" value="<?= $anuncio->fecha_caducidad ?>">
            <input type="hidden" name="id_ciudad_actual" id="id_ciudad_actual" value="<?= $anuncio->id_ciudad ?>">
            <input type="hidden" name="duracion_actual" id="duracion_actual" value="<?= $anuncio->duracion ?>">
            <input type="hidden" name="estado_actual" id="estado_actual" value="<?= $anuncio->estado_anuncio ?>">
            <input type="hidden" name="fecha_actual" id="fecha_actual" value="<?= $anuncio->fecha_publicacion ?>">

            <?php endif ?>
        </div> <!-- col -->

    </div> <!-- PASO 1 -->

    <!-- ----------- HORARIO 1 -------------------------- -->

    <div id="paso3 " class="row horarios text-center <?= isset($_GET['paso3']) ? '' : 'd-none'?> ">
        <h5 class="mt-2 text-center">Horario de trabajo</h5>

        <span>Rango 1</span>
        <div class="col-12 col-xxl-7 text-center text-xxl-end">

            <input type="checkbox" class="form-check-input" name="dias[0][activo]" value="true" checked="checked"
                style="pointer-events:none">

            <!------------------------------------------------------------------  rango 1 los dias -->
            <select class='form-select d-inline-block' name='dias[0][dia_inicio]' style='width:120px'>
                <option value='0' <?= @$rango1[0] == '0' ? 'selected' : "" ?>> Lunes</option>
                <option value='1' <?= @$rango1[0] == '1' ? 'selected' : "" ?>> Martes</option>
                <option value='2' <?= @$rango1[0] == '2' ? 'selected' : "" ?>> Miercoles</option>
                <option value='3' <?= @$rango1[0] == '3' ? 'selected' : "" ?>> Jueves</option>
                <option value='4' <?= @$rango1[0] == '4' ? 'selected' : "" ?>> Viernes</option>
                <option value='5' <?= @$rango1[0] == '5' ? 'selected' : "" ?>> Sabado</option>
                <option value='6' <?= @$rango1[0] == '6' ? 'selected' : "" ?>> Domingo</option>
            </select>




            A:
            <select class='form-select  d-inline-block' name='dias[0][dia_fin]' style='width:120px'>
                <option value='0' <?= @$rango1[1] == '0' ? 'selected' : "" ?>> Lunes</option>
                <option value='1' <?= @$rango1[1] == '1' ? 'selected' : "" ?>> Martes</option>
                <option value='2' <?= @$rango1[1] == '2' ? 'selected' : "" ?>> Miercoles</option>
                <option value='3' <?= @$rango1[1] == '3' ? 'selected' : "" ?>> Jueves</option>
                <option value='4' <?= @$rango1[1] == '4' ? 'selected' : "" ?>> Viernes</option>
                <option value='5' <?= @$rango1[1] == '5' ? 'selected' : "" ?>> Sabado</option>
                <option value='6' <?= @$rango1[1] == '6' || !isset($rango1) ? 'selected' : "" ?>> Domingo
                </option>
            </select>

        </div><!-- ./rango 1 dias -->

        <!--------------------------------------------------------------  Rango 1 HORAS -->
        <div class="col-12 col-xxl-5  text-center text-xxl-start">

            De:<input class='form-control d-inline-block' type="number" min="0" max="23" name='dias[0][hora_inicio]'
                value='<?= @$rango1[2] ?? 0 ?>' style='width:70px; text-align:center'>
            A:<input class='form-control ms-1 me-2 d-inline-block' type="number" min="0" max="23"
                name='dias[0][hora_fin]' value='<?= @$rango1[3] ?? 0 ?>' style='width:70px; text-align:center'>

        </div>






        <div class="row horarios text-center">
            <span>Rango 2</span>
            <div class="col-12 col-xxl-7 text-center text-xxl-end">


                <input type="checkbox" class="form-check-input" name="dias[1][activo]" value="true"
                    <?=@$rango2 ? "checked" : ""?>>

                <select class='form-select d-inline-block' name='dias[1][dia_inicio]' style='width:120px'>
                    <option value='0' <?= @$rango2[0] == '0' ? 'selected' : "" ?>> Lunes</option>
                    <option value='1' <?= @$rango2[0] == '1' ? 'selected' : "" ?>> Martes</option>
                    <option value='2' <?= @$rango2[0] == '2' ? 'selected' : "" ?>> Miercoles</option>
                    <option value='3' <?= @$rango2[0] == '3' ? 'selected' : "" ?>> Jueves</option>
                    <option value='4' <?= @$rango2[0] == '4' ? 'selected' : "" ?>> Viernes</option>
                    <option value='5' <?= @$rango2[0] == '5' ? 'selected' : "" ?>> Sabado</option>
                    <option value='6' <?= @$rango2[0] == '6' ? 'selected' : "" ?>> Domingo</option>
                </select>




                A:
                <select class='form-select  d-inline-block' name='dias[1][dia_fin]' style='width:120px'>
                    <option value='0' <?= @$rango2[1] == '0' ? 'selected' : "" ?>> Lunes</option>
                    <option value='1' <?= @$rango2[1] == '1' ? 'selected' : "" ?>> Martes</option>
                    <option value='2' <?= @$rango2[1] == '2' ? 'selected' : "" ?>> Miercoles</option>
                    <option value='3' <?= @$rango2[1] == '3' ? 'selected' : "" ?>> Jueves</option>
                    <option value='4' <?= @$rango2[1] == '4' ? 'selected' : "" ?>> Viernes</option>
                    <option value='5' <?= @$rango2[1] == '5' ? 'selected' : "" ?>> Sabado</option>
                    <option value='6' <?= @$rango2[1] == '6' || !isset($rango1) ? 'selected' : "" ?>> Domingo
                    </option>
                </select>

            </div><!-- rango 1 dias -->

            <div class="col-12 col-xxl-5  text-center text-xxl-start">
                <!-- rango 1 parte 2 -->

                <label class="form-label my-3 my-xxl-0" for="desdelas1">De: </label>
                <input class='form-control d-inline-block' id="desdelas1" type="number" min="0" max="23"
                    name='dias[1][hora_inicio]' value='<?= @$rango2[2] ?? 0 ?>' style='width:70px; text-align:center'>
                A:<input class='form-control ms-1 me-2 d-inline-block' type="number" min="0" max="23"
                    name='dias[1][hora_fin]' value='<?= @$rango2[3] ?? 0 ?>' style='width:70px; text-align:center'>

            </div>

        </div>



        <div class="row horarios text-center">
            <span>Rango 3</span>
            <div class="col-12 col-xxl-7 text-center text-xxl-end">

                <!-- checkbox activo -->
                <input type="checkbox" class="form-check-input" name="dias[2][activo]" value="true"
                    <?=@$rango3 ? "checked" : ""?>>

                <!-- dias -->
                <select class='form-select d-inline-block' name='dias[2][dia_inicio]' style='width:120px'>
                    <option value='0' <?= @$rango3[0] == '0' ? 'selected' : "" ?>> Lunes</option>
                    <option value='1' <?= @$rango3[0] == '1' ? 'selected' : "" ?>> Martes</option>
                    <option value='2' <?= @$rango3[0] == '2' ? 'selected' : "" ?>> Miercoles</option>
                    <option value='3' <?= @$rango3[0] == '3' ? 'selected' : "" ?>> Jueves</option>
                    <option value='4' <?= @$rango3[0] == '4' ? 'selected' : "" ?>> Viernes</option>
                    <option value='5' <?= @$rango3[0] == '5' ? 'selected' : "" ?>> Sabado</option>
                    <option value='6' <?= @$rango3[0] == '6' ? 'selected' : "" ?>> Domingo</option>
                </select>


                A: <select class='form-select  d-inline-block' name='dias[2][dia_fin]' style='width:120px'>
                    <option value='0' <?= @$rango3[1] == '0' ? 'selected' : "" ?>> Lunes</option>
                    <option value='1' <?= @$rango3[1] == '1' ? 'selected' : "" ?>> Martes</option>
                    <option value='2' <?= @$rango3[1] == '2' ? 'selected' : "" ?>> Miercoles</option>
                    <option value='3' <?= @$rango3[1] == '3' ? 'selected' : "" ?>> Jueves</option>
                    <option value='4' <?= @$rango3[1] == '4' ? 'selected' : "" ?>> Viernes</option>
                    <option value='5' <?= @$rango3[1] == '5' ? 'selected' : "" ?>> Sabado</option>
                    <option value='6' <?= @$rango3[1] == '6' || !isset($rango3) ? 'selected' : "" ?>> Domingo
                    </option>
                </select>

            </div><!-- rango 3 dias -->

            <div class="col-12 col-xxl-5  text-center text-xxl-start">
                <!-- rango 3 HORAS 2 -->

                De:<input class='form-control d-inline-block' type="number" min="0" max="23" name='dias[2][hora_inicio]'
                    value='<?= @$rango3[2] ?? 0 ?>' style='width:70px; text-align:center'>
                A:<input class='form-control ms-1 me-2 d-inline-block' type="number" min="0" max="23"
                    name='dias[2][hora_fin]' value='<?= @$rango3[3] ?? 0 ?>' style='width:70px; text-align:center'>

            </div>

        </div>

        <!-- <h5 class="text-center">Enlazar con otro anuncio </h5>

        <div class="row enlaces"> 


            <div class="col-3">
                <label for="enlace1" class="form-label">ID enlace1:</label>
                <input type="number" class="form-control" name="enlaces[0]" id="enlace1">
            </div>
 

            <div class="col-3">
                <label for="enlace2" class="form-label">ID enlace 2:</label>
                <input type="number" class="form-control" name="enlaces[1]" id="enlace2">
            </div>
 

            <div class="col-3">
                <label for="enlace3" class="form-label">ID enlace 3:</label>
                <input type="number" class="form-control" name="enlaces[2]" id="enlace3">
            </div>
 

            <div class="col-3">
                <label for="enlace4" class="form-label">ID enlace4:</label>
                <input type="number" class="form-control" name="enlaces[3]" id="enlace4">
            </div> 

        </div> -->


        <h5 class="text-center">Notas</h5>
        <div class="notas">

            <div class="row">


                <input type="hidden" name="notas[0][id]" />


                <div class="col-3">

                    <span>Icono :</span>
                    <select class="form-control" name="notas[0][tipo]">
                        <option value='si' <?= @$notas[0]['tipo'] == 'si' ? 'selected' : "" ?>> Si</option>
                        <option value='no' <?= @$notas[0]['tipo'] == 'no' ? 'selected' : "" ?>> No</option>
                        <option value='info' <?= @$notas[0]['tipo'] == 'info' ? 'selected' : "" ?>> Info
                        </option>
                    </select>
                </div>
                <div class="col-9">
                    <span>Contenido :</span>
                    <input type="text" class="form-control" name="notas[0][contenido]"
                        placeholder="Contenido para la nota 1" value="<?=@$notas[0]['contenido']?>">
                </div>
            </div>

            <div class="row">

                <input type="hidden" name="notas[1][id]" />

                <div class="col-3">

                    <span>Icono :</span>
                    <select class="form-control" name="notas[1][tipo]">
                        <option value='si' <?= @$notas[1]['tipo'] == 'si' ? 'selected' : "" ?>> Si</option>
                        <option value='no' <?= @$notas[1]['tipo'] == 'no' ? 'selected' : "" ?>> No</option>
                        <option value='info' <?= @$notas[1]['tipo'] == 'info' ? 'selected' : "" ?>> Info
                        </option>
                    </select>
                </div>
                <div class="col-9">
                    <span>Contenido :</span>
                    <input type="text" class="form-control" name="notas[1][contenido]"
                        placeholder="Contenido para la nota 2" value="<?=@$notas[1]['contenido']?>">

                </div>
            </div>

            <div class="row">

                <input type="hidden" name="notas[2][id]" />

                <div class="col-3">

                    <span>Icono :</span>
                    <select class="form-control" name="notas[2][tipo]">
                        <option value='si' <?= @$notas[2]['tipo'] == 'si' ? 'selected' : "" ?>> Si</option>
                        <option value='no' <?= @$notas[2]['tipo'] == 'no' ? 'selected' : "" ?>> No</option>
                        <option value='info' <?= @$notas[2]['tipo'] == 'info' ? 'selected' : "" ?>> Info
                        </option>
                    </select>
                </div>
                <div class="col-9">
                    <span>Contenido :</span>
                    <input type="text" class="form-control" name="notas[2][contenido]"
                        placeholder="Contenido para la nota 3" value="<?=@$notas[2]['contenido']?>">

                </div>
            </div>

            <div class="row">

                <input type="hidden" name="notas[3][id]" />

                <div class="col-3">

                    <span>Icono :</span>
                    <select class="form-control" name="notas[3][tipo]">
                        <option value='si' <?= @$notas[3]['tipo'] == 'si' ? 'selected' : "" ?>> Si</option>
                        <option value='no' <?= @$notas[3]['tipo'] == 'no' ? 'selected' : "" ?>> No</option>
                        <option value='info' <?= @$notas[3]['tipo'] == 'info' ? 'selected' : "" ?>> Info
                        </option>
                    </select>
                </div>
                <div class="col-9">
                    <span>Contenido :</span>
                    <input type="text" class="form-control" name="notas[3][contenido]"
                        placeholder="Contenido para la nota 4" value="<?=@$notas[3]['contenido']?>">

                </div>
            </div>



        </div>




    </div> <!-- paso2 -->

    <!-- 3 columna -->

    <div id="paso2" class="col-12 col-lg-6 offset-lg-3 <?= isset($_GET['paso2']) ? '' : 'd-none' ?>">



        <div class="col">
                <!-- ----------- CIUDAD -------------------------- -->
                    <label for="ciudad" class="form-label mt-2">Ciudad: </label>
                    <select class="form-select" name="ciudad" id="ciudad">

                        <?php foreach ($ciudades as $ciudad) : ?>
                        <option <?= $ciudad->nombre == 'navarra' ? 'selected' : '' ?> value="<?= $ciudad->id ?>">
                            <?= $ciudad->nombre ?>
                        </option>
                        <?php endforeach ?>

                    </select>
 

            <!-- MAPA -->

            <label for="dirección" class="form-label">Dirección: </label>
            <input type="text" class="form-control" name="direccion" id="direccion" placeholder="Escribe dirección">
            <label for="coords" class="form-label">Coordenadas: </label>
            <input class="form-control" id="coords" name="coords" type="text"> <br>

            <div class="row justify-content-center">
                <div class="col text-center">
                    <div id="map" style="width:100%;height:500px"></div>
                </div>
            </div>



        </div> <!-- cierre 3 columna col responsive -->

       

    </div> <!-- paso3 -->

 <!-- SUBMIT -->
 <div class="row justify-content-center">

<div class="col text-center">
    <button type="submit" value="Submit" class="mt-3 mb-3 btn btn-success btn-lg">Guardar</button>
    <a type="button" href="/anuncios" class="mt-3 mb-3 btn btn-danger btn-lg">Cancelar y volver</a>
    <?php if($anterior):?>
    <a type="button" class="btn btn-info btn-lg" href="<?=$anterior?>">Atrás</a>
    <?php endif; ?>
    <?php if($siguiente):?>
    <a type="button" class="btn btn-info btn-lg" href="<?=$siguiente?>">Continuar</a>
    <?php endif; ?>

   

</div>

</div>


</form>


<!-- PAISES -->
<script>
const paises = <?= $paisesJS ?>;
const field = document.getElementById('pais');
let isClicked = false

const ac = new Autocomplete(field, {
    data: [{
        label: "I'm a label",
        value: 42
    }],
    maximumItems: 5,
    threshold: 1,
    onSelectItem: ({
        label,
        value
    }) => {
        $pais_value = document.getElementById("pais_value");
        $pais_value.setAttribute("value", value);

    }
});

// perder foco con click en body
ac.setData(paises)

document.getElementById("nombre").focus()

field.addEventListener("blur", () => {
    esPaisValido = paises.some((element) => element.label === field.value);
    if (!esPaisValido) {
        field.value = ""
        $pais_value = document.getElementById("pais_value");
        $pais_value.setAttribute("value", '');
    }

    //isClicked=false;

})

</script>


<!-- MAPA -->
<script>
let current_gps = <?= $current_gps ?>;
let map;
let marker;
let autocomplete;
const initMap = async () => {

    const placeMarker = (position) => {
        if (marker == null) {
            marker = new google.maps.Marker({
                position,
                map
            });
        } else {
            marker.setPosition(position)
        }
    }
    const $direccion = document.getElementById('direccion');
    const options = {
        componentRestrictions: {
            country: "es"
        },
        fields: ["address_components", "geometry", "icon", "name"],
        strictBounds: false
    }
    const $divMap = document.getElementById("map");
    map = new google.maps.Map($divMap, {
        center: current_gps,
        zoom: 12
    })

    const geocodeLatLng = async (latlng) => {
        const geocoder = new google.maps.Geocoder();
        const response = await geocoder.geocode({
            location: latlng
        });
        if (response.results[0]) {
            return response.results[0].formatted_address;
        }
        return "No se encontró ubicación";
    }
    map.addListener('click', async (e) => {

        const position = e.latLng;
        const {
            lat,
            lng
        } = position;
        const positionJSON = {
            lat: lat(),
            lng: lng()
        };
        const positionText = `${lat()},${lng()}`
        const $coords = document.getElementById('coords');
        console.log(positionJSON);
        const direccion = await geocodeLatLng(positionJSON);
        $direccion.value = direccion;
        $coords.value = positionText;
        placeMarker(position)
    })
    placeMarker(current_gps);
    const direccion = await geocodeLatLng(current_gps);
    $direccion.value = direccion;

    const positionText = `${current_gps.lat},${current_gps.lng}`
    const $coords = document.getElementById('coords');
    $coords.value = positionText;
    autocomplete = new google.maps.places.Autocomplete($direccion, options)
    autocomplete.addListener("place_changed", () => {
        const place = autocomplete.getPlace();
        const position = place.geometry.location;
        const {
            lat,
            lng
        } = position;
        const positionJSON = `${lat()},${lng()}`
        console.log(positionJSON);
        placeMarker(position);
        map.setCenter(position);
        map.setZoom(17);
        const $coords = document.getElementById('coords');
        $coords.value = positionJSON;
    })

}
window.initMap = initMap
</script>