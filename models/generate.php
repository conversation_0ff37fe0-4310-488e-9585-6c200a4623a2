#!/usr/bin/env php
<?php
$host = 'dev.citas10.net';
$dbname = 'dev.citas10';
$username = 'citas10';
$password = 'lean1#4everNB@4710';

// Ensure the script is running within the 'models' directory
if (basename(__DIR__) !== 'models') {
    echo "Error: This script must be run from within the 'models' directory.\n";
    exit(1);
}

// Delete all .php files in the current directory that start with an uppercase letter
foreach (glob(__DIR__ . "/*.php") as $file) {
    if (ctype_upper(basename($file)[0])) {
        unlink($file);
    }
}

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    generateModelClasses($pdo);
    echo "Model classes generated successfully.\n";
} catch (PDOException $e) {
    echo "Connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

function generateModelClasses($pdo) {
    $modelsDir = __DIR__;
    $modelsFile = "$modelsDir/Models.php";
    $modelsContent = "<?php\n\n";

    // Get the list of tables
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

    foreach ($tables as $table) {
        // Get the columns and their data types
        $columns = $pdo->query("SHOW COLUMNS FROM $table")->fetchAll(PDO::FETCH_ASSOC);

        // Use the table name as the class name
        $className = ucfirst($table);

        // Start building the class content
        $classContent = "<?php\n\n";
        $classContent .= "class $className {\n";

        foreach ($columns as $column) {
            $propertyName = $column['Field'];
            $propertyType = mapColumnTypeToPhpType($column['Type']);
            $classContent .= "    public $propertyType \$$propertyName;\n";
        }

        $classContent .= "}\n";

        // Write the class to a separate file
        file_put_contents("$modelsDir/$className.php", $classContent);

        // Add include statement to Models.php
        $modelsContent .= "include_once '$className.php';\n";
    }

    // Write the Models.php file
    file_put_contents($modelsFile, $modelsContent);
}

function mapColumnTypeToPhpType($columnType) {
    if (strpos($columnType, 'int') !== false) {
        return 'int';
    } elseif (strpos($columnType, 'float') !== false || strpos($columnType, 'double') !== false || strpos($columnType, 'decimal') !== false) {
        return 'float';
    } elseif (strpos($columnType, 'bool') !== false) {
        return 'bool';
    } else {
        return 'string';
    }
}