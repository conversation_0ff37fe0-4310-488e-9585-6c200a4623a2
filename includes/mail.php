<?php

    // Email
// Librería de email
//Import PHPMailer classes into the global namespace
//These must be at the top of your script, not inside a function
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use P<PERSON><PERSON>ailer\PHPMailer\SMTP;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\Exception; 

//Create an instance; passing `true` enables exceptions
$mail = new PHPMailer(true);


/**------------------------------------------------------------------------------------------------
 *         NOTIFICACIONES POR EMAIL
 *------------------------------------------------------------------------------------------------**/
 

 function sendMail($asunto, $mensaje, $destino)
 { 
 global $mail;   
   try {
     //Server settings
     // $mail->SMTPDebug = SMTP::DEBUG_SERVER;                    //Enable verbose debug output
     $mail->isSMTP();                                            //Send using SMTP
     $mail->Host       = 'smtp.ionos.es';                         //Set the SMTP server to send through
     $mail->SMTPAuth   = true;                                   //Enable SMTP authentication
     $mail->Username   = '<EMAIL>';                //SMTP username
     $mail->Password   = 'lean1!4everNB20';                      //SMTP password
     $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;          //Enable implicit TLS encryption
     $mail->Port       = 587;                                    //TCP port to connect to; use 587 if you have set `SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS`
     // Activo condificacción utf-8
     $mail->CharSet = 'UTF-8';
     //Recipients
     $mail->setFrom('<EMAIL>', 'Citas10');   //Add a recipient
     $mail->addAddress($destino);
 
     $mail->isHTML(true); //Set email format to HTML
     $mail->Subject = $asunto;
     $mail->Body    = $mensaje;
 
     $mail->send();
   } catch (Exception $e) {
     //echo "Message could not be sent. Mailer Error: {$mail->ErrorInfo}";
   }
 }

 


 
 function sendMail2($asunto, $mensaje, $destino)
 { 
 global $mail;   
   try {
     //Server settings
     // $mail->SMTPDebug = SMTP::DEBUG_SERVER;                    //Enable verbose debug output
     $mail->isSMTP();                                            //Send using SMTP
     $mail->Host       = 'smtp.ionos.es';                         //Set the SMTP server to send through
     $mail->SMTPAuth   = true;                                   //Enable SMTP authentication
     $mail->Username   = '<EMAIL>';                //SMTP username
     $mail->Password   = 'lean1!4everNB20';                      //SMTP password
     $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;          //Enable implicit TLS encryption
     $mail->Port       = 587;                                    //TCP port to connect to; use 587 if you have set `SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS`
     // Activo condificacción utf-8
     $mail->CharSet = 'UTF-8';
     //Recipients
     $mail->setFrom('<EMAIL>');   //Add a recipient
     $mail->addAddress($destino);
 
     $mail->isHTML(true); //Set email format to HTML
     $mail->Subject = $asunto;
     $mail->Body    = $mensaje;
 
     $mail->send();
   } catch (Exception $e) {
     //echo "Message could not be sent. Mailer Error: {$mail->ErrorInfo}";
   }
 }

 
 