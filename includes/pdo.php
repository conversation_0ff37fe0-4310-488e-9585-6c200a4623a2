<?php
global $debugbar;

 $pdo_options = [
    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES 'utf8mb4'", // especificar un comando inicial para la conexión.
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_OBJ, //establecer el formato en que se devuelven los datos
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
   // PDO::ATTR_PERSISTENT => true
  ];
 
  if ( Flex()->dev ){
      $db_name = "dev.citas10"; // cambia para dev
      $user_db = "citas10"; 
      $host = PHP_OS == "WINNT" ? "dev.citas10.net" : "localhost";
      $pass_db = "lean1#4everNB@4710";  
  }else{
    $db_name = "citas10"; // cambia para producción
    $user_db = "citas10"; 
    $host = "localhost";
    $pass_db = "lean1#4everNB@4710"; 
  }
  
  $dsn = "mysql:host=$host;dbname=$db_name;charset=utf8mb4";

try {
    $pdo = new DebugBar\DataCollector\PDO\TraceablePDO(new PDO($dsn, $user_db, $pass_db, $pdo_options));

    // $pdo = new DebugBar\DataCollector\PDO\TraceablePDO(  new Replicate( dsn($s1), $s1['user'], $s1['pass'], $pdo_options));

    $debugbar->addCollector(new DebugBar\DataCollector\PDO\PDOCollector($pdo));

} catch (Exception $e) {
    if( isset($_SESSION['adm']) || flex()->user->rol == 1) $debugbar['exceptions']->addException($e);
}