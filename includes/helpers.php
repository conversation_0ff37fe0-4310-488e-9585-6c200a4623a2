<?php


function resizeImage($sourceImage, $targetImage, $maxWidth, $maxHeight, $quality = 80)
{
    // Obtain image from given source file.
    if (!$image = @imagecreatefromjpeg($sourceImage))
    {
        return false;
    }

    // Get dimensions of source image.
    list($origWidth, $origHeight) = getimagesize($sourceImage);

    if ($maxWidth == 0)
    {
        $maxWidth  = $origWidth;
    }

    if ($maxHeight == 0)
    {
        $maxHeight = $origHeight;
    }

    // Calculate ratio of desired maximum sizes and original sizes.
    $widthRatio = $maxWidth / $origWidth;
    $heightRatio = $maxHeight / $origHeight;

    // Ratio used for calculating new image dimensions.
    $ratio = min($widthRatio, $heightRatio);

    // Calculate new image dimensions.
    $newWidth  = (int)$origWidth  * $ratio;
    $newHeight = (int)$origHeight * $ratio;

    // Create final image with new dimensions.
    $newImage = imagecreatetruecolor($newWidth, $newHeight);
    imagecopyresampled($newImage, $image, 0, 0, 0, 0, $newWidth, $newHeight, $origWidth, $origHeight);
    imagejpeg($newImage, $targetImage, $quality);

    // Free up the memory.
    imagedestroy($image);
    imagedestroy($newImage);

    return true;
}


// delete all files and sub-folders from a folder
function deleteAll($dir) {

    foreach(glob($dir . '/*') as $file) {
        if(is_dir($file))
            deleteAll($file);
        else
            unlink($file);
    }
        
    rmdir($dir);

  }
 

  function pushMessage($msg, $tipo = 'success')
  {
      if (!flex()->is_page) return;
      array_push($_SESSION["sweetalert"], ["message" => $msg, "type" => $tipo]);
  }
   
  if( // ----------------------- MENSAJES -------------------------------
  !isset($_SESSION["sweetalert"]) ){
      $_SESSION["sweetalert"] = [];
  } 
  
  function showAlerts()
  {
      // tipo = error para mostrar icono error
    //  if (count($_SESSION["sweetalert"]) == 0) return;
  
      foreach ($_SESSION["sweetalert"] as $alert) {
          $msg = $alert['message'];
          $tipo = $alert['type'];
          echo "
      <script>
      (async ()=>{
          await Swal.fire({
              html: '$msg',
              toast: true,
              timer: 1500,
              position: 'top-end',
              showConfirmButton: false,
              icon: '$tipo'}
          )})();
      </script>
      ";
      }
   
      $_SESSION["sweetalert"] = [];
  }
  

  function addTimeToTimestamp($fecha, $tiempo, $medida)
  { 
      $otrafecha = date_create_from_format("Y-m-d H:i:s", $fecha);
      if ($tiempo >= 0) {
          $nuevafecha = date_add($otrafecha, date_interval_create_from_date_string("$tiempo $medida"));
      } else {
          $nuevafecha = date_sub($otrafecha, date_interval_create_from_date_string("$tiempo $medida"));
      } 
      $nuevafecha = $nuevafecha->format("Y-m-d H:i:s"); 
      return $nuevafecha;
  } 
  function fechaSimple($str)
  {
      global $meses;
      $date = date_create($str);
      echo date_format($date, "d");
      echo " de ";
      $mes_actual = date_format($date, "n");
      echo $meses[$mes_actual - 1];
      echo " a las ";
      echo date_format($date, "H:i") . "H";
  } 
  
  function seconds2human($ss)
  { 
      if ($ss < 0) {
          return 'Caducado';
      } 
      $m = floor(($ss % 3600) / 60);
      $h = floor(($ss % 86400) / 3600);
      $d = floor(($ss % 2592000) / 86400);
      return "$d días, $h h, $m min";
  } 
   
  function seconds2humanHoursOnly($ss){
      $abs_secs=abs($ss);
      if(abs($abs_secs)<3600){
          $m = floor(($abs_secs % 3600) / 60);
          return "$m min";
      }
      $h = floor(($abs_secs % 86400) / 3600);
      return "$h H";
  }
   
  function getHoursForCover($d_inicio,$d_fin,$h_inicio,$h_fin){ 
      $hora_inicio= intval(substr($h_inicio,0,2));
      $hora_fin= intval(substr($h_fin,0,2)); 
      $hora_fin= $hora_fin + 1; 
      if($hora_fin==24){
          $hora_fin= 0;
      } 
      $lunes_domingo = $d_inicio==0 && $d_fin==6 ? true : false;
      if($lunes_domingo && $hora_inicio == 0 & $hora_fin == 0){
          return "24 / 7";
      } 
      if($hora_inicio==0 && $hora_fin==0){
          return "Hoy 24H";
      } 
      if($hora_fin<=9){
          $hora_fin= "0".$hora_fin;
      }
       
      return $hora_inicio . "H a " . $hora_fin . "H"; 
      
  }
  /* Aun no se esta usando, la idea era agregar a mensajes la fecha de su emisión */
   
  function conTime($value, $grupo = 'debug', ){
       
      $date = date("d_H:i:s");
      if ( is_array($value) ){ 
          $key = key($value);
          return [ $grupo=>[$key=> [$date.": ".$value ]] ];
      }else{
          return [ $grupo=>[$date .": ". $value]];
      }
      
  } 
   // sessiondebug(["LLAVE"=>["a"=>[1,2,3]]]);
  
   //Extraer la info de la DB en formato gps
  
  function getGps($value){
      $current_gps = $value;
      $coordinates = unpack('x/x/x/x/corder/Ltype/dlat/dlng', $current_gps);
      $current_gps = json_encode([
          "lat" => $coordinates['lng'],
          "lng" => $coordinates['lat']
      ]);
  
      return $current_gps;
  }
  
  
// funcion de utilidad usada en la funcion getHorario($opt)
 
function convertirHorario($horario)
{
    $partes = explode(":", $horario);
    $partes[0] = intval($partes[0]);
    $partes[1] = intval($partes[1]);
    if ($partes[1] == 59) {
        if ($partes[0] == 23) {
            $partes[0] = 0;
        } else {
            $partes[0] = $partes[0] + 1;
        }
    }
    return $partes[0];
    //"23:59:00"
}


  
function msgbar($value){
    // if ( app\dev() ) {
         global $debugbar;
         $debugbar["messages"]->addMessage($value);
    // } 
 }
  
 /* MUY IMPORTANTE: Los argumentos JSON por url para debug deben pasarse con dobles comillas */
 if(!isset($_SESSION["logbar"])){
     $_SESSION["logbar"] = [];
 } 
  
 if (!isset($_SESSION["logN"])){
     $_SESSION["logN"] = 1;
 };
 
 function sessionDestroy() {
    session_destroy();
    return true;
 
 }
  
 function removeSession($name = []){ 
 
     $name = $name["name"];
     $_SESSION[$name] = [];
     
     
 }
  
 

if( !isset($_SESSION["logg"] ) ) $_SESSION["logg"] = [];
if( !isset($_SESSION["loggN"] )) $_SESSION["loggN"] = 1;
// krsort($_SESSION["logg"]); 



function redirectLogin(){

    // Solo las páginas no citadas anteriormente llegaran a esta parte

    if ( Flex()->zone == "adm") {

        if (!isset($_SESSION['adm']) || flex()->user->rol <= 10) app\redirect("/adm/login");

        if (!isset($_SESSION['adm']) || flex()->user->rol <= 5) app\redirect("/panel/entrar");
    }

}
 


function cerrarSesion(){
    $_SESSION['adm'] = false;
    session_destroy();
    flex()->redirect("/inicio/"); 
 }


 
function logg($key, $value){ 

    $_SESSION["logg"][$key] = $value;  
   // krsort($_SESSION["logbar"]);  
}

// Usada para redirigir mediante &fun=redirect(url:/panel/galeria/147/@step=2@edit)
function redirect($opt = []){
     
    $url = isset($opt['url']) ? $opt['url'] : $_SERVER['HTTP_REFERER'];
    $url = str_replace("@","&",$url); 

    flex()->redirect($url); 
}

 
function suma($opt = []){  
    $data = $opt['a'] + $opt['b'];
    flex()->user->suma = $data; 
    echo $data;
}


function suma2($opt = []){
    if (flex()->user->rol < 6) die("No tienes permisos para ejecutar esta funcion"); 
    $data = $opt['a'] + $opt['b'];
    flex()->user->suma2 = $data; 
    echo $data;
}