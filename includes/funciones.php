 <?php 
 
 
 // ESTO ES CUANDO SE PROGRAMA UN ANUNCIO PARA SER PUBLICADO
 function cambiarEstado($tipo, $accion, $id)
 {
 
     global $pdo;
     $sql = "SELECT * FROM anuncios WHERE ID=$id";
     $hoy = date('Y-m-d H:i:s');
 
     //  $posActual = $anuncioseleccionado->pos;
     if ($tipo == 'anuncio') {
         if ($accion == 'publicar') {
             //crearPrimeraPosicion($id_anuncio, $id_ciudad);
             $sql = "UPDATE anuncios SET estado_anuncio=1 WHERE ID=$id"; 
         } else { //caducar anuncio
             $sql = "UPDATE anuncios SET estado_anuncio=0 WHERE ID=$id";
             // quitarPosicion($id_ciudad, $posActual);
             $sql_duracion = "UPDATE anuncios SET duracion=NULL WHERE id=$id";
             $pdo->query($sql_duracion);
         }
     } else { // destacado
         if ($accion == "publicar") {
             //crearPrimeraPosicionDestacado($id_anuncio, $id_ciudad);
             /*, / FECHA_POSICIONADO_DESTACADO='$hoy' */
             $sql = "UPDATE anuncios SET estado_destacado=1 , estado_anuncio=1 WHERE ID=$id";
         } else { //quitar destacado
             $sql = "UPDATE anuncios SET estado_destacado=0 WHERE ID=$id";
             //quitarPosicionDestacado($id_ciudad,$posActual);
         }
     }
     $pdo->query($sql);
     
 }

function buscar(){

 
    if (app\uri(0) == 'inicio'){
        $str = $_POST['search'];
        $str = str_replace(' ', '+', $str);
        app\redirect(app\uri()."&search=$str");
    }else{
        $str = $_POST['buscar'];
        $str = str_replace(' ', '+', $str);
        app\redirect(app\uri()."&buscar=$str");
    }
 
}
 
 
// -------------------------------  ADMIN USER LOGIN  -------------------------------------------

 
 
function textos($opt = []){
      
}

function setHorario($opt = []){

/*
    if (isset($opt["redirect"])){
        $url = $opt["redirect"];

        $redirect = str_replace("@","&",$url);
     }else{
        $redirect = false;
     }   

     */

    $id = isset($_POST['id']) ? $_POST['id'] : $opt['id'];

    global $pdo;
    $dias = $_POST['dias']; 
  
    $horarioCalculado = [];
    $rango = 1;

    foreach ($dias as $diaSeleccionado) {
        $active = isset($diaSeleccionado['active']) ? 1 : 0;
        $dia_inicio = $diaSeleccionado['dia_inicio'];
        $dia_fin = $diaSeleccionado['dia_fin'];
        $hora_inicio = $diaSeleccionado['hora_inicio'] . ":00";
        $hora_fin = $diaSeleccionado['hora_fin'] . ":00";
        if ($diaSeleccionado['hora_fin'] == 0) {
            $hora_fin = "23:59";
        } else {
            $hora_fin = ($diaSeleccionado['hora_fin'] - 1) . ":59";
        }
        $horarioCalculado[] = [
            "num_rango" => $rango,
            "dia_inicio" => $dia_inicio,
            "dia_fin" => $dia_fin,
            "hora_inicio" => $hora_inicio,
            "hora_fin" => $hora_fin,
            "active" => $active
        ];
        $rango++;
    }
    $rango = 1;
    foreach ($horarioCalculado as $horario) {
        $num_rango = $horario['num_rango'];
        $dia_inicio = $horario['dia_inicio'];
        $dia_fin = $horario['dia_fin'];
        $hora_inicio = $horario['hora_inicio'];
        $hora_fin = $horario['hora_fin'];
        $active = $horario['active'];
        ${"sql".$rango} = "UPDATE horarios_anuncios SET active = $active, dia_inicio = $dia_inicio, dia_fin = $dia_fin, hora_inicio = '$hora_inicio', hora_fin = '$hora_fin' WHERE id_anuncio = $id AND num_rango = $num_rango";

        $pdo->query(${"sql".$rango});
        $rango++;
    }
  
    return ["POST: "=>$_POST,[$sql1,$sql2,$sql3]];
 
}

 
function actualizarPortada($opt = []){
    global $pdo;
    $id_anuncio = $opt['id'];
    $portada_anuncio= $pdo->query("SELECT filename FROM fotos WHERE id_anuncio=$id_anuncio ORDER BY pos ASC LIMIT 2")->fetchAll(PDO::FETCH_COLUMN);
    $portada_destacado= $pdo->query("SELECT filename FROM fotos WHERE id_anuncio=$id_anuncio AND destacado=1")->fetchAll(PDO::FETCH_COLUMN);
    
    $portadas=[
        "anuncio"=>$portada_anuncio,
        "destacado"=>$portada_destacado
    ];
    $portadasJSON= json_encode($portadas);
    $sql="UPDATE anuncios SET portadas='$portadasJSON' WHERE id=$id_anuncio";
    $pdo->query($sql);
    return $sql;
}


function actualizarPortadaDestacado($id_anuncio){
    global $pdo; 
    $portada_anuncio= $pdo->query("SELECT filename FROM fotos WHERE id_anuncio=$id_anuncio ORDER BY pos ASC LIMIT 2")->fetchAll(PDO::FETCH_COLUMN);
    $portada_destacado= $pdo->query("SELECT filename FROM fotos WHERE id_anuncio=$id_anuncio AND destacado=1")->fetchAll(PDO::FETCH_COLUMN);
    
    $portadas=[
        "anuncio"=>$portada_anuncio,
        "destacado"=>$portada_destacado
    ];
    $portadasJSON= json_encode($portadas);
    $sql="UPDATE anuncios SET portadas='$portadasJSON' WHERE id=$id_anuncio";
    $pdo->query($sql);
} 

  
function checkEvents()
{
    global $pdo;

    $now = date("Y-m-d H:i:s");
    $sql = "SELECT * FROM eventos WHERE ESTADO=TRUE AND FECHA_EJECUCION<='$now'";
    $eventos = $pdo->query($sql)->fetchAll();

    foreach ($eventos as $evento) {
        cambiarEstado($evento->tipo, $evento->accion, $evento->id_anuncio);
    }
    $sql = "UPDATE eventos SET ESTADO=FALSE WHERE ESTADO=TRUE AND FECHA_EJECUCION<='$now'";
    $pdo->query($sql);

    // Eliminar eventos ejecutados

    $sql = "DELETE FROM eventos WHERE estado = 0";
    $pdo->query($sql);
}

 

/* ---------------------------- AGREGAR DIAS ANUNCIO ------------------------------ */
 
function anuncioExtender(){
    global $pdo;
    $id = $_POST['id'];
    $dias = $_POST['dias'];
    $fecha_caducidad = $_POST['fecha_caducidad'];
    // $tiempo= $dias * 24* 3600;
    $fecha_caducidad = addTimeToTimestamp($fecha_caducidad, $dias, 'days');
    $hoy = date('Y-m-d H:i:s');

    $sql = "UPDATE anuncios SET fecha_caducidad='$fecha_caducidad' WHERE id=$id";
    $pdo->query($sql);

    $sqlBuscarEvento = "SELECT id FROM eventos WHERE id_anuncio=$id AND estado=true AND tipo='anuncio' AND accion='despublicar' ORDER BY fecha_creacion DESC LIMIT 1";

    $id_evento = $pdo->query($sqlBuscarEvento)->fetch()->id;

    $sqlEvento = "UPDATE eventos SET fecha_creacion='$hoy', fecha_ejecucion='$fecha_caducidad' WHERE id=$id_evento ";
    $pdo->query($sqlEvento);
    return $sql;
}

  
function anuncioCaducar(){

    global $pdo;
    $id = $_POST['id'];
    // $pos = $_POST['pos'];
    $fecha_programada = isset($_POST['fecha_programada']) ? $_POST['fecha_programada'] : null;
    $id_ciudad = $_POST['id_ciudad'];
    $fecha_caducidad = $_POST['fecha_caducidad'];
    $duracion_actual = $_POST['duracion'];
    $hoy = date('Y-m-d H:i:s');
    if ($fecha_programada) {
        $nuevaDuracion = strtotime($fecha_caducidad) - strtotime($fecha_programada);;
        $sql = "UPDATE anuncios SET duracion=$nuevaDuracion WHERE id=$id";

        $pdo->query($sql);

        $sqlBuscarEvento = "SELECT id FROM eventos WHERE id_anuncio=$id AND estado=true AND tipo='anuncio' AND accion='despublicar' ORDER BY fecha_creacion DESC LIMIT 1";

        $id_evento = $pdo->query($sqlBuscarEvento)->fetch()->id;

        $sqlEventos = "UPDATE eventos SET fecha_creacion='$hoy', fecha_ejecucion='$fecha_programada', estado=true  WHERE id=$id_evento";

        $pdo->query($sqlEventos);

      //  app\redirect("$uri&estado=0");
       return [$sql, $sqlEventos];
    }
 
    $duracion = strtotime($fecha_caducidad) - strtotime($hoy);
    $fecha_caducidad = $hoy;

    $sql = "UPDATE anuncios SET estado_anuncio=0, fecha_caducidad='$fecha_caducidad', duracion=$duracion WHERE id=$id";

    $sqlBuscarEvento = "SELECT id FROM eventos WHERE id_anuncio=$id AND estado=true AND tipo='anuncio' AND accion='despublicar' ORDER BY fecha_creacion DESC LIMIT 1";

    $id_evento = $pdo->query($sqlBuscarEvento)->fetch()->id;

    $sqlEventos = "UPDATE eventos SET fecha_creacion='$hoy', fecha_ejecucion='$fecha_caducidad', estado=false  WHERE id=$id_evento";

    $pdo->query($sql);

    $pdo->query($sqlEventos);

    sendMail("Se caduco anuncio $id", "Se caduco anuncio $id", "<EMAIL>");

    pushMessage("Anuncio $id caducado");
    app\redirect( app\uri() ); 
    return true;
}


function anuncioSubir($opt){
    global $pdo;
    $id = $opt['id'];
    $hoy = date('Y-m-d H:i:s');
    $sql = "UPDATE anuncios SET fecha_posicionado='$hoy' WHERE id=$id";
    $pdo->query($sql); 
  //  app\redirect( app\uri());
    return $sql;
}
 
  
function posicionarDelante(){

    global $pdo;
    $id = $_POST['anuncio'];

    $fecha_posicionado  = date("Y-m-d H:i:s", strtotime($_POST['fecha_posicionado']) + 5); 
    
    $sql = "UPDATE anuncios SET fecha_posicionado = '$fecha_posicionado' WHERE id = $id";
    
    $pdo->query($sql);

    return $sql;
 
    }
  
function anuncioCrear(){  
    global $pdo;
    $user_id = flex()->user->id;
     /* portadas */
        
     $portadasJSON = [
        "anuncio" => [],
        "destacado" => []
    ];
    $emptyPortadasJSON = json_encode(
        $portadasJSON
    );

    $sql = "INSERT INTO anuncios (id, id_usuario, portadas) VALUES (NULL, $user_id, '$emptyPortadasJSON')"; 
    $pdo->query($sql);
    $id = $pdo->lastInsertId();

    /* Horario */

    $rango1 = "INSERT INTO horarios_anuncios (id, id_anuncio, num_rango, active) VALUES (null, $id, 1, 1)";
    $rango2 = "INSERT INTO horarios_anuncios (id, id_anuncio, num_rango, active) VALUES (null, $id, 2, 0)";
    $rango3 = "INSERT INTO horarios_anuncios (id, id_anuncio, num_rango, active) VALUES (null, $id, 3, 0)";
 
    flex()->query($rango1);
    flex()->query($rango2);
    flex()->query($rango3);
     
    $_SESSION['lastid'] = $id;
 
     
    if( flex()->uri(0) == 'adm'){
        header( "Location: /adm/anuncio/$id/");
    }else{
        header( "Location: /panel/detalle/$id/&step=1");
    } 

    return ["sql"=>$sql,"horario"=>[$rango1,$rango2,$rango3]];
 
}  
 
function anuncioEliminar($opt){
 
 
    $id = $opt['id'];
 
        $path = $_SERVER['DOCUMENT_ROOT'] . '/' . app\Config::$uploads . '/fotos/';
       
            // Eliminar registros de la tabla fotos
            $sqlDeleteFotos = "DELETE FROM fotos WHERE id_anuncio=$id";
            flex()->query($sqlDeleteFotos);

            // Eliminar registros del anuncio en tabla anuncio_ciudades
            $sqlDeleteAnuncioCiudades = "DELETE FROM anuncio_ciudades WHERE id_anuncio=$id";
            flex()->query($sqlDeleteAnuncioCiudades);

            // Eliminar registros del anuncio en tabla horarios_anuncios
            $sqlDeleteHorarios = "DELETE FROM horarios_anuncios WHERE id_anuncio=$id";
            flex()->query($sqlDeleteHorarios);
 

            $sqlfotos = "SELECT * FROM fotos WHERE id_anuncio=$id";
            $fotos = flex()->query($sqlfotos)->fetchAll();
            //app\debugJS($fotos);
            foreach($fotos as $foto){
                unlink($path . $foto->filename);
            }  
            // Eliminar el anuncio
            $sql = "DELETE FROM anuncios WHERE id=$id";
            flex()->query($sql);

            // Eliminar el video
            $linkvideo = $_SERVER['DOCUMENT_ROOT'] . '/uploads/videos/' . $id . '.mp4';
            if(file_exists($linkvideo)){
                unlink($linkvideo);
            }
     
            pushMessage('Anuncio eliminado', $tipo = 'success');
        
        return [$sql, $sqlfotos]; 
  
 }

// anuncios/&id=4&op=destacarFoto  

function destacarFoto($id)
{
    global $pdo;
    $sql = "UPDATE fotos SET destacado = 1 WHERE id = $id";
    $pdo->query($sql);
    app\redirect("/galeria&id=$id");
    die();
} 

 
function getHorario($opt){
   global $pdo;
   
   $id=$opt['id'];
   $sql = "SELECT * FROM horarios_anuncios WHERE id_anuncio=$id ORDER BY num_rango ASC";
   $horarios = $pdo->query($sql)->fetchAll(); 
   $rango1 = [
       $horarios[0]->dia_inicio,
       $horarios[0]->dia_fin,
       convertirHorario($horarios[0]->hora_inicio),
       convertirHorario($horarios[0]->hora_fin),
       "active"=>1
   ];

   $rango2 = [
       $horarios[1]->dia_inicio,
       $horarios[1]->dia_fin,
       convertirHorario($horarios[1]->hora_inicio),
       convertirHorario($horarios[1]->hora_fin),
       "active"=>$horarios[1]->active
   ];
   $rango3 = [
       
       $horarios[2]->dia_inicio,
       $horarios[2]->dia_fin,
       convertirHorario($horarios[2]->hora_inicio),
       convertirHorario($horarios[2]->hora_fin),
       "active"=>$horarios[2]->active
   ];

   return ["sql"=>$sql, "data"=>[$rango1, $rango2, $rango3]];
}




 
 // NO POST
function getFotos($opt){
    global $pdo; 
    $id=$opt['id']; 
    $fotos = $pdo->query("SELECT * FROM fotos WHERE id_anuncio=$id AND estado=1 ORDER BY pos ASC")->fetchAll();
    return $fotos; 
}

 
 
// NO POST
function getAnuncio($opt){ 
    global $pdo; 
    $id = $opt["id"];
    $sql = "SELECT * FROM anuncios WHERE id=$id";

    $anuncio = $pdo->query($sql)->fetch();
    // $anuncio->notas = $anuncio->notas ? json_decode($anuncio->notas, true) : false ;
    //$enlaces_anuncios= $pdo->query($sql_enlaces)->fetchAll();
   // $anuncio = (array) $anuncio;
   if( isset($anuncio->gps) ) $anuncio->gps = getGps( $anuncio->gps); 
 
    return ["sql"=>$sql, "data"=>$anuncio];
}


function getDiasRestantes($fecha_publicacion, $fecha_caducidad)
{
    $DAY= 3600*24;

    $delta_seconds_publicacion=(strtotime(date("Y-m-d H:i:s")) -strtotime($fecha_publicacion));
    
    $delta_days_publicacion= $delta_seconds_publicacion/$DAY;

    if($delta_days_publicacion<=2){
        return "Novedad";
    }
    $hoy=strtotime(date("Y-m-d H:i:s"));
    $delta_seconds_caducidad=(strtotime($fecha_caducidad) - $hoy );
    
    $delta_days_caducidad= round($delta_seconds_caducidad/$DAY);


    if($delta_days_caducidad > 5){
        // return "Último día el " . date("d",strtotime($hoy . " + $delta_days_caducidad"));
        return "Último día el " . date("j",strtotime($fecha_caducidad));
    }
    if($delta_days_caducidad <=1){
        return "Último día hoy";
    }
    return "Quedan $delta_days_caducidad días";
}
 


/* Establece gps de anunciante */

function setCoords($opt = [])
{
    global $pdo;

    if (isset($opt["redirect"])){
        $url = $opt["redirect"];

        $redirect = str_replace("@","&",$url);
     }else{
        $redirect = false;
     }   

    $id = isset($_POST['id']) ? $_POST['id'] : $opt['id'];

    $coords = isset($_POST['gps']) ? $_POST['gps'] : $opt['gps'];
  
    $coords = explode(",", $coords);

    $long = $coords[0];
    $lati = $coords[1];

    $coords_sql = "POINT(" . $lati . "," . $long . ")";

    $sql = "UPDATE anuncios SET gps=$coords_sql WHERE id = $id";

    $pdo->query($sql);

    // HERE - File map image

    $key = "jGWtIYXUVtdI3JkcumMkCZEsinRYD7JFX5fJcVfcCyI";
    $app_id = "T1Cqmds7ZzoEgrlnIkMN";

    $position = "$long%2C$lati";

    // $img_url = "https://image.maps.ls.hereapi.com/mia/1.6/mapview?c=$position&z=16&apiKey=$key&h=450&w=822&u=150";
      // Actualizado a la API v3 de HERE Maps (platform.here.com citas10_api_v3 (<EMAIL> account))
    
    $img_url = "https://maps.hereapi.com/mia/v3/base/mc/center:$long,$lati;zoom=16/822x450/jpeg?apikey=$key&overlay=point:$long,$lati;style=circle;width=150;color=%230F05";
     
    $img_path = $_SERVER['DOCUMENT_ROOT'] . '/' . app\Config::$uploads . "/maps/$id.jpg"; 
    
    file_put_contents($img_path, file_get_contents($img_url));
    

}
  
function userGPS($opt = false){ 

    // Por primera vez (detectar ip usuario ciudad)
    if(!isset($_SESSION["userGPS"])) $_SESSION['userGPS']['gps'] = "42.**************,-1.****************";

    // Definida por usuario por o argumento
    if ( isset( $_POST["userGPS"] ) ) $_SESSION['userGPS']['gps'] = $_POST["userGPS"];
    if ($opt) $_SESSION['userGPS']['gps'] = $opt;
 
    $coords = explode(",", $_SESSION['userGPS']['gps']);
    $_SESSION['userGPS']['db'] = "POINT(" . $coords[1] . "," . $coords[0] . ")";
    $js["lat"] = (float)$coords[0];
    $js["lng"] = (float)$coords[1];

    $_SESSION['userGPS']['js'] = json_encode($js);
  
   // app\redirect('/');
    if ($_SESSION['userGPS']['gps'] != "42.**************,-1.****************"){

        app\redirect('/inicio/&fun=filtros("orden":"mas-cercanos")');
    }
   
    return $_SESSION['userGPS'];
}

 

 /* Actualiza todos los cambios de la tabla anuncios 
 Importante, valores boleeanos pasalos como numeros no como true / false en la url al menos se transforman a numeros en formato string.
*/

function anuncioActualizar($opt = false){
 
    // db pdo
    global $pdo;

    // Si exis redirect, se reemplaza @ por &
    if (isset($opt["redirect"])){
        $url = $opt["redirect"];
    
        $redirect = str_replace("@","&",$url);
     }else{
        $redirect = false;
     }  
 
    
   
    $servicios_str = ""; 

    $id = isset($_POST['id']) ? $_POST['id'] : $opt['id']; 
     

    $opt = $_POST; 
    $anuncio = getAnuncio(["id"=>$id])['data'];

    if(isset($opt['nombre'])){
        /* Inicio */
        $video = $anuncio->videoAprobado ? $anuncio->videoAprobado : 0;
        $sql = "UPDATE anuncios SET videoAprobado = $video";
    }else{
        $sql = "UPDATE anuncios SET nombre = '$anuncio->nombre'"; 
    }


    if(isset($opt['servicios'])){ 

        $servicios = array_keys( $_POST["servicios"] ); 
        foreach($servicios as $servicio){
            $servicios_str .= "$servicio,";
        };
        $servicios_str = substr($servicios_str, 0, -1);
    } 
 
   $categoria = $opt['id_categoria'] ? $opt['id_categoria'] : "";
   $sql .= $categoria ? ", id_categoria = $categoria" : ''; 
   
   $nombre = $opt['nombre'] ? $opt['nombre'] : "";
   $sql .= $nombre ? ", nombre = '$nombre'" : "";

   // servicios
   $sql .= $servicios_str ? ", servicios = '$servicios_str'" : "";

   $pais = $opt['pais'] ? $opt['pais'] : "";
   $sql .= $pais ? ", pais = '$pais'" : "";

   $id_pais = $opt['id_pais'] ? $opt['id_pais'] : "";
   $sql .= $id_pais ? ", id_pais = '$id_pais'" : "";

   $edad = $opt['edad'] ? $opt['edad'] : "";
   $sql .= $edad ? ", edad = $edad" : "";

   $telefono = $opt['telefono'] ? $opt['telefono'] : "";
   $telefono = str_replace(' ', '', $telefono);
   $sql .= $telefono ? ", telefono = '$telefono'" : "";
   
   $telefono2 = $opt['telefono2'] ? $opt['telefono2'] : "";
   $telefono2 = str_replace(' ', '', $telefono2);
   $sql .= $telefono2 ? ", telefono2 = '$telefono2'" : ", telefono2 = ''";

   $hayWhatsapp1 = isset($opt['hayWhatsapp1']) ? 1 : false;
   $sql .= $hayWhatsapp1 ? ", hayWhatsapp1 = $hayWhatsapp1" : ", hayWhatsapp1 = 0";

   $hayWhatsapp2 = isset($opt['hayWhatsapp2']) ? 1 : false;
   $sql .= $hayWhatsapp2 ? ", hayWhatsapp2 = $hayWhatsapp2" : ", hayWhatsapp2 = 0";

   $salidas = isset($opt['salidas']) ? 1 : false;
   $sql .= $salidas ? ", salidas = $salidas" : ", salidas = 0";
   
   $casa = isset($opt['casa']) ? 1 : false;
   $sql .= $casa ? ", casa = $casa" : ", casa = 0";

   $visa = isset($opt['visa']) ? 1 : false;
   $sql .= $visa ? ", visa = $visa" : ", visa = 0";

   $bizum = isset($opt['bizum']) ? 1 : false;
   $sql .= $bizum ? ", bizum = $bizum" : ", bizum = 0";

   $titulo = isset($opt['titulo']) ? $opt['titulo'] : "";
   $sql .= $titulo ? ", titulo = '$titulo'" : "";
 
   $descripcion = isset($opt['descripcion']) ? $opt['descripcion'] : "";
   $sql .= $descripcion ? ", descripcion = '$descripcion'" : "";
 
   $notas_admin = isset($opt['notas_admin']) ? $opt['notas_admin'] : "";
   $sql .= $notas_admin ? ", notas_admin = '$notas_admin'" : "";
 
   $gps = isset($opt['gps']) ? $opt['gps'] : "";
   $sql .= $gps ? ", gps = $gps" : "";

    /* Final */
    $sql .= " WHERE id = $id";

   $pdo->query($sql);
  
  
    if ($redirect){
     flex()->redirect($redirect);
   }
 

   return [$sql];
     

}

 
 // POST
 function setFotos($opt = []){
  
    $uploadFiles = $_FILES['fotos']['size'][0] > 0 ? true : false;

    global $pdo;
    $path = $_SERVER['DOCUMENT_ROOT'] . '/' . app\Config::$uploads . '/fotos/';
    $num_files = count($_FILES['fotos']['tmp_name']);
    $id_anuncio = isset($opt['id']) ? $opt['id'] : $_POST['id'];
    $fotos_existentes = getFotos(["id"=>$id_anuncio]); 
 
    $sql_ordenar = "UPDATE fotos SET pos=pos + $num_files WHERE id_anuncio=$id_anuncio";
    $pdo->query($sql_ordenar);
 
    if ($uploadFiles) { 
        for ($i = 0; $i < $num_files; $i++) {

            $type = $_FILES['fotos']['type'][$i];
            $size = $_FILES['fotos']['size'][$i];

            if ($type != 'image/jpeg') {
                msgbar('Formato de archivo no permitido');
            }

            if ($size > 5000000) { // 5mb = 5000000 | 10mb = 10000000
                msgbar('El archivo es demasiado grande');
            }


            $filename = $id_anuncio . '_' . $i . '_'. date('Ymd_His') . ".jpg";
  
            $fullpath = $path . $filename;

            if (move_uploaded_file($_FILES['fotos']['tmp_name'][$i], $fullpath)) {
                msgbar("El fichero es válido y se subió con éxito");
            } else {
                msgbar("¡Posible error");
            }
            // Subida de todas las fotos a la tabla fotos 

            resizeImage(app\Config::$uploads . "/fotos/$filename",app\Config::$uploads . "/fotos/$filename",700,700); 

            $sql_fotos = "INSERT INTO fotos VALUES(0, $id_anuncio, '1', '$filename', $i+1, 0) "; 
            $pdo->query($sql_fotos);
        }
    }

    actualizarPortada(["id"=>$id_anuncio]);

    return [$_POST, $_FILES, $fotos_existentes];
 }



 // NO POST
 function fotosBorrar($opt){ 
    global $pdo;
    $path = $path = $_SERVER['DOCUMENT_ROOT'] . '/' . app\Config::$uploads . '/fotos/';
    $id = $opt['id'];
    $fotos = getFotos(["id"=>$id]);  


    // Archivos
    foreach ($fotos as $foto) {
        if(file_exists("uploads/fotos/$foto->filename")){
            msgbar("foto $foto->filename ha sido eliminada");
            unlink("uploads/fotos/$foto->filename");
        }else{
            msgbar("No se pudo borrar la foto");
        }
    }

      // Base de datos
      $sql = "DELETE from fotos WHERE id_anuncio = $id";
      $pdo->query($sql); 
    if ($fotos){
        msgbar("Se eliminaron las fotos".json_encode($fotos, true));
    }else{
        msgbar("No había fotos que eliminar");
    }
   
    actualizarPortada(["id"=>$id]);

 }


 function borrarFoto($opt = []){
    global $pdo;
    $id = $opt['id'];
    $select = "SELECT * FROM fotos WHERE id = $id";
    $foto = $pdo->query($select)->fetch();  
    $id_anuncio = $foto->id_anuncio;
    // $num_files = $pdo->query("SELECT COUNT(*) FROM fotos WHERE id_anuncio = $id_anuncio")->fetchColumn();
    $pos = $foto->pos;
  
    // Eliminar archivo
    if(file_exists("uploads/fotos/$foto->filename")){
        msgbar("foto $foto->filename ha sido eliminada");
        unlink("uploads/fotos/$foto->filename");
    }else{
        msgbar("No se pudo borrar la foto");
    }
    // Eliminar Registro db de la foto
    $sql = "DELETE from fotos WHERE id = $id";
    $pdo->query($sql); 

    // Reordenar las fotos
    $sql_ordenar = "UPDATE fotos SET pos=pos - 1 WHERE id_anuncio=$id_anuncio AND pos > $pos";
    $pdo->query($sql_ordenar); 
 
    actualizarPortada(["id"=>$id_anuncio]);

    return [$sql, $foto->filename];

 }



 function subirFoto($opt = []){
    global $pdo;
    $id = $opt['id'];
    $id_anuncio = $opt['id_anuncio'];
    $pos = $opt['pos'];
    
      // sql
      $sql_ordenar = "UPDATE fotos SET pos=pos + 1 WHERE id_anuncio=$id_anuncio AND pos < $pos";  
      $sql_primero = "UPDATE fotos SET pos=1 WHERE id=$id";

     $pdo->query($sql_ordenar); 
    $pdo->query($sql_primero);
 
    actualizarPortada(["id"=>$id_anuncio]);

    return $sql_primero;

 }


// AJAX
 function destacadoFoto(){ 
    global $pdo; 
    $id = $_POST['id'];
    $set = $_POST['set']; // 1 on 0 off
     
    $foto = $pdo->query("SELECT * FROM fotos WHERE id = $id")->fetch();
    $id_anuncio = $foto->id_anuncio;
    $sql = "UPDATE fotos SET destacado = $set WHERE id = $id";
    
    
    $fotosSeleccionadas = $pdo->query("SELECT * FROM fotos WHERE id_anuncio = $id_anuncio AND destacado = 1")->fetchAll(); 

    if ($set == 1 and count($fotosSeleccionadas) >= 2) { 
        echo json_encode(["message" => "Limite de fotos superado"]);
        http_response_code(401);
        exit;
    }  
  
    try {
        $pdo->query($sql); 
        actualizarPortada(["id"=>$id_anuncio]);
        // pushMessage("Foto $id seleccionada total ".  ++$fotosSeleccionadas, $tipo = "success");
    } catch (Exception $e) { 
        // pushMessage("Ocurrio un error al seleccionar la foto $id", $tipo = "error");
    }
    
    echo json_encode(["message" => "ok"]); 

    return ["sql"=>$sql];
 }


 // AJAX

 function destacado_eliminar($opt = []){

    global $pdo; 
    $id = $opt['id'];
    $sql = "UPDATE anuncios SET estado_destacado = 0 WHERE id=$id";
 
    //http_response_code(204);
    try {
        $pdo->query($sql);
        $pdo->query("DELETE FROM eventos WHERE id_anuncio=$id AND estado=true AND tipo='destacado'");
        echo json_encode([
            "message" => "OK",
            "sql" => $sql
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            "message" => "Ha ocurrido un error al eliminar el destacado $id", "Exception" => $e->getMessage(),
            "sql" => $sql
        ]);
            
    }
    die();


}



// AJAX
function activarVideo(){
    global $pdo; 
    $id = $_POST['id'];
    $set = $_POST['set']; // 1 on 0 off
       
    $sql = "UPDATE anuncios SET videoAprobado = $set WHERE id = $id";
      
    try {
        $pdo->query($sql);  
        // pushMessage("Foto $id seleccionada total ".  ++$fotosSeleccionadas, $tipo = "success");
    } catch (Exception $e) { 
        // pushMessage("Ocurrio un error al seleccionar la foto $id", $tipo = "error");
    } 
    echo json_encode(["message" => "ok"]); 
 }


 function videoExiste($id){
    $pathVideo = $_SERVER['DOCUMENT_ROOT'] . '/' . app\Config::$uploads . '/videos/' . $id . ".mp4";
    if(file_exists($pathVideo)){
        return true;
    }else{
        return false;
    }
 }

 function videoSubir($opt = []){
    $id = $opt['id'];
    $pathVideo = $_SERVER['DOCUMENT_ROOT'] . '/' . app\Config::$uploads . '/videos/' . $id . ".mp4";
  
    $type = $_FILES['video']['type'];

    

    if ($type != "video/mp4") {
        app\console('El formato no es permitido'); 
        echo "Formato de archivo no permitido";
        return false;
    }

    if (move_uploaded_file($_FILES['video']['tmp_name'], $pathVideo)) {
        msgbar("El fichero es válido y se subió con éxito");
    } else {
        msgbar("¡Posible error");
    }

    return true;
 }


 function borrarVideo($opt = []){
    global $pdo;
    $id = $opt['id'];
    $pathVideo = $_SERVER['DOCUMENT_ROOT'] . '/' . app\Config::$uploads . '/videos/' . $id . ".mp4";
    if ( unlink($pathVideo))  {
        msgbar("El video se borro");
    } else {
        msgbar("¡Posible error");
    }

    $sql = "UPDATE anuncios SET videoAprobado = 0 WHERE id = $id";
    $pdo->query($sql);
 }

   

function userNotesNew(){
    global $pdo;
    global $id;
/*
    $name = ;
    $tipo = ;
*/ 
    $nota = [
        "nombre" => $_POST['notaUsuarioTexto'],
        "tipo" => $_POST['notaUsuarioTipo']
    ];
   
    $notas = $pdo->query("SELECT notas from anuncios WHERE id=$id")->fetch()->notas; 
    $notas = json_decode($notas, true);

    array_push($notas, $nota);
 
    $notas = json_encode($notas);

    $sql="UPDATE anuncios SET notas='$notas' WHERE id=$id"; 
    $pdo->query($sql);
     return $sql; 
}


/* 
[
    {"tipo": "no","texto": "numeros ocultos"},
    {"tipo": "si","texto": "acepto bizum"}
]


{"anuncio":[],
    "destacado":[]
}

*/


function getUserNotes($opt = []){

    global $pdo;
    $id = $opt['id'];
    $sql = "SELECT notas from anuncios WHERE id=$id";

    $data = $pdo->query($sql)->fetch()->notas;
    if($data){
    $data = json_decode($data, true);
    }
    return ["sql" => $sql, "data" => $data];
}
 
 
function userNotesSave(){
 
    global $pdo;
    global $id; 

    if (isset($_POST['notas'])){
        $notas = json_encode($_POST['notas'], JSON_UNESCAPED_UNICODE);  
        $sql="UPDATE anuncios SET notas='$notas' WHERE id=$id"; 
    }else{
        $sql="UPDATE anuncios SET notas=NULL WHERE id=$id"; 
    }

   
    $pdo->query($sql);
    return $sql; 
     

}

function userNotesNueva(){
 
    global $pdo;
    global $id;

    $vacia = ["tipo" => "info", "texto" => ""];
   
    $notas = $pdo->query("SELECT notas from anuncios WHERE id=$id")->fetch()->notas; 
    $notas = json_decode($notas, true);

    if($notas){
        array_push($notas, $vacia);
    }else{
        $notas = [$vacia];
    }

    $notas = json_encode($notas, JSON_UNESCAPED_UNICODE);

    $sql="UPDATE anuncios SET notas='$notas' WHERE id=$id"; 
    $pdo->query($sql);
     return $sql; 

}


function usuarioCrear(){
    global $pdo;

    $anuncio_id = $_POST['id']; 
    $usuario_email = $_POST['usuario_email'];

    $sqlSelect = "SELECT * from usuarios where email = '$usuario_email'";
    $usuario = $pdo->query($sqlSelect)->fetch();

    if ($usuario){ // EXISTE ESE EMAIL

        $id_usuario = $usuario->id;

    }else{ // NO EXISTE ESE EMAIL CREA USUARIO
            $sqlUsuario = "INSERT INTO usuarios VALUES (null, 2, '$usuario_email', null, 1, null, null)";
            $pdo->query($sqlUsuario);
            $id_usuario = $pdo->lastInsertId();
    }
 
    $sqlAnuncio = "UPDATE anuncios SET id_usuario = $id_usuario where id = $anuncio_id";
    $pdo->query($sqlAnuncio);

    return [@$sqlUsuario, $sqlAnuncio];

}

 
  

//http://citas10-2022.test/ajax/&fun=cron()&api


 
/* NUEVAS FUNCIONES PARA USUARIOS CRUD */

// http://citas10.test/entrar/&fun=getUsuario(%22email%22:%<EMAIL>%22)

function getUsuario($opt = []){
   
    global $pdo; 
    $email = isset($opt['email']) ? $opt['email'] : false;
    $id = isset($opt['id']) ? $opt['id'] : false;

    if ($email){
        $sql = "SELECT * FROM usuarios WHERE email='$email'";  
    }else{
        $sql = "SELECT * FROM usuarios WHERE id='$id'";  
    }
     
    $usuario = $pdo->query($sql)->fetch();
    
    return ["sql"=>$sql,"data"=>$usuario];

}

function getUsuariosConPublicados($opt = []){

        $sql = "
        SELECT u.*, COUNT(a.id) AS num_anuncios
        FROM usuarios u
        JOIN anuncios a ON u.id = a.id_usuario
        WHERE a.estado_anuncio = 1
        GROUP BY u.id
    ";
    $usuariosConAnuncios = flex()->fetchAll($sql);

    return ["sql"=>$sql,"data"=>$usuariosConAnuncios];

}

function buscarUsuario($opt = []){
    $valor = $opt['valor'];
    $campo = $opt['campo'];
    $sql = "SELECT * FROM usuarios WHERE $campo='$valor'";
    $usuario = flex()->fetchAll($sql);
    return ["sql"=>$sql,"data"=>$usuario];
}



function timer($opt = []){  
      
    if ( !isset($_SESSION['time']) ) $_SESSION['time'] = 0; 
    if ( !isset($_SESSION['redirect']) ) $_SESSION['redirect'] = 0; 
    
     $time = $_SESSION['time'] ;
 
    if ($time >=5){
        $trigger = "";  
      } else{
        $trigger = "every 1s";
      }
  
      $time = $_SESSION['time'] ; 

      echo  " 
      <div hx-get='&fun=timer()&api' hx-trigger='$trigger' hx-target='this' hx-swap='outerHTML'>
      <progress style='display:block; width:50%; margin:0 auto; text-align: center' value='$time' max='5'> </progress>
      </div>"; 
       

      if ($time >=5){
        unset($_SESSION['time']); 
        if ($_SESSION['redirect']) app\redirect($_SESSION['redirect']);
        unset($_SESSION['redirect']);  
      } else{
        $_SESSION['time'] = $_SESSION['time'] + 1;
      }
 
}

// &fun=usuarioAsignarAnuncios()

function usuarioAsignarAnuncio(){
    
    global $pdo;

    $anuncio = $_POST['anuncios'];
    $id = $_POST['id'];
  
   $sql = "UPDATE anuncios SET id_usuario=$id WHERE id=$anuncio";
   $pdo->query($sql);
 
    return $sql;

}

function getUsuarioAnuncio($opt = []){

    global $pdo;

    $anuncio = $opt['id'];

    $sql = "SELECT id_usuario FROM anuncios WHERE id=$anuncio";
    $datos = $pdo->query($sql)->fetch();

    return ["sql"=>$sql,"data"=>$datos];

}

//http://citas10.test/adm/x/&fun=getNumeroAnunciosFromUsuario("id":50)&api&htmx

function getNumeroAnunciosFromUsuario($opt = []){

   global $pdo; 
   $usuario = $opt['id']; 
   $estado = isset($opt['estado']) ? "AND estado_anuncio=".$opt['estado'] : ""; 
   $sql = "SELECT id FROM anuncios WHERE id_usuario=$usuario $estado";
   $anuncios = $pdo->query($sql)->fetchAll();
   $anuncios = count($anuncios);
   if(isset($_GET["htmx"])):?>

        <span><?=$anuncios?></span> 
   
   <?php endif;

   return ["sql"=>$sql,"data"=>$anuncios];

}


 
//http://citas10.test/adm/&fun=anuncioCaducaEn(%22id%22:300) 
function anuncioCaducaEn($opt = []){
    global $pdo;
    $id = $opt['id'];

    $anuncio = getfun("getAnuncio(\"id\":\"$id\")")["data"]; 

    return seconds2human((strtotime($anuncio->fecha_caducidad) - strtotime(date("Y-m-d H:i:s")))) ; 

}

//$diff_mins = round(abs($ahora->getTimestamp() - $expire->getTimestamp()) / 60);

 

function conruta($opt = []){
    $a = $opt['a'];
    $b = $opt['b'];
    echo "con ruta $a+$b=".($a+$b) ;
}

function test1(){
    echo "funcion test1";
}