<?php
function logbar($msg)
{

  $limit = 6;
  $number =  $_SESSION["logN"];

  if (count($_SESSION["logbar"]) > $limit) {
    array_pop($_SESSION["logbar"]);
  }
  $_SESSION["logbar"][date("d_H:i:s($number)")] = $msg;
  krsort($_SESSION["logbar"]);

  $_SESSION["logN"]++;
}

function fun($name)
{
  global $flex;
  $name = str_replace("'", "\"", $name); // Comillas simples sustituidas por json dobles

  $loggN = &$_SESSION["loggN"];

  $fileName = debug_backtrace()[0]["file"];
  $fileName = explode('\\', $fileName);
  $fileName = $fileName[count($fileName) - 1];
  $line = debug_backtrace()[0]["line"];
  $fileLine = "$fileName:$line";

  // PENDIENTE: Si no tiene privilegios sacar y redireccionar. (sesion adm ok)

  $fn_array = explode('(', $name);

  $fname = $fn_array[0];
  $fargs = isset($fn_array[1]) ? $fn_array[1] : '';
  $fargs_ori = substr($fargs, 0, -1); // borramos el cierre del parentesis
  // -- json formating
  if (Flex()->isJson("{" . $fargs_ori . "}")) {
    $fargs_json = "{" . $fargs_ori . "}";
  } else {
    $fargs_json = Flex()->ejson($fargs_ori);
  }

  $flex->args = $fargs_json;

  // Comprobar si el string contiene el caracter {
  /* if (substr($fargs_ori, 0, 1) != "{" ) {
     $fargs_json = "{". $fargs_ori."}";
    }*/


  // arguments JSON to array  
  $fargs_array = json_decode($fargs_json, true);
  $method = $_SERVER["REQUEST_METHOD"] ?? "code";
  if (isset($_SERVER["HTTP_REFERER"])) {
    $referer = explode('/', $_SERVER["HTTP_REFERER"]);
    $view = $referer[3];
    $referer4 = isset($referer[4])? "/".$referer[4] : '';
    $referer5 = isset($referer[5])? "/".$referer[5] : '';
    $referer = $view . $referer4 . $referer5;
    $referer = rawurldecode($referer);
  } else {
    $referer = '';
    $view = flex()->uri(0);
  }

  $tag = isset($_GET['tag']) ? $_GET['tag'] : false;
  if (!$tag) {
    $tag = $method != 'GET' ? 'FETCH' : 'URL';
  }

  $retorno = $fname($fargs_array);

  logbar(["$method | $referer | $fileLine | $fname ($fargs_json)" => ["$method | $referer | $fileLine | $fname ($fargs_json)" => $retorno]]);

  if (!isset($_SESSION["logg"])) {
    $_SESSION["logg"] = [];
  }

  if (count($_SESSION["logg"]) > 10) {
    array_pop($_SESSION["logg"]);
  }

  // array_unshift($logg, "$view:$fname($fargs_json)" => $retorno);  
  // array_merge($logg, ["$view:$fname($fargs_json)" => $retorno]);  
  $_SESSION["logg"]["$view&fun($tag) $referer | $fileLine:$fname($fargs_json)"] = ["$view&fun($tag) $referer | $fileLine:$fname($fargs_json)" => $retorno];

  $loggN++;

  pushMessage("$fname($fargs_ori)");

  /*
 if ( !isset($_GET['api']) AND app\uri(1) != "x" AND app\uri(2) != "x"){
     echo "<script>
     let url = " ."/". app\uri(0) ."/" . "
     history.pushState(null, '', url)
     </script>"; 
 }
 */

  return $retorno;
}

// para asignaciones  
function getfun($name)
{

  global $debugbar;
  $loggN = &$_SESSION["loggN"];
  $fileName = debug_backtrace()[0]["file"];
  $fileName = explode('\\', $fileName);
  $fileName = $fileName[count($fileName) - 1];
  $line = debug_backtrace()[0]["line"];
  $fileLine = "$fileName:$line";

  $fn_array = explode('(', $name);
  $fname = $fn_array[0];
  $fargs = $fn_array[1];
  $fargs_ori = substr($fargs, 0, -1); // borramos el cierre del parentesis 
  // -- json formating

  // -- json formating
  if (Flex()->isJson("{" . $fargs_ori . "}")) {
    $fargs_json = "{" . $fargs_ori . "}";
  } else {
    $fargs_json = Flex()->ejson($fargs_ori);
  }


  // arguments JSON to array  
  $fargs_array = json_decode($fargs_json, true);
  // ----------- mensajes debugbar   



  try {
    $retorno = $fname($fargs_array);
  } catch (Exception $e) {
    if (isset($_SESSION['adm']) || $_SESSION['user']['rol'] == 1) $debugbar['exceptions']->addException($e);
  }

  $sql = isset($retorno['sql']) ? "SQL" : '';

  logbar(["getfun() $sql | $fileLine | $fname ($fargs_json)" => $retorno]);


  /* test */

  // array_unshift($logg, ["$fileLine"=>["$fname($fargs_json)" => $retorno]]) ;  
  //  array_merge($logg, ["$fileLine"=>["$fname($fargs_json)" => $retorno]]);  
  $_SESSION["logg"]["GET $fileLine $fname($fargs_json)"] = ["GET $fileLine $fname($fargs_json)" => $retorno];
  $loggN++;
  // krsort($_SESSION["logbar"]);  


  return $retorno;
}
