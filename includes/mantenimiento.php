<?php

/**------------------------------------------------------------------------
 *                 ADMINISTRADOR BACKUPS STATIC HTML
 *------------------------------------------------------------------------**/



 /**--------------------------------------------
  *   Altera mediante JS el CSS ocultando diversos elementos
  *   y mostrando un texto de aviso de que esta el modo mantenimiento
  *  ----------
  *  @param el contenido html capturado con file_get_contents(url)
  *  
  *---------------------------------------------**/
 
 function addScript($content){

    $script = "
    <script>
     /* ---------- SELECTORES ------------ */
    let interface = document.querySelector('.interface'); 
    let botones = document.querySelector('.botones'); 
    let ubicacion = document.querySelector('.ubicacion'); 

    /* ---------  OCULTACIÓN DE ELEMENTOS ------------ */ 
    interface.style.display = 'none'; 
     botones.classList.remove('d-flex');
     botones.style.display = 'none';  
      ubicacion.style.display = 'none';

    /* ------------- SELECTORES -------------------- */
 
    let nav = document.querySelector('.nav-max');
    let mov = document.querySelector('.mov-max');

    /* ------------- NODOS ------------------------ */
    let h2 = document.createElement('h2');
    let h2mov = document.createElement('h2');

    /* ---------------- AGREGAR CONTENIDO PC ------------- */
    nav.style.justifyContent = 'start';
    h2.innerText = 'EN MANTENIMIENTO, MENÚ NO DISPONIBLE';
    h2.style.marginLeft = '30px';
    h2.style.fontSize = '1.5rem';
    h2.style.lineHeight = '2.6';
    h2.style.textAlign = 'center';
    nav.appendChild(h2);
 
    /* ---------------- AGREGAR CONTENIDO MOVIL ------------- */
    mov.style.justifyContent = 'start';
    h2mov.innerText = 'EN MANTENIMIENTO, MENÚ NO DISPONIBLE';
    h2mov.style.fontSize = '1rem'; 
    h2mov.style.marginLeft = '10px'; 
    mov.appendChild(h2mov);

    /* ------------------- FIN ------------------------ */
    </script>  
    </body>
    ";

    return str_replace("</body>", $script, $content);

}


 
function backup_mantenimiento(){ 

    /* 
    
    Solo realizar el backup si no se ha realizado anteriormente

    */

    $fecha = date('Y.m.d_H');
 
    if ( file_exists("mantenimiento_backups/$fecha") ){
     return false;
    } 


    global $zipFile;
    // No se realiza si se esta en mantenimiento
    if (file_exists("mantenimiento_1.txt")) return false;
    // Borramos carpeta antigua
    if (file_exists('mantenimiento')) deleteAll('mantenimiento');

     // Creamos carpetas
     $ruta_mantenimiento = "mantenimiento";
     if (!file_exists($ruta_mantenimiento)) mkdir($ruta_mantenimiento); 

     $ruta_uploads = "mantenimiento/uploads";
     if (!file_exists($ruta_uploads)) mkdir($ruta_uploads); 

     $ruta_anuncio = "mantenimiento/anuncio";
     if (!file_exists($ruta_anuncio)) mkdir($ruta_anuncio); 

     $ruta_inicio = "mantenimiento/inicio";
     if (!file_exists($ruta_inicio)) mkdir($ruta_inicio); 

     $ruta_fotos = "mantenimiento/uploads/fotos";
     if (!file_exists($ruta_fotos)) mkdir($ruta_fotos); 

     $ruta_maps = "mantenimiento/uploads/maps";
     if (!file_exists($ruta_maps)) mkdir($ruta_maps); 

     $ruta_videos = "mantenimiento/uploads/videos";
     if (!file_exists($ruta_videos)) mkdir($ruta_videos); 

     $ruta_mantenimiento_backups = "mantenimiento_backups";
     if (!file_exists($ruta_mantenimiento_backups)) mkdir($ruta_mantenimiento_backups); 

        // archivo de redirección

        $contenido = 
        "
        <html>
            <head>
                <script>
                    let url = window.location.href;
                    console.log(url);
                    window.location.replace(url+'inicio');
                </script>
            </head> 
        </html>
        "; 

      // $file_redirect = fopen("mantenimiento/index.html", "w");
        file_put_contents("mantenimiento/index.html", $contenido);
    
    // Copia actualizada de carpetas de archivos recursivos 
    smartCopy("js", "mantenimiento/js");
    smartCopy("css", "mantenimiento/css");
    smartCopy("files", "mantenimiento/files");

    // Geración de la portada

    $dominio = $_SERVER['HTTP_HOST'];
    $html = file_get_contents("http://$dominio/inicio");
    $html = addScript($html);
    file_put_contents("mantenimiento/inicio/index.html", $html);
     

   
    // Obtener anuncios publicados
    $anuncios = getfun("getAnuncios2()")["data"];
    $dominio = $_SERVER['HTTP_HOST']; 

    foreach ($anuncios as $anuncio){
       
        // Si no existe carpeta la creamos
        $ruta = "mantenimiento/anuncio/$anuncio->id";
        if (!file_exists($ruta)) mkdir($ruta);
        // Leemos index y lo guardamos
        $html = file_get_contents("http://$dominio/anuncio/$anuncio->id/");
        file_put_contents("mantenimiento/anuncio/$anuncio->id/index.html", $html);
  
        // ------------------------   Archivos de anuncio ----------------------.
         $fotos = getFotos(["id" => $anuncio->id]); 
         // Fotos 
         foreach($fotos as $foto){
             $origen = "uploads/fotos/$foto->filename";
             $destino = "mantenimiento/uploads/fotos/$foto->filename";  
             copy($origen, $destino);
         }

         // Mapa
         if(file_exists("uploads/maps/$anuncio->id.jpg")){
             copy("uploads/maps/$anuncio->id.jpg", "mantenimiento/uploads/maps/$anuncio->id.jpg");
         }
         // video
         if(file_exists("uploads/videos/$anuncio->id.mp4")){
            copy("uploads/videos/$anuncio->id.mp4", "mantenimiento/uploads/videos/$anuncio->id.mp4");
        }
 
    }

 

    // archivo fecha.txt
    $archivo_fecha = fopen("mantenimiento_backups/fecha.txt", "w");
    fwrite($archivo_fecha, $fecha);
    fclose($archivo_fecha);


    rename("mantenimiento", "mantenimiento_backups/$fecha"); 

    /*

    if(file_exists('mantenimiento')){ 
        $root = $_SERVER['DOCUMENT_ROOT']; 
        $fecha_backup = file_get_contents("mantenimiento/fecha.txt");
        rename("mantenimiento", "mantenimiento_backups/$fecha"); 
    } 
  
        $zipFile 
        ->addDirRecursive('mantenimiento')
        ->saveAsFile('mantenimiento.zip')
        ->close();
    */

    // Limpia backups antiguas
  
  //  borrarArchivos();
 

}

function borrarArchivos(){
 
    // selección de todos los archivos

    $todos = glob("mantenimiento_backups/*.zip");
    arsort($todos);
    $sinlos6ultimos = $todos;
    if(count($todos) > 6){
        $sinlos6ultimos = array_splice( $sinlos6ultimos, 6);

        
        $delas23h = glob("mantenimiento_backups/*_23.zip");
        arsort($delas23h);
        $eliminar = array_diff($sinlos6ultimos, $delas23h);
            // Selección las backups a las 23h
            foreach($eliminar as $el){
            unlink($el);
            }

    // Array sin los 6 ultimos
    }
  
}

 function listarZips($opt = []){

    $dir = $opt["path"];
    $listado = glob("$dir/*.tar.gz");
    $lista = [];
    foreach ($listado as $item){
        $item = str_replace("$dir/", "", $item); 
        $lista[] = $item;
    }
    arsort($lista);

    return $lista;

}   
 
 

 function redireccion_mantenimiento(){
   // El administrador podra ver el sitio real y google
    if(strstr(strtolower($_SERVER['HTTP_USER_AGENT']), "googlebot") OR isset($_SESSION['adm']))
    {
        return false;
    }
 
     // MANTENIMIENTO
    
    if ( file_exists('mantenimiento_1.txt') AND !str_contains($_SERVER['REQUEST_URI'], 'login') AND !str_contains($_SERVER['REQUEST_URI'], 'mantenimiento')){
       
        // Permitir acceso a login 
        if ( str_contains($_SERVER['REQUEST_URI'], 'anuncios')){ 
          header("Location: /login"); 
          die();
        }
        // Si aun no es admin redireccionar a mantenimiento

        if (!str_contains($_SERVER['REQUEST_URI'], 'mantenimiento')){ 
            header("Location: /mantenimiento/inicio");  
        } 
         
    } 
  
 }
    
    

 function modomantenimiento(){
    //toggle

    if (file_exists("mantenimiento_1.txt")){
         rename("mantenimiento_1.txt", "mantenimiento_0.txt");
         header("Location: /administrador");
        return false;
    }

    if (file_exists("mantenimiento_0.txt")){
        rename("mantenimiento_0.txt", "mantenimiento_1.txt");
        header("Location: /administrador");
        return true;
    }
}
 
function restaurarzip($opt = []){
    global $zipFile;  
    
    $ruta_mantenimiento_backups = "mantenimiento_backups";
    if (!file_exists($ruta_mantenimiento_backups)) mkdir($ruta_mantenimiento_backups); 

    $name = $opt["name"];
    $ruta = "mantenimiento_backups/$name";
    if (file_exists("mantenimiento")) deleteAll("mantenimiento");

    // Creamos carpetas
    $ruta_mantenimiento = "mantenimiento";
    if (!file_exists($ruta_mantenimiento)) mkdir($ruta_mantenimiento); 

    $zipFile
    ->openFile($ruta)
    ->extractTo("mantenimiento")
    ->close();

    $fecha = fopen("mantenimiento/fecha.txt", "w");
    $txt = str_replace('.zip', '', $name);
    fwrite($fecha, $txt);
    fclose($fecha);

  
}



 /*================= END OF ADMINISTRADOR BACKUPS STATIC HTML ====================================================*/
 
 
 // API que retorna el estado del mantenimiento para sacar a los visitantes de version de mantenimiento
 
 

if( isset($_GET["mantenimiento_activo"]) & isset($_GET['api']) ){
 
    if (file_exists("mantenimiento_1.txt")){
        echo json_encode(["message" => 1]); 
        return 1;
    }else{
        echo json_encode(["message" => 0]);  
        return 0;
    }
     
}

