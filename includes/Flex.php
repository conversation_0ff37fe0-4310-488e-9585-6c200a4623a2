<?php
 
#[AllowDynamicProperties]
class Flex
{ 
    public readonly string $method,$is_page, $dev;
    public  $view, $view_path, $args, $route, $error; // Modificada por funciones_fun.php 

    public function __construct(array $config = [])
    {
        $this->request_uri = $_SERVER["REQUEST_URI"];
        $this->zone = $this->zone();
        $this->method = strtolower( $_SERVER["REQUEST_METHOD"]);
        $this->args = $this->args();
        $this->view = $this->view();
        $this->view_path = $this->view_path();
        $this->route = $this->route();
        $this->is_page = $this->_is_page();
        $this->dev = $this->dev();
        $this->router_route = "";
        $this->router_parameters = "";
        $this->path = $this->path();
        //$this->user = $this->userDefault();
        // Inicializar user con los datos de la sesión
       //
        // $_SESSION['user'] = $_SESSION['user'] ?? ["email" => "anonimo", "tel" => "+34", "rol" => 0];

       
    }
 
     function zone(){
        // Eliminando parametros de la url & y ? 
        $request_uri = $_SERVER['REQUEST_URI'];
        $request_uri = preg_replace('/\?.*|&.*$/', '', $request_uri);
        if ($request_uri == "/") return "index"; 
        // Si existe carpeta en /paginas con el primer valor de la url uri(1)
        if (file_exists("paginas/" . $this->uri(1))) return $this->uri(1);
        // Si no existe carpeta de zona, usamos index
        return "index";
    }

    function _is_page(){ 
        // Generar url de la url actual
        $url = $_SERVER['REQUEST_URI']; 
        // Eliminar slash al final de la url
        $url = rtrim($url, '/');
        // Devolver false si la url contiene x o api
        if($this->uri("api") || isset($_GET["x"]) || isset($_GET["api"]) || isset($_GET["action"])) {
            return false;
        } 
        return true;
    }
 

    public function arrayToObject($array)
    {
        if (is_array($array)) {
            return (object) array_map([$this, 'arrayToObject'], $array);
        }
        return $array;
    }

    /* @param $name,
     * @param $default
     */



    // Redirección infalible si falla usa location de js
    public function redirect($dest = false){  
       
      //  if (!flex()->is_page) return;  
        $url = $dest ? $dest : $this->route(); 
        echo "<script>window.location.href='$url'</script>"; 
    }

    public function view_path(){ 
        // Es una carpeta con index.php dentro
        if(file_exists("paginas/" . $this->view . ".php")){ 
            return "paginas/" . $this->view . ".php";
        }else{
            // Es una pagina concreta
            return "error";
        } 
    } 

    private function args(){
        // capturamos ultimo valor
        $last = $this->uri(-1);
        // Si el ultimo valor contiene dos puntos
        if ( str_contains( $last, ":") ) {
            // Formateamos a json
            $args_json = $this->ejson($last);
            // objeto
            $args_obj = json_decode($args_json);
            return $args_obj;
        } else{
            return 0;
        }
    }

    private function route(){
        if( $_SERVER['REQUEST_URI'] == "/" ) return "/"; 
        if($this->view == "index/index") return "/";
        $view = str_replace("/index", "", $this->view);
        return "/". $view ."/";
    }

    private function path(){
        if( $_SERVER['REQUEST_URI'] == "/" ) return "/";   
        $path = $this->route();
        // Elimina el / inicial si existe
        $path = ltrim($path, "/");
        // Elimina el slash al final del string
        $path = rtrim($path, "/");
        // Si la ruta es la raiz y esta vacia
        if($path == "") return "/";
        return $path;
    }

    private function view(){     
        // Si la vista es la raiz
        if( $_SERVER['REQUEST_URI'] == "/" ) return "index/index";  
        // Si el ultimo valor de la vista coincide con el patron final de numero / eliminamos el numero y slash final
        $view = preg_replace('/\/\d+\/$/', '/', $this->uri(0)); 
        // Si el ultimo caracter es un slash lo eliminamos el ultimo caracter
        $view = rtrim($view, "/");  
        // Elimina el slash del inicio
        $view = ltrim($view, "/");  
        // si la petición es index uncamente sin vista
        if(!$view) return "index/index";   
        // Si la zona y la vista coinciden
        if ($this->zone == $view) return $this->zone . "/index";  
        // Si la vista es una carpeta con un index
        if (file_exists("paginas/" . $view . "/index.php")) return $view . "/index"; 
        // Si la vista es un archivo
        if (file_exists("paginas/" . $view . ".php")) return $view;
         
        return false;
    }

    public function isJson($string)
    {
        json_decode($string);
        if (json_last_error() === 0) {
            return true;
        } else {
            return false;
        }
    }

    public function debugObj($obj){

        if (!isset($obj->args)) return false;

        //  $obj->args_json = json_encode($obj->args);
        // $obj->args_obj = json_decode($obj->args_json);
        $obj->args_array = $this->object_to_array($obj);
        foreach($obj->args_array as $key=>$value){
            if(is_array($value)){
                $obj->args_array[$key] = json_encode($value);
            }
        }

        $obj->args_array = $this->ksortRecursive($obj->args_array);
        return (object) $obj->args_array;
    }


    /**
     * ==========================================
     * \* Convertir una representación de cadena de una estructura de datos compleja a formato JSON
     * ==========================================
     *
     * Esta función toma una cadena que representa una estructura de datos compleja, que puede incluir mezclas de arrays y objetos
     * (indicados por corchetes y pares clave-valor, respectivamente), y la convierte en una cadena compatible con JSON.
     * Maneja estructuras anidadas y las convierte adecuadamente. La transformación incluye:
     * - Sustituir los corchetes por llaves para denotar objetos.
     * - Encerrar las claves y los valores entre comillas dobles.
     * - Asegurar que la cadena completa sea un objeto JSON válido envolviéndola en llaves.
     *
     * @param string $str La cadena de entrada que representa la estructura de datos compleja.
     * @return string Una cadena formateada como un objeto JSON, lista para ser decodificada o procesada.
     */

    public function ejson(string $str): string
    {
        $str = str_replace([':[', ']'], [':{', '}'], $str);
        $str = preg_replace('/(\w+):/', '"\1":', $str); // Enclose keys in double quotes
        $str = preg_replace('/:([^,\{\}]+)/', ':"\1"', $str); // Enclose values in double quotes
        // Eliminamos las comilas del string a todos los elementos numéricos
        $str = preg_replace('/"(\d+)"/', '\1', $str);
        return '{'.$str.'}';

    }

    public function object_to_array($obj) {
        //only process if it's an object or array being passed to the function
        if(is_object($obj) || is_array($obj)) {
            $ret = (array) $obj;
            foreach($ret as &$item) {
                //recursively process EACH element regardless of type
                $item = $this->object_to_array($item);
            }
            return $ret;
        }
        //otherwise (i.e. for scalar values) return without modification
        else {
            return $obj;
        }
    }

    public function ksortRecursive($array) {
        foreach ($array as &$value) {
            if (is_array($value))
                $this->ksortRecursive($value);
        }
        ksort($array);
        return $array;
    }

    /**
     * ==========================================
     *
     *  Normaliza los separadores de
     *  directorios en una ruta.
     *
     * ==========================================
     * @param string $path La ruta de archivo o directorio a normalizar.
     * @return string La ruta con los separadores normalizados a barras normales (/).
     */

    function normalizePathSeparator(string $path): string
    {
        return str_replace("\\", "/", $path);
    }

    /**
     * ==========================================
     *  * Refina y manipula segmentos de la URL basándose en parámetros proporcionados
     * ==========================================
     *
     * Esta función `uri` es una herramienta versátil para el manejo de URLs, permitiendo no solo acceder a segmentos específicos,
     * sino también modificar la URL recortando segmentos desde el inicio o el final, y ofreciendo múltiples formatos de salida.
     * Es ideal para operaciones que requieren una manipulación precisa de la URL, como el enrutamiento o la generación de enlaces.
     *
     * @param mixed $param Define la operación a realizar sobre la URL:
     *    - Si es numérico (positivo o negativo), selecciona un segmento específico de la URL.
     *    - Si es una cadena, puede ser:
     *      - "full" para obtener la URL completa.
     *      - "a" para obtener un array de los segmentos de la URL.
     *      - "json" para obtener los segmentos de la URL en formato JSON.
     *      - Una expresión regular (precedida por ":") para buscar coincidencias en la URL.
     *      - Cualquier otro valor de cadena para verificar su presencia en la URL.
     *
     * @param mixed $param2 Opcional. Se utiliza para definir el recorte de segmentos de la URL:
     *     - Si es numérico, indica cuántos segmentos recortar desde el final.
     *     - Si se omite o es "no", no se realiza recorte adicional.
     *
     * @return mixed El resultado varía según los parámetros:
     *   - Un segmento específico de la URL o false si no existe.
     *   - La URL completa, un array de segmentos, o un JSON de segmentos.
     *   - True o false para búsquedas de cadenas o expresiones regulares.
     *   - Una URL modificada tras recortar segmentos.
     *
     * Ejemplos de uso:
     *   - Obtener el primer segmento de la URL: `uri(1)`.
     *   - Obtener el último segmento de la URL: `uri(-1)`.
     *   - Obtener la URL completa: `uri("full")`.
     *   - Obtener los segmentos de la URL como array: `uri("a")`.
     *   - Verificar la presencia de una cadena en la URL: `uri("cadena")`.
     *   - Buscar con expresión regular: `uri(":^inicio")`.
     *   - Recortar los primeros 2 segmentos y los últimos 2: `uri(3, 2)`.
     *     Esto tomará la URL, eliminará los primeros 2 segmentos y luego los últimos 2 del resultado.
     *   - Recortar el primer segmento y devolver el resto de la URL: `uri(2, 0)`.
     *     Si la URL es "/inicio/servicios/contacto", el resultado será "/servicios/contacto".
     */

    public function uri($param = null, $param2 = null)
    {

        $request = isset($_GET['url']) ? strtolower($_GET['url']) : '/';
        $request = "/" . trim($request, '/') . "/";
        $requestArray = explode('/', $request);
        // Limpia el array eliminando elementos vacíos, excepto el '0' como cadena
        $requestArray = array_filter($requestArray, function ($value) {
            return $value !== '' && $value !== null;
        }); 

        if (($param === null || $param === 0) && $param2 === null) return $request;

        // Devuelve la URL completa
        if ($param === "full") return $_SERVER["REQUEST_URI"];
        // Devuelve los segmentos como un array
        if ($param === "a") return $requestArray;
        // Devuelve los segmentos como JSON
        if ($param === "json") return json_encode($requestArray, JSON_UNESCAPED_SLASHES);

        if (is_string($param)) {
            if (str_starts_with($param, ":")) {
                // Búsqueda de expresiones regulares
                $pattern = '/' . substr($param, 1) . '/';
                $full_request = $_SERVER["SERVER_NAME"].$request;
                preg_match($pattern, $full_request, $matches);
                return $matches[0] ?? false;
            }
            // Buscar si $param str esta en la URL
            if( strpos($_SERVER["REQUEST_URI"], $param) !== false ){
                return true;
            }else{
                return false;
            } 
        }
  
        /* Recortar la url */
        if (is_numeric($param2)) {
            // Recortar segmentos de la URL
            $inicio = $param;
            $final = count($requestArray) - $param2;
            $slicedArray = array_slice($requestArray, $inicio, $final - $inicio);
            return '/' . implode('/', $slicedArray) . '/';
        } else {
            /* ------ Devolver un valor por la url -----------*/
            $index = $param > 0 ? $param : count($requestArray) - abs(++$param);
            return $requestArray[$index] ?? false;
        }

    }

    /**
     * Fetches a single row from the database using the provided SQL query.
     *
     * @param string $sql The SQL query to execute.
     * @return mixed The fetched row.
     */
    public function fetch(string $sql): mixed
    {
        global $pdo;
        return $pdo->query($sql)->fetch();
    }

    /**
     * Fetches all rows from the database using the provided SQL query.
     *
     * @param string $sql The SQL query to execute.
     * @return array|false The fetched rows.
     */
    public function fetchAll(string $sql)
    {
        global $pdo;
        return $pdo->query($sql)->fetchAll();
    }

    /**
     * Executes a SQL query and returns the PDOStatement object.
     *
     * @param string $sql The SQL query to execute.
     * @return PDOStatement The PDOStatement object resulting from the query.
     */
    public function query(string $sql): PDOStatement
    {
        global $pdo;
        return $pdo->query($sql);
    }

    /**
     * ==========================================
     * \* Determina si el entorno es de desarrollo
     * ==========================================
     *
     * Esta función `dev` verifica si el entorno actual es de desarrollo. La determinación se basa en:
     * - Si el sistema operativo es Windows, siempre se considera un entorno de desarrollo.
     * - Si el nombre del servidor comienza con "dev." o termina con ".test".
     *
     * @return bool Devuelve `true` si el entorno es de desarrollo, de lo contrario `false`.
     */

    public function dev() : bool{
         if(PHP_OS == 'WINNT') {
            return true;
        }
        $dominio = $_SERVER['SERVER_NAME'];

        if (preg_match("/^dev./", $dominio) == true || preg_match("/.test$/", $dominio) == true || preg_match("/.dev$/", $dominio) == true ) { // Otra opcion: self::uri(":\.test")){

            return true;
        } else{
            return false;
        }
    }

    public function debugbarInfo($msg){
        global $debugbar;
        $debugbar["messages"]->addMessage($msg);
    }
 
}
 
// Crear la instancia solo si no existe
if (!isset($flex)) {
    $flex = new Flex();
}

function flex() : Flex{
    global $flex;
    return $flex;
}
 