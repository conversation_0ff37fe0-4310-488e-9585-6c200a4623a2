<?php
namespace app;
date_default_timezone_set('Europe/Madrid');
 
// Si no se pasa valor devuelve toda la ruta -> /usuarios/listar
// Si se pada un número devuelve el parametro -> usuarios
function uri($num = 'path')
{
    $request = isset($_GET['url']) ? strtolower($_GET['url']) : '/';
   
    if ($num !== 'path') { // Si se quiere una posicion (Si le pasas argumneto)
       
        $requestArray = explode('/', $request); 
          
        if( isset($requestArray[$num]) ){
            return $requestArray[$num] ; 
        } else { 
            Config::$uri = $request;
            return false;
        }
       
    } else { // Ruta completa
        $request = isset($_GET['url']) ? '/'. strtolower($_GET['url']) : '/';
        Config::$uri = $request;
        return $request;
    }
}

$uri = uri();
$uriAr = explode('/', $uri);
$uriEnd = end($uriAr);


//Config::$page = uri() == '/' ? Config::$home : uri(0);

function getPageName(){

    // Admin zone
    if ( (strlen(uri(1)) >= 1) && uri(0) != "anuncio"){
        $section = uri(0);
        $page = uri(1);
        $path = "/$section/$page";
    }else{
        $section = "";
        $page = uri(0);
        $path = "/$page";
    } 

     $path = Config::$pages.$path.".php"; 
  
    if (  file_exists( $path ) ) {
        Config::$page = $page;
    }else{
         Config::$page = "404";
    } 

    return Config::$page;
}
 
function init($opt)
{
    uri();
    set_dev($opt['dev']);
   
    foreach($opt as $key=>$value){
        if ($key == "dev") continue;
        if ($key == "extra") continue;
        Config::${$key} = $value;
    }

    //Extra info to init example
    Config::add('example', true);
}

function redirect($dest = false){
    global $uri;
   // if (!flex()->is_page) return;
   
    $dest = $dest ? $dest : $uri;
    echo "<script>window.location.href='$dest'</script>"; 
}

function set_dev($opt){ 
   
// FORZAR UN MODO MANUALMENTE ( Si no esta modo auto )
    if ($opt != 'auto'){
        Config::$dev = $opt;
        return $opt;
    }
    
// SI ENTRA AQUI SERA DETECCIÓN AUTOMATICA 

    // 1. SI ES WINDOWS SIEMPRE SERA DEV
    if(PHP_OS == 'WINNT') { 
        Config::$dev = true;
        return true;
    } 

    // 2. SUBDOMINIO DEV. SIEMPRE SERA DEV
    $dominio = $_SERVER['SERVER_NAME']; 
    $exp = "/^dev./";

    if (preg_match($exp, $dominio) == true){
        Config::$dev = true; 
        return true;
    } else{
        Config::$dev = false; 
        return false;
    }
  
}


// Retorna el entorno
function dev(){
    return Config::$dev;
}

function partial($name)
{
    $fullpath = Config::$partials . "/$name.php";
    if (file_exists($fullpath)) {
        return $fullpath;
    } else {
        throw new \Exception("El archivo parcial no existe en: $fullpath");
    }
}

function head($name = false){

    if ( $name ) $name = "_$name";
    $fullpath = Config::$partials . "/head$name.php";
    if (file_exists($fullpath)) {
        include $fullpath;
    }  

}

function nav($name = false){
    if ( $name ) $name = "_$name";
    $fullpath = Config::$partials . "/nav$name.php";

    if (file_exists($fullpath)) {
        include $fullpath;
    } else {
         return false;
    }
}

function footer($name = false){
    if ( $name ) $name = "_$name";
    $fullpath = Config::$partials . "/footer$name.php";
    if (file_exists($fullpath)) {
        return $fullpath;
    } else {
        throw new \Exception("El archivo footer no existe en: $fullpath");
    }
}

function debug($var)
{
    echo "<pre>";
    print_r($var);
    die();
}
function msgjs($var)
{
    $var = json_encode([$var]);
    echo "<script>console.dir($var)</script>";
}


function console($var){
    echo "<pre>";
    json_encode($var);
    var_dump($var);
    $var = json_encode(["config"=>$var]);
    echo "<script>console.dir($var)</script>";
    die();
}


// Retorna ruta completa
function part($part){
    
    $fullpath = Config::$pages . "/". uri(0) . "/$part.php";

    if (file_exists($fullpath)) {
    return $fullpath;
    } else {
        throw new \Exception("El archivo part no existe en: $fullpath");
    }
 
}
  
// Depende de uri()


function controller()
{
    $name = uri(0);
   
    Config::$controller = $name; 
    $fullpath = Config::$controllers . "/$name". "Controller.php";
   
   // $home = Config::$controllers . "/" . Config::$home . "Controller.php";
      
    // CARGA DEL ARCHIVO CONTROLADOR DE PAGINA
    if (file_exists($fullpath)) {
        return $fullpath;
    } 
}

function section(){
    return strlen(  uri(1) ) > 0 ? uri(0) : false; 
}


function page()
{
      // Admin zone
      if (strlen(uri(1)) > 0 && uri(0) != 'anuncio'){
        $section = uri(0);
        $page = uri(1);
        $path = "/$section/$page";
    }else{
        $section = "";
        $page = uri(0);
        $path = "/$page";
    } 

    $fullpath = Config::$pages.$path.".php";   
 
    if (file_exists($fullpath)) {
        return $fullpath;
    } else {
        http_response_code(404);
        echo "<script>console.log('No se encontra la página $fullpath')</script>"; 
        header("Location: /inicio/");
    }
   
}

// recorre los parametros y devuelve true si la pagina esta en args NO FUNCIONA EN INICIO
function ispage(...$args){

    foreach($args as $arg){
        if (uri(0) == $arg){
            return true;
        }
    }

    return false;

}

// Dependencia de consolelog
function globals()
{
    $globales = [];
    foreach ($GLOBALS as $key => $value) {
        if (
            $key == 'dsn' ||   $key == 'pdo' || $key == 'pdo_options' || 'faker'
        ) {
            continue;
        }
 
    } 
 

    return $globales;
}

// get('usuarios', 'admin', 'posts')

function api(){
    if  ( isset($_GET['api']) ) return true; 
}

// Depende de globals
function debug_config()
{
    $ReflectionClass = new \ReflectionClass('App\Config');
    $props = $ReflectionClass->getStaticProperties();
    
    $retorno = [
        "Config" => $props 
    ];   
    return $retorno; 
}

class Config
{
    public static $css = 'css';
    public static $files = "files";
    public static $uploads = "uploads";
    public static $home = 'inicio';
    public static $js = "js";
    public static $libs = 'libs';
    public static $page404 = '404.php';
    public static $pages = 'paginas';
    public static $controllers = 'controllers';
    public static $partials = 'partials';
    public static $extra = ["test"=> "hola"];

    // Set by functions in startup
    public static $dev;
    public static $uri;
    public static $page;
    public static $controller;

    static function add($value){
 
       self::$extra[] = $value;
    }
}