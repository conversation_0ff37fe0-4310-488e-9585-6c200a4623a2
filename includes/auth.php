<?php

 
#[AllowDynamicProperties]
class User{  
    public $user;  
    public function __construct(){
        $_SESSION['user'] = $_SESSION['user'] ?? (object)["id"=>0,"email" => "anonimo", "tel" => "+34", "rol" => 0]; 
        $this->user =  &$_SESSION['user'];   
    }
}
 /* Agregación de user al objeto flex */
 
// instanciación del objeto
if (!isset($_SESSION['user'])) {  
    $user = new User();
}

// Asignar directamente la sesión a flex()->user
flex()->user = &$_SESSION['user'];
 
/* --------------------  USUARIOS NO REGISTRADOS ----------------------------------------- */

  if (flex()->route == "/adm" ) flex()->redirect("/adm/anuncios/");
 // if (flex()->route == "/panel" ) flex()->redirect("/panel/inicio/");
  
  if (flex()->user->rol == 0 && (flex()->zone == "panel" || flex()->zone == "adm")) {

    // Si es la ruta de login o registro o codigo salir 
    if( flex()->route != "/panel/entrar" && flex()->route != "/adm/login" && 
    flex()->route != "/panel/registrarse" && flex()->route != "/panel/entrar-codigo"){ 

        if (flex()->zone == "panel") {
          flex()->redirect("/panel/entrar");
        } else {
          flex()->redirect("/adm/login");
        } 
    } 
  }





/* --------------------  USUARIOS REGISTRADOS ZONAS ----------------------------------------- */
flex()->zones = ["panel" => "2", "adm"=> "5"];
flex()->zone_rol = array_key_exists( flex()->zone, flex()->zones) ? flex()->zones[flex()->zone] : 0;
if ( flex()->user->rol > 0 && flex()->user->rol < flex()->zone_rol ) flex()->redirect("/panel/entrar");
 

/* --------------------  FUNCIONES ----------------------------------------- */


  
// http://citas10.test/entrar/&fun=userCreate("email":"<EMAIL>","tel":"600600600")&api

function userCreate($opt = []){
    global $pdo; 
    $email = isset($_POST["email"]) ? $_POST["email"] : $opt["email"];
    $tel = isset($_POST["tel"]) ? $_POST["tel"] : $opt["tel"];
   // Crear codigo verificación email
   $code = rand(1111,9999);

    $sql = "INSERT into usuarios VALUES(NULL,2,'$email',0,'$tel',$code,NOW())";

    // Comprobar si existe ya ese email registrado
    $usuario = getfun("getUsuario(\"email\":\"$email\")")["data"];
    // Si usario existe retornamos falso
    if ($usuario) return ["sql"=>"$sql","data"=>false];
 
    // Crear usuario 
    $pdo->query($sql);
    
    $id = $pdo->lastInsertId();

    return ["sql"=>$sql,"data"=>["id"=>$id,"email"=>$email]];
        
}

// http://citas10.test/adm/x&fun=usuarioEdit("id":29)

function usuarioEdit($opt = []){
 
    global $pdo;  
    $id = $opt["id"]; 
    $rol = isset($_POST["rol"]) ? ",rol=".$_POST['rol']: "";
    $email = isset($_POST["email"]) ? $_POST["email"] : $opt["email"];
    $tel = isset($_POST["tel"]) ? $_POST["tel"] : $opt["tel"];
    
    $sql = "UPDATE usuarios SET email='$email',tel='$tel'$rol WHERE id=$id";  
  
    // Editar usuario
    $pdo->query($sql);   
    
    $retorno = ["sql"=>$sql];

     return $retorno;
    
}

// http://citas10.test/entrar/&fun=userCodeCreate("id":1)

function userCodeCreate($opt = []){

    global $pdo; 
  
    $id = $opt["id"];
    $code = rand(1111,9999);
 
    $sql = "UPDATE usuarios SET code=$code,code_time=now() WHERE id=$id";  

    $pdo->query($sql);

    $usuario = getfun("getUsuario(\"id\":\"$id\")")["data"];
    $userMail = $usuario->email;

    sendMail("Código de acceso", "<p>Se ha solicitado el acceso a un nuevo dispositivo con el usuario: $userMail, <br> El código de acceso es:<br> </p><h1>$code</h1>
   <p> 
   * Si no ha sido usted y/o recibe varios mensajes como este, avisenos del intento no autorizado a su cuenta.<br>
   * Le recordamos que este código solo es válido por 10 minutos y es de un solo uso.<br>
   * Cualquier problema, sugerencia o duda puede contactar con nosotros. <br> </p> <br>
   Agradecemos que use nuestra plataforma y esperamos que encuentre lo que busca en ella</p> <br><br><strong>A su disposicion, el equipo de citas10</strong></p>", $userMail);

  // app\redirect("/panel/entrar-codigo/&id=$id&email=$userMail");

    return $usuario;
    // Crea un código y resetea el code_time. 

}

// para adm/usuarios, no envia email
function userResetCode($opt = []){
    global $pdo;
    $id = $opt["id"];
    $code = rand(1111,9999);
    $sql = "UPDATE usuarios SET code=$code,code_time=now() WHERE id=$id";
    $pdo->query($sql);

    echo "<strong>$code </strong> Expira en 10 min";

}

// http://citas10.test/entrar/&fun=userLoginOk("id":1,"code":7811) 

function userLoginOk($opt = []){ 

    $id = $opt["id"];
    $code = isset($_POST["code"]) ? $_POST["code"] : $opt["code"];
    if($code < 1){
        echo "No se aporto codigo";
        return;
    } 

    $usuario = getfun("getUsuario(\"id\":\"$id\")")["data"];

    $ahora = date('Y-m-d H:i:s');
 
    $expired_time =  addTimeToTimestamp($usuario->code_time, 10, "minutes");
   
 
    if($code == $usuario->code){
        if($ahora > $expired_time){
         echo "
        <div class='alert alert-warning' role='alert'> 
        El código <strong>$code</strong> ha expirado <br>
        </div>
        ";  
        }else{ 
            $_SESSION['user'] = [
                "rol"=>$usuario->id_rol,
                "email"=>$usuario->email,
                "tel"=>$usuario->tel,
                "id"=>$usuario->id
            ];

           /* echo "
            <div class='alert alert-success' role='alert'> 
            Código correcto, Preparando su panel: <br> 
            </div>
            <div hx-get='&fun=timer()&api' hx-swap='outerHTML' hx-trigger='load' hx-target='this'>
            <div>  
            ";
            */

            flex()->redirect("/panel/inicio/");
            return true;

        }
        // tiempo

    }else{ 
         echo "
        <div class='alert alert-danger' role='alert'> 
        El código <strong>$code</strong> NO es correcto <br>
        </div>
        "; 
    } 
}

 
function usuarioGetCode($opt = []){

    global $pdo;
    $id = $opt['id']; 

    $usuario = getfun("getUsuario(id:$id)")["data"];

    echo $id;
    return;

    $expired_time = addTimeToTimestamp($usuario->code_time, 10, "minutes");
    $ahora = date_create(date('Y-m-d H:i'));
    $expire = date_create($expired_time);
    // diferencia en minutos y segundos 00:00 entre $ahora y $expire
    $diff_mins = round(abs($ahora->getTimestamp() - $expire->getTimestamp()) / 60);
    $estado_code = $ahora > $expire ? "EXPIRADO" : "Expira en $diff_mins min";

    echo "<span>CÓDIGO: $id</span>";

}

// htmx

function reenviarcodigo($opt = []){   
    $id = $opt['id']; 
    userCodeCreate(["id"=>$id]);
    echo "
    <div class='m-2 alert alert-warning alert-dismissible fade show' role='alert'> 
        Nuevo codigo generado
        <button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Close'></button>
    </div>
    ";
};
 
/*

post('/adm/usuarios-resetcode/$id', function($id){
    $code = rand(1111,9999);
    flex()->query("UPDATE usuarios SET code=$code,code_time=now() WHERE id=$id");
    echo "<span id='code1'>CODIGO: <strong>$code </strong> <br> <span hx-get='/adm/x/usuarios-codestatus/$id' hx-trigger='load, every 5s' hx-swap='innerHTML'></span> </span>";
});



get('/adm/usuarios-codestatus/$id', function($id){
    $usuario = flex()->fetch("SELECT code_time FROM usuarios WHERE id=$id");

    // Agregamos 10 minutos a la fecha de creacion para saber la fecha que daduca
    $expired_time = addTimeToTimestamp($usuario->code_time, 10, "minutes");
    $expire = date_create($expired_time);

    // Creamos fecha actual
    $ahora = date_create(date('Y-m-d H:i:s'));

    // Calculate the difference in seconds
    $diff_seconds = $expire->getTimestamp() - $ahora->getTimestamp();

    $diff = date_diff($ahora, $expire)->format('%imin y %s seg');

   // echo $ahora->format('H:i:s');
   echo $diff_seconds > 0 ? "   Expira en: ". $diff : " Expirado ";
});

*/
 
// http://citas10.test/panel/x/&fun=cerrarsesion

function cerrarsession(){
    session_destroy();
    flex()->redirect();
};


 