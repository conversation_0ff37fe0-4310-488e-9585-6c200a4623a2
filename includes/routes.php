<?php

/** @var \Anuncios $anuncio */


/* ---------------------------------- */

  
get('/cerrarsession', function(){
    session_destroy();
    header("Location: /inicio");
});


// =================================================================

//             FUNCIONES DE PANEL

// =================================================================

  

post('/panel/x/galeria/fotoid/$id/filename/$filename/tapar', function($id, $filename){
    global $pix;

    $path = "uploads/fotos/$filename";
    $path_bk = "uploads/fotos/$filename.bk";
    $domain = $_SERVER['SERVER_NAME'];
    $img = "https://$domain/uploads/fotos/$filename";
    // Restaurar
    if ( file_exists($path_bk) ){
        unlink($path);
        rename($path_bk, $path);
        header("HX-Refresh: true");
        return true;
    }
    copy($path, $path_bk); // crear backup

    /* Invoke facedetect first  */
    if( !$pix->get('facedetect',array('img' => $img)) ){
        header("HX-Refresh: true");
    }
    /* Grab the total number of detected faces */
    $faces = $pix->json->faces;

    if( !$pix->post('mogrify', ['img' => $img,'cord' => $faces]) ){

    }else{
        $link = $pix->json->link;
    }

    $link = file_get_contents($link);
    file_put_contents($path, $link);

    header("HX-Refresh: true");
});


post('/panel/x/galeria/anuncioid/$anuncioid/fotoid/$fotoid/destacar', function($anuncioid, $fotoid){

    global $pdo;
    $id = $fotoid;
    $id_anuncio = $anuncioid;
    $set = $_POST['set']; // 1 on 0 off

    $sql = "UPDATE fotos SET destacado = $set WHERE id = $id";

    $fotosSeleccionadas = $pdo->query("SELECT * FROM fotos WHERE id_anuncio = $id_anuncio AND destacado = 1")->fetchAll();

    if ($set == 1 and count($fotosSeleccionadas) >= 2) {
        echo json_encode(["message" => "Limite de fotos superado"]);
        http_response_code(401);
        exit;
    }

    try {
        $pdo->query($sql);
        actualizarPortada(["id"=>$id_anuncio]);
        // pushMessage("Foto $id seleccionada total ".  ++$fotosSeleccionadas, $tipo = "success");
    } catch (Exception $e) {
        // pushMessage("Ocurrio un error al seleccionar la foto $id", $tipo = "error");
    }

    echo json_encode(["message" => "ok"]);

    return ["sql"=>$sql];

});

get('/panel/galeria-taparcara/id/$id/fotoid/$fotoid/file/$filename', function($id, $fotoid,$file) {
    $path = $_SERVER["DOCUMENT_ROOT"] . '/paginas/panel/_galeria-taparcara.php';
    include $path;
});

get('/inicio', function() { 
    echo "impresion";
    flex()->debugbarInfo("ruta coincide");
    return "Hola";
});

get('/inicio/nombre/$nombre', function($nombre) { 
    echo "Hola $nombre"; 
});


get('/inicio/nombre/$nombre/edad/$edad', function($nombre, $edad) { 
    echo "Hola $nombre, tienes $edad años"; 
});

get('/api/inicio/nombre/$nombre', function($nombre) { 
    echo "Hola $nombre";
    return "Hola $nombre"; 
});

get('/panel/inicio/nombre/$nombre', function($nombre) { 
    echo "Hola $nombre";
    return "Hola $nombre"; 
});

get('/panel/route/nombre/$nombre', function($nombre) { 
    echo "Hola $nombre";
    return "Hola $nombre"; 
});

get('/inicio/route/numero/$numero', function($numero) { 
    echo $numero * 2; 
});

 
get('/inicio/orden/$orden/categoria/$categoria', function($orden, $categoria) { 
    echo "<h2>Orden: $orden</h2>";
    echo "<h2>Categoria: $categoria</h2>";
});

get('/seccion/nueva/orden/$orden', function($orden) { 
    echo "<h2>La funcion imprime el parametro, Orden: $orden</h2>"; 
});

get('/seccion/nueva/sinparametros', function() { 
    $suma = 5+5;
    echo "<h2>sin parametros: 5+5 es igual a $suma </h2>";  
});

get('/seccion/nueva', function() { 
     $suma = 5+5;
    echo "<h2>sin parametros: 5+5 es igual a $suma </h2>";  
});

get('/seccion/nueva/orden/$orden/segundo/$segundo', function($orden, $segundo) { 
    echo "<h2>La funcion imprime el parametro, Orden: $orden segundo $segundo</h2>"; 
});

get('/blog/articulo/$articulo', function($articulo) { 
    echo "<h2>Blog articulo $articulo</h2>"; 
});
    
get('/blog/articulo/$articulo/filtro/$filtro', function($articulo, $filtro) { 
    echo "<h2>Blog articulo $articulo filtro $filtro</h2>"; 
});

get('/blog/articulo/id/$id', function($id) { 
    echo "<h2>Blog articulo $id</h2>"; 
});

get('/blog/edad/$edad/numero/$numero', function($edad, $numero) { 
    echo "<h2>Blog articulo $edad numero $numero</h2>"; 
});

get('/blog/ruta', function() { 
    echo "<h2>Blog ruta</h2>"; 
});