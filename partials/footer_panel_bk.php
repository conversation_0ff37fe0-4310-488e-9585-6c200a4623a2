 

</div> <!-- .container -->
<?php if (isset($_GET["step"])) : ?>
        <?php
        $id_anuncio = app\uri(2);
        $step = $_GET['step'];
        $width = [
            "1" => 0,
            "2" => 25,
            "3" => 50,
            "4" => 72,
            "5" => 100
        ];
        $progress = $width[$step];

        $steps = [
            "1" => "detalle",
            "2" => "galeria",
            "3" => "textos",
            "4" => "horario",
            "5" => "mapa"
        ];

        ?>
        <div class="footer">

        <div class="contenido">

       

        <div class="paso d-flex justify-content-between">
            
            <button class="anterior btn btn-primary <?= $step <= 1 ? 'disabled' : ''?>"> <i class="fa-solid fa-arrow-left"></i> Anterior</button>
 
            <span style="font-size: 13px; line-height: 17px">
               <?= $step ?> de 5 <br> <?= strtoupper((app\uri(1))) ?>
            </span>

            <?php if( $step < 5 ):?> 
            <button class="btn btn-primary siguiente"> Siguiente <i class="fa-solid fa-arrow-right ps-1"></i></button>
            <?php endif?>
            <?php if( $step == 5):?>
            <button class="btn btn-success"> Finalizar <i class="fa-solid fa-rocket ps-1"></i></button>
            <?php endif?>
            </div>



        <div class="progress">
            <div class="progress-bar" role="progressbar" aria-label="Basic example" aria-valuenow="<?= $progress ?>" aria-valuemin="0" aria-valuemax="100" style="width: <?= $progress ?>%"></div>

        </div>
        <div class="bolas">
            <div data="1" class="pasobtn<?= $step >= 1 ? ' superado' : '' ?> <?= $step == 1 ? ' activa' : '' ?>">
                <div class="bola step1">1</div>
                <div class="boton2">
                    <i class="fa-solid fa-address-card fa-xl"></i>
                    <span>Detalle</span>
                </div>
            </div>
            <div data="2" class="pasobtn<?= $step >= 2 ? ' superado' : '' ?> <?= $step == 2 ? ' activa' : '' ?>">
                <div class="bola step2">2</div>
                <div class="boton2">
                    <i class="fa-regular fa-images fa-xl"></i>
                    <span>Galeria</span>
                </div>
            </div>
            <div data="3" class="pasobtn<?= $step >= 3 ? ' superado' : '' ?> <?= $step == 3 ? ' activa' : '' ?>">
                <div class="bola step3">3</div>
                <div class="boton2">
                    <i class="fa-brands fa-readme fa-xl"></i>
                    <span>Textos</span>
                </div>
            </div> 

            <div data="4" class="pasobtn<?= $step >= 4 ? ' superado' : '' ?> <?= $step == 3 ? ' activa' : '' ?>"> 
                <div class="bola step4">4</div>
                <div class="boton2">
                    <i class="fa-regular fa-clock fa-xl"></i>
                    <span>Horario</span>
                </div> 
            </div>
            <div data="5" class="pasobtn<?= $step >= 5 ? ' superado' : '' ?> <?= $step == 3 ? ' activa' : '' ?>">
                <div class="bola step5">5</div>
                <div class="boton2">
                    <i class="fa-solid fa-location-dot fa-xl"></i>
                    <span>Mapa</span>
                </div>
            </div>
        </div>



        </div>
        
        
    </div> <!-- footer -->
    <?php endif ?>

    </div> <!-- .main que agrupa un posible sidebar y container -->


    <style>
        .boton2 {
            padding-top: 12px;
            display: flex;
            height: 50px;
            flex-direction: column;
            justify-content: space-around;
            font-size: 0.9rem;
            width: 60px;
            position: relative;
            left: -13px;
            margin-top: 7px;
            cursor: pointer;
        }

        .activa .boton2 {
            background-color: #E3E9FF;
            border-radius: 5px;
            border: solid 1px #819CFF;
        }

         .boton2:hover {
            background-color: #F3F5FC;
            border-radius: 5px;
            border: solid 1px #819CFF;
        }

        .bolas .pasobtn {
            text-align: center;
            text-decoration: none;
        }

        .bolas .activa span{
            font-weight: 600; 
            
        }

        
        .footer {
            
            position: fixed;
            background-color: white;
            width: 100%;
            margin: 0 auto; 
            height: 150px;
            bottom: 60px; 
            border: solid 1px #B0C2FF;
            box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;

        }

        .bolas {
            display: flex;
            justify-content: space-around;
            width: 99%;
            position: relative;
            margin: 0 auto;
            left: 2%;
            top: 2px;

        }


        .bolas i {
            color: #C9C9C9;
        }

        .bolas .superado i {
            color: #0d6efd;
        }

        .bolas span {
            color: #8F8F8F;
            padding: 1px 3px;
        }

        .bolas .superado span {
            color: #0d6efd; 
        }

        .bola {
            color: white;
            text-align: center;
            line-height: 1.9rem;
            font-weight: normal;
            font-size: 1.2rem;
            width: 30px;
            height: 30px;
            border-radius: 30px;
        }

        .bola {
            background-color: #C9C9C9;
            cursor: pointer;
        }

        .bola:hover{
            background-color: #8AA4FF;
        }

        .superado .bola {
            background-color: #0d6efd;
        }

        .paso {
            color: #0d6efd;
            ;
            width: 100%;
            margin: 0 auto;
            text-align: center;
            padding: 4px;
            font-size: 1.3rem;
        }

        .progress {
            --bs-progress-height: 0.3rem;
            position: relative;
            top: 20px;
            width: 80%;
            margin: 0 auto;
            text-align: center;
            overflow: visible;
        }

        .footer .contenido{
            max-width: 400px;
            margin: 0 auto;
        }
 

    </style>


    <script>
        function addTagToLinks() {

            const a = document.querySelectorAll('a[href*="&fun"]');
            a.forEach(el => el.setAttribute("href", el.getAttribute('href') + '&tag=a'))

            const submit = document.querySelectorAll('form[action*="&fun"]');
            submit.forEach(el => el.setAttribute("action", el.getAttribute('action') + '&tag=submit'))

        }
        addTagToLinks();

    </script>

          
    <?php if(isset($_GET['step'])):?>

    <?php


        $edit = isset($_GET["edit"]) ? "@edit" : ""; 
        $anterior =  "/panel/".  $steps[$step-1] . "@step=". $step-1 . $edit ; 
        if($step < 5){

            $siguiente =  "/panel/". $steps[$step+1] . "@step=". $step+1 . $edit;
        }

        $fn = [
            "1"=> "&fun=anuncioActualizar",
            "4"=> "&fun=setHorario",
            "5"=> "&fun=setCoords"
        ];

        ?>
     <script>
    // ----------- STEP TO STEP PANEL$steps[$step]

    

    const steps = {
            "1":"detalle",
            "2":"galeria",
            "3":"textos",
            "4":"horario",
            "5":"mapa"
    };

     
    
    me(".anterior").on('click',ev=>{
        ev.preventDefault();
        me("form").submit();  
    });

    <?php if($step < 5): ?>
    me(".siguiente").on('click',ev=>{
        ev.preventDefault();
        me("form").submit();  
    });
    <?php endif ?>

    any(".pasobtn").on('click',ev=>{
        let step = ev.currentTarget.getAttribute("data"); 
        ev.preventDefault();  
        let href = `/panel/x/<?=$fn[$step]?>("redirect":"/panel/${steps[step]}/<?=$id_anuncio?>/@step=${step}<?=$edit?>")`;
        let formulario = me('form');
        formulario.setAttribute("action", href);
        me("form").submit();   

    });
 

    </script>

    <?php endif ?>


    </body>

    </html>