</div> <!-- .container -->
 

       <!-- <li><a href="/paginas/privacidad.html"> PRIVACIDAD</a></li>
        <li><a href="/paginas/condiciones.html">CONDICIONES</li>
        <li><a href="/paginas/politica-de-cookies.html">COOKIES</li> -->

</div> <!-- .main que agrupa un posible sidebar y container -->

<!-- Como estas amigo -->



<script src="/js/footer-bs5.js"></script>
<?php


//showAlerts();

/*
if ($dev OR isset($_GET['debugbar']))  echo $debugbarRenderer->render();

function debugbarRendered(){
}
*/  


?>

<script>

function addTagToLinks(){

  const a = document.querySelectorAll('a[href*="&fun"]');
  a.forEach(el => el.setAttribute("href", el.getAttribute('href') + '&tag=a'))

  const submit = document.querySelectorAll('form[action*="&fun"]');
  submit.forEach(el => el.setAttribute("action", el.getAttribute('action') + '&tag=submit'))
 
}
addTagToLinks();


 
</script>


<!-- Cloudflare Web Analytics -->
<script defer src='https://static.cloudflareinsights.com/beacon.min.js' data-cf-beacon='{"token": "a8d9ed55e0d54774bd45f1198f4cd617"}'></script>
<!-- End Cloudflare Web Analytics -->

</body>

</html>