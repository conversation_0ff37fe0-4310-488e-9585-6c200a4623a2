

<?php 
 // Si son las pagina de registro no se muestra la barra de menú inferior
 if(flex()->view != 'entrar' && flex()->view != 'registrarse' && flex()->view != 'entrar-codigo'):?>

 <style>

.footer{
  bottom: 0px !important; 
}

 </style>

<?php if ( !isset($_GET['step']) && !isset($_GET['edit']) ):?>

        <nav class="panelmenu navbar navbar-expand-sm fixed-bottom" style="border-top: 1px grey solid">
            <div class="container">
              
            <?php $pag = app\uri(1) ?>
            <a class="nav-link <?= $pag == "inicio" ? "active" : ""?>" href="/panel/inicio/"><i class="fa-solid fa-house fa-xl"></i><span>Inicio</span></a>
                <a class="nav-link <?= $pag == "historial" ? "active" : ""?>" href="/panel/historial/"><i class="fa-regular fa-calendar fa-xl"></i><span>Historial</span></a> 
                <a class="nav-link <?= $pag == "ayuda" ? "active" : ""?>" href="/panel/ayuda/"> <i class="fa-solid fa-question fa-xl"></i><span>Ayuda</span></a> 
                <a class="nav-link <?= $pag == "opciones" ? "active" : ""?>" href="/panel/opciones/"> <i class="fa-solid fa-gear fa-xl"></i><span>Opciones</span></a>
              

                
            </div>
        </nav>



        <style>

        .navbar.fixed-bottom{
          padding: 0;
          background-color: #F7F7F7;
        } 

        .panelmenu{
          color: #686868;
          font-size: 0.9rem;
        }

        .panelmenu .container {
          max-width:420px;
          position:relative;
          height: 60px; 
          display: flex;
          text-decoration: none;
          list-style:none;  
          justify-content: space-between;
          flex-wrap:nowrap;
        }

        .panelmenu a{
          display:flex;
          flex-direction: column;
          align-items: center;  
          position: relative; 
          padding: 2px;
          margin: 0px;
          height: 60px; 
          min-width: 25%; 
        }

        .panelmenu a:hover{
          color:white;
          background-color: #4A4A4A;
        }
        
        a.active{
          color:white;
          background-color: #4A4A4A;
        }


        .panelmenu a span{ 
        position: relative;
        display: inline-block;
          top: 30px;
        }
        
        .panelmenu a i{
          z-index: 10;
          position: relative;
          top: 20px;
        }

          

        i {
            padding-right: 5px;
        }

        #menusuperior{ 
            --bs-dropdown-divider-margin-y: 0;
        }

        #menusuperior a{
            padding: 10px 10px;
        }
        
        .fixed-bottom{
          background-color: white;
        }

        .footer{
          bottom: 60px !important; 
        }

        </style>
        
<?php endif; ?>

<script>


/*
    onloadAdd(_ =>{
        me('#menusuperior').styles();
        console.log("loading finished");
    }

    )
    onloadAdd.styles('top: 30').console.log("he"); 

    */

</script>

<?php endif; ?>