
<?php
$id = (int) Flex()->uri(-1);

 //$bar["messages"]->addMessage($id);
 $anuncio = getfun("getAnuncio(\"id\":$id)")["data"]; 
?>


<!DOCTYPE html>
<html lang="es">
<!-- head_anuncio.php -->

<head> 

     
 <!-- Google Tag Manager or NO ROBOTS--> 
 <?php if( Flex()->dev ): ?>
  <meta name="robots" content="noindex">
<?php else: ?>
  <script>
    (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
      new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
      j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
      'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
      })(window,document,'script','dataLayer','GTM-KQTCZDJ');
  </script> 
  
<?php endif; ?>

<!-- Datos estructurados  -->
   
<script type="application/json">
{
  "@context": "https://schema.org",
  "@type": "Corporation",
  "name": "Citas 10",
  "alternateName": "Navarrasex",
  "url": "https://citas10.net/",
  "contactPoint": {
    "@type": "ContactPoint",
    "telephone": "+34635929009",
    "areaServed": "ES",
    "availableLanguage": "es"
  }
} 
</script>

  <meta name='keywords' content='Citas en Pamplona, modelos en Pamplona, Masajistas en Pamplona, citas10 navarrasex, no Putas'/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
   
    <link href="https://fonts.googleapis.com/css?family=Black+Ops+One|Merienda|Quantico" rel="stylesheet">
    <!-- font-family: 'Merienda', cursive;
font-family: 'Quantico', sans-serif;
font-family: 'Black Ops One', cursive; -->

   <!-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/normalize/8.0.0/normalize.min.css" /> -->
   <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.4.2/css/all.css">

 
    <link rel="stylesheet" href="/css/anuncio.css?rel=<?=time()?>">

    <!-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/flickity/2.1.2/flickity.min.css" /> --> 
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/flickity/2.2.0/flickity.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
      <!-- SCRIPT DETECCION DE ORDENADOR PC -->
<script src="/js/detector.js"></script>

    <!-- <script src="//citas10.com/anuncio/funciones.js"></script> -->
    <script src="/js/anuncio.js"></script>
  <!--  <script src="/js/mantenimiento.js"></script> -->
    <title><?="$anuncio->nombre en citas10 antes Navarrasex"?></title>

    <!-- Etiqueta canonical -->
    <link rel="canonical" href="https://citas10.net/">

 <meta name="description" content="<?=" Citas10.com【 ANTES ⭐NAVARRASEX OFICIAL⭐ 】🥂Citas en pamplona🥂 anuncio de $anuncio->nombre en pamlona"?>">
  <link rel="stylesheet" href="/css/anuncio-galeria.css?rel=<?=time()?>">



</head>
<body>
 <!-- Google Tag Manager (noscript) -->
 <?php if( Flex()->dev ): ?>
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-KQTCZDJ"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<?php endif ?>
<!-- End Google Tag Manager (noscript) -->
	
<nav>
    <div class="contenido">
      <!-- <span class="nav-nombre"<?php if( strlen( $anuncio->nombre)>6) echo "style='font-size: 1rem'"?>><?=$anuncio->nombre?></span> -->
 
        <a href="/"> <h4 class="logo">Citas10</h4></a>
 
    <!-- <div class="boton"> 
        <a href="#" onclick="window.close()" style="color: white"><i class="fas fa-times-circle fa-3x"> </i></a> 
    </div> -->

</nav>