# Depuración y Desarrollo en FlexHTTP

FlexHTTP incluye herramientas de depuración y desarrollo que facilitan la identificación y solución de problemas durante el desarrollo de aplicaciones.

## Modo Desarrollo

FlexHTTP detecta automáticamente si está en entorno de desarrollo o producción:

```php
public function dev() : bool{
    if(PHP_OS == 'WINNT') {
        return true;
    }
    $dominio = $_SERVER['SERVER_NAME'];
    if (preg_match("/^dev./", $dominio) || preg_match("/.test$/", $dominio) || preg_match("/.dev$/", $dominio)) {
        return true;
    } else{
        return false;
    }
}
```

También se puede configurar manualmente al inicializar el framework:

```php
$opciones = [ "dev" => true ];  // Forzar modo desarrollo
app\init($opciones);
```

## Integración con PHP DebugBar

FlexHTTP integra [PHP DebugBar](http://phpdebugbar.com/) para monitoreo en tiempo real:

```php
// En index.php
$debugbar = new DebugBar\StandardDebugBar();
$debugbarRenderer = $debugbar->getJavascriptRenderer();

// Añadir colector de configuración
$debugbar->addCollector(new DebugBar\DataCollector\ConfigCollector(app\debug_config(), "Config"));

// Añadir información de desarrollo
$dev = [
    "Route" => "uri:".$flex->uri().", view: ". $flex->view.", zone:$flex->zone",
    "view_path & zones" => $flex->view_path. ", zones: ". json_encode($flex->zones),
    // ...más información...
];
$debugbar->addCollector(new DebugBar\DataCollector\ConfigCollector($dev, "Dev"));

// Renderizar en la página
if ($flex->is_page) {
    echo $debugbarRenderer->renderHead();
    // ...contenido de la página...
    echo $debugbarRenderer->render();
}
```

### Añadir Mensajes a DebugBar

```php
// En cualquier parte del código
$debugbar["messages"]->addMessage("Este es un mensaje de depuración");
$debugbar["messages"]->addMessage(["usuario" => $usuario, "acción" => "login"]);

// Usando el método de la clase Flex
flex()->debugbarInfo("Cargando vista: " . $flex->view_path);
```

## Función `logbar()`

FlexHTTP incluye una función `logbar()` para registrar mensajes en la barra de depuración:

```php
function logbar($message, $label = null) {
    global $debugbar;
    
    if (!flex()->dev) return;  // Solo en modo desarrollo
    
    if ($label) {
        $debugbar["messages"]->addMessage([$label => $message]);
    } else {
        $debugbar["messages"]->addMessage($message);
    }
}
```

Ejemplo de uso:

```php
// Registrar mensaje simple
logbar("Procesando formulario");

// Registrar mensaje con etiqueta
logbar($usuario, "Usuario actual");

// Registrar variables
logbar($_POST, "Datos POST");
```

## Visualización de Errores

En modo desarrollo, FlexHTTP muestra errores detallados:

```php
// En framework.php
function set_dev($opt){ 
    // ...
    
    if (Config::$dev) {
        // Mostrar todos los errores en desarrollo
        ini_set('display_errors', 1);
        ini_set('display_startup_errors', 1);
        error_reporting(E_ALL);
    } else {
        // Ocultar errores en producción
        ini_set('display_errors', 0);
        ini_set('display_startup_errors', 0);
        error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED & ~E_STRICT);
    }
}
```

## Funciones de Depuración

FlexHTTP proporciona funciones para facilitar la depuración:

```php
// Imprimir variable formateada
function dump($var) {
    echo '<pre>';
    var_dump($var);
    echo '</pre>';
}

// Imprimir variable y detener ejecución
function dd($var) {
    dump($var);
    die();
}

// Obtener información de configuración para depuración
function debug_config() {
    $ReflectionClass = new \ReflectionClass('App\Config');
    $props = $ReflectionClass->getStaticProperties();
    
    $retorno = [
        "Config" => $props 
    ];   
    return $retorno; 
}

// Obtener variables globales
function globals() {
    $globales = [];
    foreach ($GLOBALS as $key => $value) {
        if (
            $key == 'dsn' ||   $key == 'pdo' || $key == 'pdo_options' || 'faker'
        ) {
            continue;
        }
        $globales[$key] = $value;
    } 
    return $globales;
}
```

## Registro de Ejecución de Funciones

FlexHTTP registra todas las ejecuciones de funciones dinámicas:

```php
function logFuncion($nombre, $params, $resultado, $origen) {
    global $debugbar;
    
    $log = [
        'funcion' => $nombre,
        'parametros' => $params,
        'resultado' => $resultado,
        'origen' => $origen,
        'tiempo' => date('Y-m-d H:i:s')
    ];
    
    $debugbar["messages"]->addMessage($log);
}
```

## Depuración de Rutas

FlexHTTP proporciona información detallada sobre las rutas:

```php
$routeInfo = [
    "URI" => flex()->uri(),
    "View" => flex()->view,
    "View Path" => flex()->view_path,
    "Zone" => flex()->zone,
    "Method" => flex()->method,
    "Args" => flex()->args,
    "Is Page" => flex()->is_page ? "true" : "false"
];

$debugbar->addCollector(new DebugBar\DataCollector\ConfigCollector($routeInfo, "Route"));
```

## Depuración de Base de Datos

FlexHTTP puede registrar y mostrar consultas SQL:

```php
// En pdo.php
$pdo = new PDO($dsn, $username, $password, $options);

// Añadir colector de PDO a DebugBar
$pdoCollector = new DebugBar\DataCollector\PDO\PDOCollector($pdo);
$debugbar->addCollector($pdoCollector);
```

## Depuración de Tiempo de Ejecución

FlexHTTP puede medir el tiempo de ejecución de operaciones:

```php
// Iniciar temporizador
$debugbar["time"]->startMeasure('render', 'Tiempo de renderizado');

// Código a medir
// ...

// Detener temporizador
$debugbar["time"]->stopMeasure('render');
```

## Depuración de Memoria

FlexHTTP puede monitorear el uso de memoria:

```php
// Registrar uso de memoria
$debugbar["memory"]->addMessage(memory_get_usage(true));
```

## Herramientas de Desarrollo

### Browser-Sync

FlexHTTP incluye configuración para [Browser-Sync](https://browsersync.io/), que permite recargar automáticamente el navegador al realizar cambios:

```json
// En package.json
{
  "name": "citas10-2022",
  "version": "1.0.0",
  "description": "",
  "main": "index.js",
  "scripts": {
    "start": "browser-sync start --proxy citas10.test --files '**/*' ",
    "tunnel": "lt --port 80 --local-host 'citas10.test'"
  },
  "keywords": [],
  "author": "",
  "license": "ISC",
  "devDependencies": {
    "browser-sync": "^3*"
  }
}
```

Para iniciar Browser-Sync:

```bash
npm start
```

### Túnel Local

FlexHTTP incluye configuración para [localtunnel](https://github.com/localtunnel/localtunnel), que permite exponer un servidor local a Internet:

```bash
npm run tunnel
```

## Ejemplos Prácticos

### Depurar una Función

```php
function procesarPago($monto, $metodo) {
    logbar("Iniciando procesamiento de pago", "Pago");
    logbar(["monto" => $monto, "metodo" => $metodo], "Parámetros");
    
    $debugbar["time"]->startMeasure('procesar_pago', 'Tiempo de procesamiento de pago');
    
    try {
        // Lógica de procesamiento de pago
        $resultado = realizarPago($monto, $metodo);
        
        logbar($resultado, "Resultado del pago");
        
        $debugbar["time"]->stopMeasure('procesar_pago');
        
        return $resultado;
    } catch (Exception $e) {
        logbar($e->getMessage(), "Error de pago");
        
        $debugbar["time"]->stopMeasure('procesar_pago');
        
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}
```

### Depurar Consultas SQL

```php
function obtenerUsuarios($filtro = null) {
    global $pdo, $debugbar;
    
    $sql = "SELECT * FROM usuarios";
    $params = [];
    
    if ($filtro) {
        $sql .= " WHERE nombre LIKE :filtro OR email LIKE :filtro";
        $params['filtro'] = "%$filtro%";
    }
    
    logbar($sql, "SQL");
    logbar($params, "Parámetros SQL");
    
    $debugbar["time"]->startMeasure('query_usuarios', 'Consulta de usuarios');
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $usuarios = $stmt->fetchAll(PDO::FETCH_OBJ);
    
    $debugbar["time"]->stopMeasure('query_usuarios');
    
    logbar(count($usuarios), "Número de usuarios encontrados");
    
    return $usuarios;
}
```

### Panel de Depuración Personalizado

```php
// En una página de administración
<div class="card">
    <div class="card-header">
        <h3>Información de Depuración</h3>
    </div>
    <div class="card-body">
        <h4>Configuración</h4>
        <pre><?php print_r(app\debug_config()); ?></pre>
        
        <h4>Rutas</h4>
        <ul>
            <li><strong>URI:</strong> <?= flex()->uri() ?></li>
            <li><strong>View:</strong> <?= flex()->view ?></li>
            <li><strong>View Path:</strong> <?= flex()->view_path ?></li>
            <li><strong>Zone:</strong> <?= flex()->zone ?></li>
            <li><strong>Method:</strong> <?= flex()->method ?></li>
            <li><strong>Is Page:</strong> <?= flex()->is_page ? 'true' : 'false' ?></li>
        </ul>
        
        <h4>Entorno</h4>
        <ul>
            <li><strong>Modo Desarrollo:</strong> <?= flex()->dev ? 'Activado' : 'Desactivado' ?></li>
            <li><strong>PHP Version:</strong> <?= phpversion() ?></li>
            <li><strong>Server:</strong> <?= $_SERVER['SERVER_SOFTWARE'] ?></li>
            <li><strong>Document Root:</strong> <?= $_SERVER['DOCUMENT_ROOT'] ?></li>
        </ul>
        
        <h4>Memoria</h4>
        <ul>
            <li><strong>Uso Actual:</strong> <?= formatBytes(memory_get_usage()) ?></li>
            <li><strong>Uso Máximo:</strong> <?= formatBytes(memory_get_peak_usage()) ?></li>
        </ul>
    </div>
</div>

<?php
function formatBytes($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= (1 << (10 * $pow));
    return round($bytes, $precision) . ' ' . $units[$pow];
}
?>
```

## Siguientes Pasos

- Aprende sobre la [Integración con Frontend](10_frontend.md) para crear interfaces dinámicas.
- Explora el [Índice](00_indice.md) para encontrar más información sobre el framework.
- Revisa la [Introducción](01_introduccion.md) para entender los conceptos básicos del framework.
