# Controladores y Ejecución Dinámica en FlexHTTP

Los controladores en FlexHTTP gestionan la lógica de negocio de la aplicación y proporcionan un sistema único de ejecución dinámica de funciones directamente desde la URL.

## Estructura de Controladores

Los controladores se organizan en archivos PHP dentro del directorio `controllers/`, siguiendo la convención de nomenclatura `nombreController.php`:

```
/controllers
├── inicioController.php
├── usuariosController.php
├── productosController.php
└── ...
```

### Carga de Controladores

La función `controller()` determina qué controlador debe cargarse según la URL actual:

```php
function controller()
{
    $name = uri(0);
    Config::$controller = $name; 
    $fullpath = Config::$controllers . "/$name". "Controller.php";
   
    if (file_exists($fullpath)) {
        return $fullpath;
    } 
}
```

El controlador se carga automáticamente en `index.php`:

```php
if ($controllerPath = app\controller()) {
    include $controllerPath;
}
```

## Estructura de un Controlador

Un controlador típico contiene funciones que implementan la lógica de negocio:

```php
<?php
// usuariosController.php

// Obtener un usuario por ID
function getUsuario($id) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = :id");
    $stmt->execute(['id' => $id]);
    return $stmt->fetch(PDO::FETCH_OBJ);
}

// Crear un nuevo usuario
function crearUsuario($nombre, $email, $password) {
    global $pdo;
    $stmt = $pdo->prepare("INSERT INTO usuarios (nombre, email, password) VALUES (:nombre, :email, :password)");
    $result = $stmt->execute([
        'nombre' => $nombre,
        'email' => $email,
        'password' => password_hash($password, PASSWORD_DEFAULT)
    ]);
    
    if ($result) {
        return ['success' => true, 'id' => $pdo->lastInsertId()];
    } else {
        return ['success' => false, 'error' => 'Error al crear usuario'];
    }
}

// Actualizar un usuario
function actualizarUsuario($id, $datos) {
    // Implementación
}

// Eliminar un usuario
function eliminarUsuario($id) {
    // Implementación
}
```

## Sistema de Ejecución Dinámica

Una característica única de FlexHTTP es la capacidad de ejecutar funciones directamente desde la URL mediante los parámetros `fun[]` y `getfun[]`.

### Función `fun()`

La función `fun()` ejecuta una función y muestra su resultado, realizando una redirección después:

```php
function fun($funcion, $params = [])
{
    // Ejecuta la función especificada con los parámetros dados
    // Registra la ejecución en el log
    // Realiza redirección
}
```

### Función `getfun()`

La función `getfun()` ejecuta una función y devuelve su resultado sin realizar redirección:

```php
function getfun($funcion, $params = [])
{
    // Ejecuta la función especificada con los parámetros dados
    // Registra la ejecución en el log
    // Devuelve el resultado
}
```

### Diferencias entre `fun()` y `getfun()`

- **fun()**: Se usa para acciones que modifican datos y requieren redirección (enlaces, formularios).
- **getfun()**: Se usa para recuperar datos que se mostrarán en la página (consultas, listados).

### Llamada desde URL

Se pueden ejecutar funciones directamente desde la URL utilizando el parámetro `fun[]`:

```
http://sitio.com/pagina?fun[]=crearUsuario(nombre:Juan,email:<EMAIL>,password:123456)
```

O para obtener datos sin redirección:

```
http://sitio.com/pagina?getfun[]=getUsuario(id:123)
```

### Sintaxis de Parámetros

Los parámetros se pasan en formato `nombre:valor` separados por comas:

```
funcion(param1:valor1,param2:valor2,param3:valor3)
```

Para valores más complejos, se pueden usar punto y coma para separar subparámetros:

```
funcion(opciones:color:rojo;tamaño:grande;precio:100)
```

### Ejecución desde PHP

También se pueden ejecutar funciones dinámicamente desde el código PHP:

```php
// Ejecutar una función y mostrar su resultado
fun("crearUsuario", ["nombre" => "Juan", "email" => "<EMAIL>", "password" => "123456"]);

// Ejecutar una función y obtener su resultado
$usuario = getfun("getUsuario", ["id" => 123]);
echo $usuario["data"]->nombre;
```

## Rastreo de Origen de Llamadas

FlexHTTP puede detectar el origen de una llamada a función:

```php
function detectarOrigen() {
    if (isset($_GET['fun']) || isset($_GET['getfun'])) {
        return 'URL';
    } elseif (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
        return 'AJAX';
    } elseif (isset($_POST['_method'])) {
        return 'FORM';
    } else {
        return 'CODE';
    }
}
```

Esto permite comportamientos diferentes según cómo se llamó a la función:

```php
function guardarDatos($datos) {
    // Guardar datos en la base de datos
    
    $origen = detectarOrigen();
    
    if ($origen === 'URL' || $origen === 'FORM') {
        // Redireccionar a página de éxito
        redirect('/exito');
    } elseif ($origen === 'AJAX') {
        // Devolver respuesta JSON
        header('Content-Type: application/json');
        echo json_encode(['success' => true]);
        exit;
    } else {
        // Devolver resultado para uso en código
        return ['success' => true];
    }
}
```

## Logging de Ejecuciones

FlexHTTP registra todas las ejecuciones de funciones para facilitar la depuración:

```php
function logFuncion($nombre, $params, $resultado, $origen) {
    global $debugbar;
    
    $log = [
        'funcion' => $nombre,
        'parametros' => $params,
        'resultado' => $resultado,
        'origen' => $origen,
        'tiempo' => date('Y-m-d H:i:s')
    ];
    
    $debugbar["messages"]->addMessage($log);
    
    // También se puede guardar en archivo o base de datos
}
```

## Integración con Modelos

Las funciones dinámicas se integran perfectamente con los modelos generados automáticamente:

```php
// Obtener un anuncio y convertirlo en objeto tipado
/** @var \Anuncio $anuncio */
$anuncio = getfun("getAnuncio(id:2)")["data"];

// El IDE ahora sugerirá propiedades como $anuncio->titulo
echo $anuncio->titulo;
```

## Ejemplos Prácticos

### Crear un CRUD Básico

```php
// En usuariosController.php

// Listar usuarios
function listarUsuarios() {
    global $pdo;
    $stmt = $pdo->query("SELECT * FROM usuarios");
    return $stmt->fetchAll(PDO::FETCH_OBJ);
}

// Obtener un usuario
function getUsuario($id) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = :id");
    $stmt->execute(['id' => $id]);
    return $stmt->fetch(PDO::FETCH_OBJ);
}

// Crear usuario
function crearUsuario($nombre, $email, $password) {
    global $pdo;
    $stmt = $pdo->prepare("INSERT INTO usuarios (nombre, email, password) VALUES (:nombre, :email, :password)");
    $result = $stmt->execute([
        'nombre' => $nombre,
        'email' => $email,
        'password' => password_hash($password, PASSWORD_DEFAULT)
    ]);
    
    return ['success' => $result, 'id' => $pdo->lastInsertId()];
}

// Actualizar usuario
function actualizarUsuario($id, $nombre, $email) {
    global $pdo;
    $stmt = $pdo->prepare("UPDATE usuarios SET nombre = :nombre, email = :email WHERE id = :id");
    $result = $stmt->execute([
        'id' => $id,
        'nombre' => $nombre,
        'email' => $email
    ]);
    
    return ['success' => $result];
}

// Eliminar usuario
function eliminarUsuario($id) {
    global $pdo;
    $stmt = $pdo->prepare("DELETE FROM usuarios WHERE id = :id");
    $result = $stmt->execute(['id' => $id]);
    
    return ['success' => $result];
}
```

### Uso en Vistas

```php
<!-- En paginas/usuarios/lista.php -->
<h1>Listado de Usuarios</h1>

<table>
    <tr>
        <th>ID</th>
        <th>Nombre</th>
        <th>Email</th>
        <th>Acciones</th>
    </tr>
    
    <?php $usuarios = getfun("listarUsuarios")["data"]; ?>
    
    <?php foreach ($usuarios as $usuario): ?>
    <tr>
        <td><?= $usuario->id ?></td>
        <td><?= $usuario->nombre ?></td>
        <td><?= $usuario->email ?></td>
        <td>
            <a href="/usuarios/editar/<?= $usuario->id ?>">Editar</a>
            <a href="?fun[]=eliminarUsuario(id:<?= $usuario->id ?>)">Eliminar</a>
        </td>
    </tr>
    <?php endforeach; ?>
</table>

<a href="/usuarios/crear">Crear Nuevo Usuario</a>
```

```php
<!-- En paginas/usuarios/crear.php -->
<h1>Crear Usuario</h1>

<form action="?fun[]=crearUsuario" method="post">
    <div>
        <label for="nombre">Nombre:</label>
        <input type="text" id="nombre" name="nombre" required>
    </div>
    <div>
        <label for="email">Email:</label>
        <input type="email" id="email" name="email" required>
    </div>
    <div>
        <label for="password">Contraseña:</label>
        <input type="password" id="password" name="password" required>
    </div>
    <button type="submit">Guardar</button>
</form>
```

## Siguientes Pasos

- Explora el [Sistema de Vistas](05_vistas.md) para crear interfaces de usuario.
- Aprende sobre la [Generación de Modelos](06_modelos.md) para trabajar con datos estructurados.
- Descubre el [Sistema de Autenticación](07_autenticacion.md) para proteger tus rutas y funciones.
