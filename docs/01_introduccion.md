# Introducción a FlexHTTP

FlexHTTP es un framework PHP ligero y flexible diseñado para el desarrollo rápido de aplicaciones web. Su enfoque minimalista y su arquitectura intuitiva lo hacen ideal para proyectos de pequeño a mediano tamaño donde la velocidad de desarrollo y el rendimiento son prioritarios.

## Características Principales

- **Arquitectura MVC Ligera**: Implementa el patrón Modelo-Vista-Controlador de forma simplificada y eficiente.
- **Ejecución Dinámica de Funciones**: Permite llamar a funciones PHP directamente desde la URL.
- **Generación Automática de Modelos**: Crea clases PHP a partir de tablas de la base de datos.
- **Sistema de Plantillas Nativo**: Utiliza PHP puro para las vistas, sin necesidad de aprender nuevas sintaxis.
- **Depuración Integrada**: Incluye herramientas de desarrollo y monitoreo en tiempo real.
- **Ligereza**: Todo el núcleo del framework ocupa menos de 1000 líneas de código.
- **Sin Dependencias Complejas**: Mínimas dependencias externas para mayor control y rendimiento.

## Requisitos del Sistema

- **PHP**: Versión 8.3 o superior
- **Servidor Web**: Apache 2.4.62-24 o superior con mod_rewrite habilitado
- **Base de Datos**: MariaDB 10.x o MySQL 5.7+
- **Composer**: Para gestión de dependencias

## Instalación

### Método 1: Instalación Manual

1. Descarga el código fuente desde el repositorio.
2. Coloca los archivos en tu directorio web.
3. Ejecuta `composer install` para instalar las dependencias.
4. Configura tu servidor web para que apunte al directorio público.
5. Copia el archivo `.env.example` a `.env` y configura tus variables de entorno.

### Método 2: Usando Composer

```bash
composer create-project flexhttp/framework mi-proyecto
cd mi-proyecto
composer install
```

## Configuración Básica

La configuración principal se realiza a través de la clase `Config` en `includes/framework.php`:

```php
class Config
{
    public static $css = 'css';
    public static $files = "files";
    public static $uploads = "uploads";
    public static $home = 'inicio';
    public static $js = "js";
    public static $libs = 'libs';
    public static $page404 = '404.php';
    public static $pages = 'paginas';
    public static $controllers = 'controllers';
    public static $partials = 'partials';
    // ...
}
```

Para inicializar el framework, se utiliza la función `init()` en el archivo principal:

```php
include_once "includes/framework.php";
$opciones = [ "dev" => "auto" ];
app\init($opciones);
```

## Estructura de Directorios

```
/
├── controllers/       # Controladores de la aplicación
├── css/              # Archivos CSS
├── docs/             # Documentación
├── includes/         # Núcleo del framework
│   ├── Flex.php      # Clase principal del framework
│   ├── framework.php # Funciones y configuración base
│   └── ...
├── js/               # Archivos JavaScript
├── models/           # Modelos de datos
├── paginas/          # Vistas/páginas de la aplicación
├── partials/         # Componentes reutilizables
├── vendor/           # Dependencias (gestionadas por Composer)
├── .htaccess         # Configuración de Apache
├── composer.json     # Configuración de Composer
└── index.php         # Punto de entrada de la aplicación
```

## Primeros Pasos

### Crear una Página Simple

1. Crea un archivo en la carpeta `paginas`, por ejemplo `paginas/hola.php`:

```php
<h1>¡Hola Mundo!</h1>
<p>Esta es mi primera página con FlexHTTP.</p>
```

2. Accede a la página a través de la URL: `http://tu-sitio.com/hola`

### Crear una Función Dinámica

Para crear una función que pueda ser llamada desde la URL:

```php
// En un archivo de controlador
function saludar($nombre = "Mundo") {
    echo "¡Hola, $nombre!";
}
```

Ahora puedes llamar a esta función desde la URL:
`http://tu-sitio.com/pagina?fun[]=saludar(nombre:Juan)`

## Filosofía de Diseño

FlexHTTP se basa en los siguientes principios:

1. **Simplicidad**: Código claro y directo, fácil de entender y mantener.
2. **Flexibilidad**: Adaptable a diferentes tipos de proyectos y estilos de desarrollo.
3. **Rendimiento**: Optimizado para velocidad y eficiencia.
4. **Pragmatismo**: Enfocado en resolver problemas reales de forma práctica.
5. **Convención sobre configuración**: Estructura predecible con mínima configuración.

## Siguientes Pasos

- Explora la [Arquitectura](02_arquitectura.md) del framework para entender su estructura interna.
- Aprende sobre el [Sistema de Enrutamiento](03_routing.md) para gestionar las URLs de tu aplicación.
- Descubre cómo funcionan los [Controladores](04_controladores.md) y la ejecución dinámica de funciones.
