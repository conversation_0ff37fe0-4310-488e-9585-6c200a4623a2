# Sistema de Vistas en FlexHTTP

El sistema de vistas de FlexHTTP utiliza PHP nativo para crear interfaces de usuario, complementado con un sistema de componentes reutilizables (partials) y layouts.

## Estructura de Vistas

Las vistas se organizan en archivos PHP dentro del directorio `paginas/`, siguiendo la estructura de URL:

```
/paginas
├── inicio.php           # Accesible como /inicio
├── contacto.php         # Accesible como /contacto
├── admin/               # Sección "admin"
│   ├── dashboard.php    # Accesible como /admin/dashboard
│   └── usuarios.php     # Accesible como /admin/usuarios
└── blog/                # Sección "blog"
    ├── articulos.php    # Accesible como /blog/articulos
    └── categorias.php   # Accesible como /blog/categorias
```

## Carga de Vistas

La función `page()` determina qué archivo de vista debe cargarse según la URL actual:

```php
function page()
{
    // Admin zone
    if (strlen(uri(1)) > 0 && uri(0) != 'anuncio'){
        $section = uri(0);
        $page = uri(1);
        $path = "/$section/$page";
    } else {
        $section = "";
        $page = uri(0);
        $path = "/$page";
    } 

    $fullpath = Config::$pages.$path.".php";   
 
    if (file_exists($fullpath)) {
        return $fullpath;
    } else {
        http_response_code(404);
        echo "<script>console.log('No se encontra la página $fullpath')</script>"; 
        header("Location: /inicio/");
    }
}
```

La vista se carga automáticamente en `index.php`:

```php
if (file_exists($flex->view_path) && !isset($_GET['action'])) {
    include $flex->view_path;
} else {
    $flex->debugbarInfo("No se encuentra".$flex->view_path);
}
```

## Sistema de Partials

Los partials son componentes reutilizables que se pueden incluir en múltiples vistas:

```
/partials
├── head.php             # Cabecera HTML común
├── head_admin.php       # Cabecera específica para admin
├── nav.php              # Navegación principal
├── nav_admin.php        # Navegación de administración
├── footer.php           # Pie de página común
└── componentes/         # Componentes específicos
    ├── card.php         # Componente de tarjeta
    ├── alert.php        # Componente de alerta
    └── pagination.php   # Componente de paginación
```

### Función `partial()`

La función `partial()` permite incluir componentes reutilizables:

```php
function partial($name)
{
    $fullpath = Config::$partials . "/$name.php";
    if (file_exists($fullpath)) {
        return $fullpath;
    } else {
        throw new \Exception("El archivo parcial no existe en: $fullpath");
    }
}
```

Ejemplo de uso:

```php
// Incluir un componente
include partial('componentes/card');

// Pasar variables al componente
$titulo = "Mi Tarjeta";
$contenido = "Contenido de la tarjeta";
include partial('componentes/card');
```

### Funciones `head()` y `nav()`

Las funciones `head()` y `nav()` permiten incluir cabeceras y navegación específicas por sección:

```php
function head($name = false){
    if ($name) $name = "_$name";
    $fullpath = Config::$partials . "/head$name.php";
    if (file_exists($fullpath)) {
        include $fullpath;
    }  
}

function nav($name = false){
    if ($name) $name = "_$name";
    $fullpath = Config::$partials . "/nav$name.php";
    if (file_exists($fullpath)) {
        include $fullpath;
    } else {
        return false;
    }
}
```

Estas funciones se utilizan automáticamente en `index.php`:

```php
if ($flex->is_page) {
    if (file_exists("partials/head_$flex->zone.php")) include "partials/head_$flex->zone.php";
    if (file_exists("partials/nav_$flex->zone.php")) include "partials/nav_$flex->zone.php";
    echo $debugbarRenderer->renderHead();
}
```

## Función `part()`

La función `part()` permite incluir subcomponentes específicos de una página:

```php
function part($part){
    $fullpath = Config::$pages . "/". uri(0) . "/$part.php";
    if (file_exists($fullpath)) {
        return $fullpath;
    } else {
        throw new \Exception("El archivo part no existe en: $fullpath");
    }
}
```

Ejemplo de uso:

```php
// En paginas/usuarios.php
<div class="container">
    <h1>Gestión de Usuarios</h1>
    
    <?php if (uri(2) == 'crear'): ?>
        <?php include part('crear'); ?>
    <?php elseif (uri(2) == 'editar'): ?>
        <?php include part('editar'); ?>
    <?php else: ?>
        <?php include part('listar'); ?>
    <?php endif; ?>
</div>
```

Con archivos como:
- `paginas/usuarios/crear.php`
- `paginas/usuarios/editar.php`
- `paginas/usuarios/listar.php`

## Layouts

FlexHTTP utiliza un sistema de layouts basado en la inclusión de cabeceras y pies de página:

```php
// En index.php
if ($flex->is_page) {
    if (file_exists("partials/head_$flex->zone.php")) include "partials/head_$flex->zone.php";
    if (file_exists("partials/nav_$flex->zone.php")) include "partials/nav_$flex->zone.php";
    echo $debugbarRenderer->renderHead();
}

// Carga de la vista
if (file_exists($flex->view_path) && !isset($_GET['action'])) {
    include $flex->view_path;
} else {
    $flex->debugbarInfo("No se encuentra".$flex->view_path);
}

// Pie de página
if ($flex->is_page) {
    if (file_exists("partials/footer_$flex->zone.php")) include "partials/footer_$flex->zone.php";
    echo $debugbarRenderer->render();
}
```

## Integración con Datos

Las vistas pueden acceder directamente a los datos obtenidos mediante `getfun()`:

```php
<!-- En paginas/usuarios/detalle.php -->
<?php
$id = flex()->uri(3);
$usuario = getfun("getUsuario(id:$id)")["data"];
?>

<div class="container">
    <h1>Detalle del Usuario</h1>
    
    <div class="card">
        <div class="card-header">
            <h2><?= $usuario->nombre ?></h2>
        </div>
        <div class="card-body">
            <p><strong>Email:</strong> <?= $usuario->email ?></p>
            <p><strong>Fecha de registro:</strong> <?= date('d/m/Y', strtotime($usuario->fecha_registro)) ?></p>
        </div>
        <div class="card-footer">
            <a href="/usuarios" class="btn btn-secondary">Volver</a>
            <a href="/usuarios/editar/<?= $usuario->id ?>" class="btn btn-primary">Editar</a>
            <a href="?fun[]=eliminarUsuario(id:<?= $usuario->id ?>)" class="btn btn-danger" onclick="return confirm('¿Estás seguro?')">Eliminar</a>
        </div>
    </div>
</div>
```

## Conversión Array-Objeto

FlexHTTP proporciona una función para convertir arrays en objetos navegables, lo que facilita el trabajo con datos en las vistas:

```php
public function arrayToObject($array)
{
    if (is_array($array)) {
        return (object) array_map([$this, 'arrayToObject'], $array);
    }
    return $array;
}
```

Ejemplo de uso:

```php
$datos = [
    'usuario' => [
        'nombre' => 'Juan',
        'email' => '<EMAIL>',
        'direccion' => [
            'calle' => 'Calle Principal',
            'ciudad' => 'Madrid'
        ]
    ]
];

$objeto = flex()->arrayToObject($datos);

// Ahora se puede acceder como:
echo $objeto->usuario->nombre;  // Juan
echo $objeto->usuario->direccion->ciudad;  // Madrid
```

## Sistema de Pasos

FlexHTTP incluye un sistema para crear formularios por pasos:

```html
<div class="bolas">
    <div data="1" class="pasobtn <?=flex()->view == 'detalle' ? 'superado' : '' ?>">
        <div class="bola step1">1</div>
        <div class="boton2">
            <i class="fa-solid fa-address-card fa-xl"></i>
            <span>Detalle</span>
        </div>
    </div>
    <div data="2" class="pasobtn <?=flex()->view == 'fotos' ? 'superado' : '' ?>">
        <div class="bola step2">2</div>
        <div class="boton2">
            <i class="fa-solid fa-images fa-xl"></i>
            <span>Fotos</span>
        </div>
    </div>
    <!-- Más pasos... -->
</div>
```

## Mensajes al Usuario

FlexHTTP proporciona un sistema para mostrar mensajes y alertas al usuario:

```php
function mostrarMensaje($tipo, $mensaje) {
    $_SESSION['mensaje'] = [
        'tipo' => $tipo,  // 'success', 'error', 'warning', 'info'
        'texto' => $mensaje
    ];
}
```

En la vista:

```php
<?php if (isset($_SESSION['mensaje'])): ?>
    <div class="alert alert-<?= $_SESSION['mensaje']['tipo'] ?>">
        <?= $_SESSION['mensaje']['texto'] ?>
    </div>
    <?php unset($_SESSION['mensaje']); ?>
<?php endif; ?>
```

## Ejemplos Prácticos

### Crear una Página con Layout

```php
<!-- En partials/head.php -->
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mi Aplicación</title>
    <link rel="stylesheet" href="/css/styles.css">
</head>
<body>

<!-- En partials/nav.php -->
<nav class="navbar">
    <div class="container">
        <a href="/" class="navbar-brand">Mi Aplicación</a>
        <ul class="navbar-nav">
            <li class="nav-item"><a href="/inicio" class="nav-link">Inicio</a></li>
            <li class="nav-item"><a href="/productos" class="nav-link">Productos</a></li>
            <li class="nav-item"><a href="/contacto" class="nav-link">Contacto</a></li>
        </ul>
    </div>
</nav>

<!-- En paginas/productos.php -->
<div class="container">
    <h1>Nuestros Productos</h1>
    
    <div class="row">
        <?php $productos = getfun("listarProductos")["data"]; ?>
        
        <?php foreach ($productos as $producto): ?>
            <div class="col-md-4">
                <?php
                $datos = [
                    'titulo' => $producto->nombre,
                    'contenido' => $producto->descripcion,
                    'imagen' => $producto->imagen,
                    'enlace' => "/productos/detalle/{$producto->id}"
                ];
                include partial('componentes/card');
                ?>
            </div>
        <?php endforeach; ?>
    </div>
</div>

<!-- En partials/footer.php -->
    <footer class="footer">
        <div class="container">
            <p>&copy; <?= date('Y') ?> Mi Aplicación. Todos los derechos reservados.</p>
        </div>
    </footer>
    <script src="/js/scripts.js"></script>
</body>
</html>
```

### Crear un Componente Reutilizable

```php
<!-- En partials/componentes/card.php -->
<div class="card">
    <?php if (isset($imagen) && $imagen): ?>
        <img src="<?= $imagen ?>" class="card-img-top" alt="<?= $titulo ?>">
    <?php endif; ?>
    
    <div class="card-body">
        <h5 class="card-title"><?= $titulo ?></h5>
        <p class="card-text"><?= $contenido ?></p>
        
        <?php if (isset($enlace) && $enlace): ?>
            <a href="<?= $enlace ?>" class="btn btn-primary">Ver más</a>
        <?php endif; ?>
    </div>
</div>
```

## Siguientes Pasos

- Aprende sobre la [Generación de Modelos](06_modelos.md) para trabajar con datos estructurados.
- Descubre el [Sistema de Autenticación](07_autenticacion.md) para proteger tus rutas y vistas.
- Explora las [Utilidades](08_utilidades.md) para facilitar el desarrollo.
