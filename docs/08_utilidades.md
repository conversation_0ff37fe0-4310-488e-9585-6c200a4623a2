# Helpers y Utilidades en FlexHTTP

FlexHTTP incluye un conjunto de funciones auxiliares (helpers) y utilidades que facilitan tareas comunes en el desarrollo de aplicaciones web.

## Funciones de Redirección

FlexHTTP proporciona funciones para realizar redirecciones:

```php
// En Flex.php
public function redirect($dest = false){  
    $url = $dest ? $dest : flex()->path;
    echo "<script>window.location.href='$url'</script>"; 
}

// En framework.php
function redirect($dest = false){
    global $uri;
    $dest = $dest ? $dest : $uri;
    echo "<script>window.location.href='$dest'</script>"; 
}
```

Ejemplo de uso:

```php
// Redireccionar a la página de inicio
redirect('/inicio');

// Redireccionar a la página anterior
redirect();

// Redireccionar con parámetros
redirect('/usuarios?mensaje=guardado');
```

## Gestión de Sesiones

Funciones para manejar datos de sesión:

```php
// Establecer un valor en la sesión
function setSession($key, $value) {
    $_SESSION[$key] = $value;
}

// Obtener un valor de la sesión
function getSession($key, $default = null) {
    return $_SESSION[$key] ?? $default;
}

// Eliminar un valor de la sesión
function removeSession($key) {
    if (isset($_SESSION[$key])) {
        unset($_SESSION[$key]);
        return true;
    }
    return false;
}

// Verificar si existe un valor en la sesión
function hasSession($key) {
    return isset($_SESSION[$key]);
}

// Establecer un mensaje flash (se muestra una vez y se elimina)
function setFlash($key, $value) {
    $_SESSION['_flash'][$key] = $value;
}

// Obtener un mensaje flash
function getFlash($key, $default = null) {
    $value = $_SESSION['_flash'][$key] ?? $default;
    if (isset($_SESSION['_flash'][$key])) {
        unset($_SESSION['_flash'][$key]);
    }
    return $value;
}

// Verificar si existe un mensaje flash
function hasFlash($key) {
    return isset($_SESSION['_flash'][$key]);
}
```

Ejemplo de uso:

```php
// Guardar datos en la sesión
setSession('usuario_id', 123);

// Obtener datos de la sesión
$usuarioId = getSession('usuario_id');

// Establecer un mensaje flash
setFlash('mensaje', 'Operación realizada con éxito');

// En otra página, mostrar el mensaje flash
if (hasFlash('mensaje')) {
    echo '<div class="alert alert-success">' . getFlash('mensaje') . '</div>';
}
```

## Funciones de Logging

Funciones para registrar eventos y depurar:

```php
// Registrar un mensaje en el log
function log_message($level, $message, $context = []) {
    global $debugbar;
    
    $log = [
        'level' => $level,
        'message' => $message,
        'context' => $context,
        'time' => date('Y-m-d H:i:s')
    ];
    
    $debugbar["messages"]->addMessage($log);
    
    // También se puede guardar en archivo
    if (Config::$log_to_file) {
        $logFile = Config::$log_path . '/' . date('Y-m-d') . '.log';
        $logText = "[" . date('Y-m-d H:i:s') . "] [$level] $message " . json_encode($context) . PHP_EOL;
        file_put_contents($logFile, $logText, FILE_APPEND);
    }
}

// Funciones específicas por nivel
function log_info($message, $context = []) {
    log_message('INFO', $message, $context);
}

function log_error($message, $context = []) {
    log_message('ERROR', $message, $context);
}

function log_debug($message, $context = []) {
    if (flex()->dev) {
        log_message('DEBUG', $message, $context);
    }
}
```

Ejemplo de uso:

```php
// Registrar información
log_info('Usuario registrado', ['id' => 123, 'email' => '<EMAIL>']);

// Registrar error
try {
    // Código que puede fallar
} catch (Exception $e) {
    log_error('Error al procesar pago', ['error' => $e->getMessage()]);
}

// Registrar depuración (solo en entorno de desarrollo)
log_debug('Valores de variables', ['var1' => $var1, 'var2' => $var2]);
```

## Funciones de Validación

Funciones para validar datos de entrada:

```php
// Validar email
function is_valid_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// Validar URL
function is_valid_url($url) {
    return filter_var($url, FILTER_VALIDATE_URL) !== false;
}

// Validar número entero
function is_valid_int($value, $min = null, $max = null) {
    $options = [];
    if ($min !== null) $options['min_range'] = $min;
    if ($max !== null) $options['max_range'] = $max;
    
    return filter_var($value, FILTER_VALIDATE_INT, ['options' => $options]) !== false;
}

// Validar número decimal
function is_valid_float($value, $min = null, $max = null) {
    $result = filter_var($value, FILTER_VALIDATE_FLOAT);
    
    if ($result === false) return false;
    if ($min !== null && $result < $min) return false;
    if ($max !== null && $result > $max) return false;
    
    return true;
}

// Sanitizar texto (eliminar HTML y caracteres especiales)
function sanitize_text($text) {
    return htmlspecialchars(strip_tags($text), ENT_QUOTES, 'UTF-8');
}
```

Ejemplo de uso:

```php
// Validar datos de un formulario
$email = $_POST['email'] ?? '';
$edad = $_POST['edad'] ?? '';
$precio = $_POST['precio'] ?? '';
$descripcion = $_POST['descripcion'] ?? '';

$errores = [];

if (!is_valid_email($email)) {
    $errores[] = 'El email no es válido';
}

if (!is_valid_int($edad, 18, 120)) {
    $errores[] = 'La edad debe ser un número entre 18 y 120';
}

if (!is_valid_float($precio, 0)) {
    $errores[] = 'El precio debe ser un número positivo';
}

// Sanitizar texto antes de guardar
$descripcionSegura = sanitize_text($descripcion);
```

## Funciones de Fecha y Hora

Funciones para manejar fechas y horas:

```php
// Formatear fecha
function format_date($date, $format = 'd/m/Y') {
    if (!$date) return '';
    
    if (is_string($date)) {
        $date = new DateTime($date);
    }
    
    return $date->format($format);
}

// Obtener diferencia entre fechas en formato legible
function time_ago($datetime) {
    $now = new DateTime();
    $ago = new DateTime($datetime);
    $diff = $now->diff($ago);
    
    if ($diff->y > 0) {
        return $diff->y . ' año' . ($diff->y > 1 ? 's' : '') . ' atrás';
    }
    
    if ($diff->m > 0) {
        return $diff->m . ' mes' . ($diff->m > 1 ? 'es' : '') . ' atrás';
    }
    
    if ($diff->d > 0) {
        return $diff->d . ' día' . ($diff->d > 1 ? 's' : '') . ' atrás';
    }
    
    if ($diff->h > 0) {
        return $diff->h . ' hora' . ($diff->h > 1 ? 's' : '') . ' atrás';
    }
    
    if ($diff->i > 0) {
        return $diff->i . ' minuto' . ($diff->i > 1 ? 's' : '') . ' atrás';
    }
    
    return 'hace unos segundos';
}
```

Ejemplo de uso:

```php
// Formatear fecha
$fechaFormateada = format_date($usuario->fecha_registro, 'd/m/Y H:i');
echo "Fecha de registro: $fechaFormateada";

// Mostrar tiempo transcurrido
$tiempoAtras = time_ago($comentario->fecha);
echo "Publicado $tiempoAtras";
```

## Funciones de Archivos

Funciones para manejar archivos y directorios:

```php
// Subir un archivo
function upload_file($inputName, $directory, $allowedTypes = [], $maxSize = 5242880) {
    if (!isset($_FILES[$inputName]) || $_FILES[$inputName]['error'] !== UPLOAD_ERR_OK) {
        return [
            'success' => false,
            'error' => 'Error al subir el archivo: ' . upload_error_message($_FILES[$inputName]['error'] ?? UPLOAD_ERR_NO_FILE)
        ];
    }
    
    $file = $_FILES[$inputName];
    
    // Verificar tamaño
    if ($file['size'] > $maxSize) {
        return [
            'success' => false,
            'error' => 'El archivo excede el tamaño máximo permitido'
        ];
    }
    
    // Verificar tipo
    if (!empty($allowedTypes)) {
        $fileType = mime_content_type($file['tmp_name']);
        if (!in_array($fileType, $allowedTypes)) {
            return [
                'success' => false,
                'error' => 'Tipo de archivo no permitido'
            ];
        }
    }
    
    // Crear directorio si no existe
    if (!is_dir($directory)) {
        mkdir($directory, 0755, true);
    }
    
    // Generar nombre único
    $filename = uniqid() . '_' . sanitize_filename($file['name']);
    $destination = rtrim($directory, '/') . '/' . $filename;
    
    // Mover archivo
    if (move_uploaded_file($file['tmp_name'], $destination)) {
        return [
            'success' => true,
            'filename' => $filename,
            'path' => $destination,
            'url' => str_replace($_SERVER['DOCUMENT_ROOT'], '', $destination)
        ];
    } else {
        return [
            'success' => false,
            'error' => 'Error al guardar el archivo'
        ];
    }
}

// Sanitizar nombre de archivo
function sanitize_filename($filename) {
    // Eliminar caracteres especiales
    $filename = preg_replace('/[^\w\-\.]/u', '_', $filename);
    // Eliminar múltiples guiones bajos
    $filename = preg_replace('/_+/', '_', $filename);
    return $filename;
}

// Obtener mensaje de error de subida
function upload_error_message($code) {
    $errors = [
        UPLOAD_ERR_INI_SIZE => 'El archivo excede el tamaño máximo permitido por PHP',
        UPLOAD_ERR_FORM_SIZE => 'El archivo excede el tamaño máximo permitido por el formulario',
        UPLOAD_ERR_PARTIAL => 'El archivo se subió parcialmente',
        UPLOAD_ERR_NO_FILE => 'No se subió ningún archivo',
        UPLOAD_ERR_NO_TMP_DIR => 'Falta la carpeta temporal',
        UPLOAD_ERR_CANT_WRITE => 'Error al escribir el archivo en el disco',
        UPLOAD_ERR_EXTENSION => 'Una extensión de PHP detuvo la subida'
    ];
    
    return $errors[$code] ?? 'Error desconocido';
}
```

Ejemplo de uso:

```php
// Subir una imagen
$resultado = upload_file('imagen', Config::$uploads . '/imagenes', ['image/jpeg', 'image/png', 'image/gif']);

if ($resultado['success']) {
    // Guardar ruta en la base de datos
    $stmt = $pdo->prepare("UPDATE usuarios SET imagen = :imagen WHERE id = :id");
    $stmt->execute([
        'imagen' => $resultado['url'],
        'id' => $usuario->id
    ]);
    
    echo "Imagen subida correctamente";
} else {
    echo "Error: " . $resultado['error'];
}
```

## Funciones de Texto

Funciones para manipular texto:

```php
// Truncar texto
function truncate($text, $length = 100, $suffix = '...') {
    if (mb_strlen($text) <= $length) {
        return $text;
    }
    
    return mb_substr($text, 0, $length) . $suffix;
}

// Generar slug (URL amigable)
function slugify($text) {
    // Convertir a minúsculas
    $text = mb_strtolower($text);
    
    // Reemplazar caracteres especiales
    $text = str_replace(
        ['á', 'é', 'í', 'ó', 'ú', 'ü', 'ñ', ' '],
        ['a', 'e', 'i', 'o', 'u', 'u', 'n', '-'],
        $text
    );
    
    // Eliminar caracteres no alfanuméricos
    $text = preg_replace('/[^a-z0-9\-]/', '', $text);
    
    // Eliminar guiones duplicados
    $text = preg_replace('/-+/', '-', $text);
    
    // Eliminar guiones al principio y al final
    $text = trim($text, '-');
    
    return $text;
}

// Generar texto aleatorio
function random_string($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $result = '';
    
    for ($i = 0; $i < $length; $i++) {
        $result .= $characters[rand(0, strlen($characters) - 1)];
    }
    
    return $result;
}
```

Ejemplo de uso:

```php
// Truncar descripción
$descripcionCorta = truncate($producto->descripcion, 150);
echo $descripcionCorta;

// Generar slug para URL
$slug = slugify($articulo->titulo);
echo "URL: /articulos/$slug";

// Generar contraseña aleatoria
$password = random_string(12);
echo "Tu contraseña temporal es: $password";
```

## Funciones de Array y Objeto

Funciones para manipular arrays y objetos:

```php
// Convertir array a objeto
function array_to_object($array) {
    return flex()->arrayToObject($array);
}

// Obtener valor de un array multidimensional con notación de punto
function array_get($array, $key, $default = null) {
    if (is_null($key)) return $array;
    
    foreach (explode('.', $key) as $segment) {
        if (is_array($array) && array_key_exists($segment, $array)) {
            $array = $array[$segment];
        } elseif (is_object($array) && isset($array->{$segment})) {
            $array = $array->{$segment};
        } else {
            return $default;
        }
    }
    
    return $array;
}

// Filtrar array por callback
function array_filter_recursive($array, $callback) {
    $result = [];
    
    foreach ($array as $key => $value) {
        if (is_array($value)) {
            $result[$key] = array_filter_recursive($value, $callback);
        } else {
            if ($callback($value, $key)) {
                $result[$key] = $value;
            }
        }
    }
    
    return $result;
}
```

Ejemplo de uso:

```php
// Convertir array a objeto
$datos = [
    'usuario' => [
        'nombre' => 'Juan',
        'direccion' => [
            'ciudad' => 'Madrid'
        ]
    ]
];

$objeto = array_to_object($datos);
echo $objeto->usuario->direccion->ciudad;  // Madrid

// Obtener valor con notación de punto
$ciudad = array_get($datos, 'usuario.direccion.ciudad', 'Desconocida');
echo $ciudad;  // Madrid

// Filtrar valores nulos recursivamente
$datos = [
    'nombre' => 'Juan',
    'email' => '<EMAIL>',
    'telefono' => null,
    'direccion' => [
        'calle' => 'Calle Principal',
        'numero' => null,
        'ciudad' => 'Madrid'
    ]
];

$datosFiltrados = array_filter_recursive($datos, function($value) {
    return $value !== null;
});
```

## Funciones de Depuración

Funciones para facilitar la depuración:

```php
// Imprimir variable formateada
function dump($var) {
    echo '<pre>';
    var_dump($var);
    echo '</pre>';
}

// Imprimir variable y detener ejecución
function dd($var) {
    dump($var);
    die();
}

// Añadir información a la barra de depuración
function debug($info, $label = null) {
    global $debugbar;
    
    if ($label) {
        $debugbar["messages"]->addMessage([$label => $info]);
    } else {
        $debugbar["messages"]->addMessage($info);
    }
}
```

Ejemplo de uso:

```php
// Depurar variable
$usuario = getfun("getUsuario(id:123)")["data"];
dump($usuario);

// Depurar y detener
$resultado = procesarDatos($datos);
dd($resultado);

// Añadir información a la barra de depuración
debug($consulta, 'SQL');
debug($tiempoEjecucion, 'Tiempo');
```

## Ejemplos Prácticos

### Formulario con Validación

```php
// En un controlador
function procesarFormulario($nombre, $email, $edad, $mensaje) {
    $errores = [];
    
    // Validar campos
    if (empty($nombre)) {
        $errores['nombre'] = 'El nombre es obligatorio';
    }
    
    if (!is_valid_email($email)) {
        $errores['email'] = 'El email no es válido';
    }
    
    if (!is_valid_int($edad, 18, 120)) {
        $errores['edad'] = 'La edad debe ser un número entre 18 y 120';
    }
    
    if (empty($mensaje)) {
        $errores['mensaje'] = 'El mensaje es obligatorio';
    }
    
    // Si hay errores, devolver
    if (!empty($errores)) {
        return [
            'success' => false,
            'errores' => $errores
        ];
    }
    
    // Procesar datos
    // ...
    
    return [
        'success' => true,
        'mensaje' => 'Formulario procesado correctamente'
    ];
}
```

```php
<!-- En una vista -->
<?php
$errores = [];
$valores = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $resultado = getfun("procesarFormulario(nombre:{$_POST['nombre']},email:{$_POST['email']},edad:{$_POST['edad']},mensaje:{$_POST['mensaje']})")["data"];
    
    if ($resultado['success']) {
        setFlash('mensaje', $resultado['mensaje']);
        redirect('/contacto/gracias');
    } else {
        $errores = $resultado['errores'];
        $valores = $_POST;
    }
}
?>

<div class="container">
    <h1>Formulario de Contacto</h1>
    
    <form method="post">
        <div class="form-group">
            <label for="nombre">Nombre:</label>
            <input type="text" id="nombre" name="nombre" class="form-control <?= isset($errores['nombre']) ? 'is-invalid' : '' ?>" value="<?= $valores['nombre'] ?? '' ?>">
            <?php if (isset($errores['nombre'])): ?>
                <div class="invalid-feedback"><?= $errores['nombre'] ?></div>
            <?php endif; ?>
        </div>
        
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" class="form-control <?= isset($errores['email']) ? 'is-invalid' : '' ?>" value="<?= $valores['email'] ?? '' ?>">
            <?php if (isset($errores['email'])): ?>
                <div class="invalid-feedback"><?= $errores['email'] ?></div>
            <?php endif; ?>
        </div>
        
        <div class="form-group">
            <label for="edad">Edad:</label>
            <input type="number" id="edad" name="edad" class="form-control <?= isset($errores['edad']) ? 'is-invalid' : '' ?>" value="<?= $valores['edad'] ?? '' ?>">
            <?php if (isset($errores['edad'])): ?>
                <div class="invalid-feedback"><?= $errores['edad'] ?></div>
            <?php endif; ?>
        </div>
        
        <div class="form-group">
            <label for="mensaje">Mensaje:</label>
            <textarea id="mensaje" name="mensaje" class="form-control <?= isset($errores['mensaje']) ? 'is-invalid' : '' ?>"><?= $valores['mensaje'] ?? '' ?></textarea>
            <?php if (isset($errores['mensaje'])): ?>
                <div class="invalid-feedback"><?= $errores['mensaje'] ?></div>
            <?php endif; ?>
        </div>
        
        <button type="submit" class="btn btn-primary">Enviar</button>
    </form>
</div>
```

### Subida de Archivos

```php
// En un controlador
function subirImagen($id) {
    $resultado = upload_file('imagen', Config::$uploads . '/imagenes', ['image/jpeg', 'image/png', 'image/gif']);
    
    if (!$resultado['success']) {
        return [
            'success' => false,
            'error' => $resultado['error']
        ];
    }
    
    // Guardar en la base de datos
    global $pdo;
    $stmt = $pdo->prepare("UPDATE productos SET imagen = :imagen WHERE id = :id");
    $stmt->execute([
        'imagen' => $resultado['url'],
        'id' => $id
    ]);
    
    return [
        'success' => true,
        'mensaje' => 'Imagen subida correctamente',
        'url' => $resultado['url']
    ];
}
```

```php
<!-- En una vista -->
<div class="container">
    <h1>Subir Imagen del Producto</h1>
    
    <?php if (hasFlash('error')): ?>
        <div class="alert alert-danger"><?= getFlash('error') ?></div>
    <?php endif; ?>
    
    <?php if (hasFlash('mensaje')): ?>
        <div class="alert alert-success"><?= getFlash('mensaje') ?></div>
    <?php endif; ?>
    
    <form method="post" enctype="multipart/form-data" action="?fun[]=subirImagen(id:<?= $producto->id ?>)">
        <div class="form-group">
            <label for="imagen">Imagen:</label>
            <input type="file" id="imagen" name="imagen" class="form-control-file" accept="image/*" required>
            <small class="form-text text-muted">Formatos permitidos: JPG, PNG, GIF. Tamaño máximo: 5MB.</small>
        </div>
        
        <button type="submit" class="btn btn-primary">Subir Imagen</button>
    </form>
    
    <?php if ($producto->imagen): ?>
        <div class="mt-4">
            <h3>Imagen Actual</h3>
            <img src="<?= $producto->imagen ?>" alt="<?= $producto->nombre ?>" class="img-thumbnail" style="max-width: 300px;">
        </div>
    <?php endif; ?>
</div>
```

## Siguientes Pasos

- Descubre las herramientas de [Depuración](09_depuracion.md) para solucionar problemas.
- Aprende sobre la [Integración con Frontend](10_frontend.md) para crear interfaces dinámicas.
- Explora el [Índice](00_indice.md) para encontrar más información sobre el framework.
