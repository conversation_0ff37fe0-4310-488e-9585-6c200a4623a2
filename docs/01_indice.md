Documentación del Framework - Guía Completa
1. Núcleo del Framework
Archivos principales:  includes/Flex.php,  includes/framework.php,  index.php

Arquitectura general
El framework implementa una estructura MVC ligera donde:

Modelo: Generado automáticamente desde la base de datos
Vista: Sistema de plantillas basado en PHP nativo con partials
Controlador: Funciones PHP organizadas por secciones
Inicialización
// En framework.php
function init($opt)
{
    uri();
    set_dev($opt['dev']);
   
    foreach($opt as $key=>$value){
        if ($key == "dev") continue;
        if ($key == "extra") continue;
        Config::${$key} = $value;

Clase Config
Centraliza la configuración del framework y la aplicación:

class Config
{
    public static $css = 'css';
    public static $files = "files";
    public static $uploads = "uploads";
    public static $home = 'inicio';
    // ...más propiedades...

    static function add($value){
        self::$extra[] = $value;

Sistema de rutas
Manejo de URI y parámetros mediante funciones como uri() y la clase Flex:

function uri($param = null, $param2 = null)
{
    // Implementación para obtener segmentos de la URL
}
Gestión de vistas
Carga dinámica de páginas basada en la URL:

function page()
{
    // Determina la ruta del archivo de la página actual
    // ...
    return $fullpath;
}
2. Sistema de Ejecución Dinámica
Archivos principales: includes/funciones_fun.php

Función fun()
Permite ejecutar funciones PHP directamente desde la URL:

function fun($funcion, $params = [])
{
    // Ejecuta la función especificada con los parámetros dados
    // Registra la ejecución en el log
}
Función getfun()
Similar a fun() pero devuelve datos en lugar de imprimirlos:

Rastreo de origen
Detecta si la función fue llamada desde URL, FETCH, enlace o formulario:

Logging detallado
Registra todas las ejecuciones de funciones con sus parámetros:

Integración con debugbar
Visualiza logs y ejecuciones en tiempo real durante el desarrollo:

3. Autenticación y Autorización
Archivos principales: includes/auth.php

Gestión de usuarios
Clase User para manejar la información y sesiones de usuario:

Sistema de roles
Niveles de acceso del 0 al 10 para controlar permisos:

function hasRole($minRole)
{
    global $user;
    return $user->rol >= $minRole;
}
Zonas protegidas
Control de acceso por secciones de la aplicación:

function protectZone($zone, $minRole)
{
    if (!hasRole($minRole)) {
        redirect('/login?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    }
}
Funciones de login/logout
Gestión de sesiones de usuario:

Verificación por código
Sistema de verificación en dos pasos:

Loading...
4. Helpers y Utilidades
Archivos principales: includes/helpers.php

Redirecciones
Loading...
Gestión de sesiones
Funciones para manejo de datos de sesión:

Logging
Función para registro de eventos:

Loading...
Funciones auxiliares
Utilidades comunes para el desarrollo:

5. Generación de Modelos
Archivos principales: models/generate.php

Generación automática
Creación de clases PHP a partir de tablas de la base de datos:

Mapeo de tipos
Conversión de tipos SQL a PHP para tipado correcto:

Autocompletado
Soporte para sugerencias en IDE mediante PHPDoc:

// Ejemplo de uso:
/** @var \Anuncio $anuncio */
$anuncio = getfun("getAnuncio(id:2)")["data"];
// El IDE ahora sugerirá propiedades como $anuncio->titulo
Documentación automática
Generación de PHPDoc para clases:

// Ejemplo de clase generada:
/**
 * Representa la tabla 'usuarios' en la base de datos
 * @property int $id ID único del usuario
 * @property string $username Nombre de usuario
 * @property string $email Correo electrónico
 */
class Usuario {
    // Propiedades y métodos
}
6. Sistema de Plantillas
Archivos principales: partials/, framework.php (funciones partial(), head(), nav())

Partials
Sistema de componentes reutilizables:

function partial($name)
{
    $fullpath = Config::$partials . "/$name.php";
    if (file_exists($fullpath)) {
        return $fullpath;
    } else {
        throw new \Exception("El archivo parcial no existe en: $fullpath");
    }
}
Layouts
Estructura de páginas con cabecera y pie:

Zonas específicas
Plantillas por sección (_panel, _adm, etc.):

Integración con objetos
Uso de objetos en plantillas HTML para acceso directo a propiedades:

7. Depuración y Desarrollo
Archivos principales:  includes/framework.php,  index.php

Modo desarrollo
Detección automática y manual del entorno:

public function dev() : bool{
    if(PHP_OS == 'WINNT') {
        return true;
    }
    $dominio = $_SERVER['SERVER_NAME'];
    if (preg_match("/^dev./", $dominio) || preg_match("/.test$/", $dominio) || preg_match("/.dev$/", $dominio)) {
        return true;
    } else{
        return false;

Debugbar
Integración con PHP DebugBar para monitoreo en tiempo real:

$debugbar = new DebugBar\StandardDebugBar();
$debugbarRenderer = $debugbar->getJavascriptRenderer();
$debugbar->addCollector(new DebugBar\DataCollector\ConfigCollector(app\debug_config(), "Config"));
Logging visual
Visualización de logs y eventos en la interfaz:

Información de sistema
Datos de configuración y entorno para diagnóstico:

$dev = [
    "Route" => "uri:".$flex->uri().", view: ". $flex->view.", zone:$flex->zone",
    "view_path & zones" => $flex->view_path. ", zones: ". json_encode($flex->zones),
    // ...más información...
];
$debugbar->addCollector(new DebugBar\DataCollector\ConfigCollector($dev, "Dev"));
8. Enrutamiento y URL
Archivos principales: .htaccess, includes/Flex.php

Reescritura de URL
Configuración de Apache para URLs limpias:

Parámetros limpios
URLs amigables sin GET visible:

Gestión de secciones
Estructura jerárquica de rutas:

function getPageName(){
    // Admin zone
    if ((strlen(uri(1)) >= 1) && uri(0) != "anuncio"){
        $section = uri(0);
        $page = uri(1);
        $path = "/$section/$page";
    }else{
        $section = "";
        $page = uri(0);
        $path = "/$page";

Redirecciones automáticas
Manejo de 404 y rutas inválidas:

function page()
{
    // ...
    if (file_exists($fullpath)) {
        return $fullpath;
    } else {
        http_response_code(404);
        echo "<script>console.log('No se encontra la página $fullpath')</script>"; 
        header("Location: /inicio/");
    }

9. Características Únicas
Archivos principales: includes/funciones_fun.php, includes/Flex.php

Ejecución desde URL
Sistema &fun[]=funcion(param:valor):

// URL: /pagina?fun[]=guardarDatos(nombre:Juan,edad:30)
// Ejecuta automáticamente la función guardarDatos()
Rastreo de origen de llamadas
Detección de fuente de ejecución:

Conversión array-objeto
Función para transformar arrays en objetos navegables:

Ligereza extrema
Framework completo en menos de 1000 líneas, optimizado para rendimiento:

10. Integración con Frontend
Archivos principales: js/, partials/footer_*.php

Helpers JavaScript
Funciones auxiliares para frontend:

Integración con AJAX/Fetch
Soporte para llamadas asíncronas:

Sistema de pasos
Navegación por pasos en formularios:

<div class="bolas">
    <div data="1" class="pasobtn <?=flex()->view == 'detalle' ? 'superado' : '' ?>">
        <div class="bola step1">1</div>
        <div class="boton2">
            <i class="fa-solid fa-address-card fa-xl"></i>
            <span>Detalle</span>
        </div>
    </div>
    <!-- Más pasos... -->
</div>
Mensajes al usuario
Sistema de alertas y notificaciones:

Esta documentación proporciona una visión completa del framework, destacando sus características únicas y su eficiencia. Cada sección incluye ejemplos de código y explicaciones detalladas para facilitar su comprensión y uso.

