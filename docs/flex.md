
# FLEXHTTP

## Properties

## zone

Sera el primer segmento de la URL, en caso de que no exista una carpeta en paginas, sera "index"

## view
- Establece la zona index a la ruta absoluta, el resto usara el primer valor de la URL como zona.
- Ignora parametros GET.
- Ignora variables de phprouter.
- Elimina slash al inicial y final de la URL.
- Elimina valores númericos al final de la URL.

### Ejemplos:
- / -> index/index
- /servicios -> servicios/index
- /blog/ -> blog/index
- /blog/noticias/ -> blog/noticias
- /blog/noticias/1 -> blog/noticias
- /blog/noticias/&search=algo -> blog/noticias
- /blog/noticias/1&search=algo -> blog/noticias
- /blog/noticias/&fun[]=buscar(q:algo) -> blog/noticias

### Usos
- Es usada para formatear view_path agregando la carpeta /paginas/ al inicio y el .php al final.
 
¿Cómo diferencia de una pagina de index y una de una carpeta?

/blog/ -> index/blog -> blog/index

