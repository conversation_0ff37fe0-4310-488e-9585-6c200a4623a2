# Sistema de Autenticación y Autorización en FlexHTTP

FlexHTTP incluye un sistema de autenticación y autorización que permite gestionar usuarios, roles y permisos de acceso a diferentes secciones de la aplicación.

## Conceptos Básicos

El sistema de autenticación se basa en:

1. **Usuarios**: Almacenados en la base de datos con sus credenciales y roles
2. **Roles**: Niveles de acceso del 0 al 10 para controlar permisos
3. **Sesiones**: Almacenamiento de datos de usuario autenticado
4. **Zonas Protegidas**: Áreas de la aplicación que requieren autenticación

## Estructura del Sistema

El sistema de autenticación se implementa principalmente en el archivo `includes/auth.php`:

```php
<?php
// includes/auth.php

// Clase User para gestionar usuarios
class User {
    public $id;
    public $username;
    public $email;
    public $nombre;
    public $rol;
    public $logged_in = false;
    
    public function __construct($userData = null) {
        if ($userData) {
            $this->id = $userData['id'] ?? 0;
            $this->username = $userData['username'] ?? '';
            $this->email = $userData['email'] ?? '';
            $this->nombre = $userData['nombre'] ?? '';
            $this->rol = $userData['rol'] ?? 0;
            $this->logged_in = true;
        }
    }
    
    public static function current() {
        if (isset($_SESSION['user']) && $_SESSION['user']['id'] > 0) {
            return new User($_SESSION['user']);
        }
        
        return new User();
    }
}

// Inicializar usuario actual
$user = User::current();

// Verificar si el usuario tiene un rol mínimo
function hasRole($minRole) {
    global $user;
    return $user->logged_in && $user->rol >= $minRole;
}

// Proteger una zona por rol
function protectZone($zone, $minRole) {
    if (!hasRole($minRole)) {
        redirect('/login?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    }
}

// Iniciar sesión de usuario
function login($email, $password) {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT * FROM usuarios WHERE email = :email");
    $stmt->execute(['email' => $email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user && password_verify($password, $user['password'])) {
        // Eliminar la contraseña antes de guardar en sesión
        unset($user['password']);
        
        // Guardar usuario en sesión
        $_SESSION['user'] = $user;
        
        // Registrar inicio de sesión
        $stmt = $pdo->prepare("UPDATE usuarios SET ultimo_login = NOW() WHERE id = :id");
        $stmt->execute(['id' => $user['id']]);
        
        return true;
    }
    
    return false;
}

// Cerrar sesión
function logout() {
    unset($_SESSION['user']);
    session_destroy();
    redirect('/login');
}

// Verificar si el usuario está autenticado
function isLoggedIn() {
    return isset($_SESSION['user']) && $_SESSION['user']['id'] > 0;
}

// Obtener usuario actual
function currentUser() {
    global $user;
    return $user;
}
```

## Roles y Niveles de Acceso

FlexHTTP utiliza un sistema de roles numéricos del 0 al 10:

- **0**: Usuario anónimo (no autenticado)
- **1**: Usuario básico
- **2-4**: Usuarios con permisos limitados
- **5-7**: Usuarios con permisos avanzados
- **8-9**: Moderadores y administradores
- **10**: Superadministrador

La función `hasRole()` verifica si el usuario tiene un nivel de acceso mínimo:

```php
function hasRole($minRole) {
    global $user;
    return $user->logged_in && $user->rol >= $minRole;
}
```

## Protección de Zonas

La función `protectZone()` permite restringir el acceso a ciertas secciones de la aplicación:

```php
function protectZone($zone, $minRole) {
    if (!hasRole($minRole)) {
        redirect('/login?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    }
}
```

Ejemplo de uso:

```php
// En paginas/admin/dashboard.php
<?php
protectZone('admin', 8);  // Solo administradores (rol 8+)
?>

<h1>Panel de Administración</h1>
<!-- Contenido solo para administradores -->
```

## Proceso de Login

El proceso de login implica verificar las credenciales y almacenar los datos del usuario en la sesión:

```php
function login($email, $password) {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT * FROM usuarios WHERE email = :email");
    $stmt->execute(['email' => $email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user && password_verify($password, $user['password'])) {
        // Eliminar la contraseña antes de guardar en sesión
        unset($user['password']);
        
        // Guardar usuario en sesión
        $_SESSION['user'] = $user;
        
        // Registrar inicio de sesión
        $stmt = $pdo->prepare("UPDATE usuarios SET ultimo_login = NOW() WHERE id = :id");
        $stmt->execute(['id' => $user['id']]);
        
        return true;
    }
    
    return false;
}
```

Implementación en un formulario de login:

```php
// En paginas/login.php
<?php
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if (login($email, $password)) {
        $redirect = $_GET['redirect'] ?? '/inicio';
        redirect($redirect);
    } else {
        $error = 'Credenciales incorrectas';
    }
}
?>

<div class="container">
    <h1>Iniciar Sesión</h1>
    
    <?php if ($error): ?>
        <div class="alert alert-danger"><?= $error ?></div>
    <?php endif; ?>
    
    <form method="post">
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" class="form-control" required>
        </div>
        <div class="form-group">
            <label for="password">Contraseña:</label>
            <input type="password" id="password" name="password" class="form-control" required>
        </div>
        <button type="submit" class="btn btn-primary">Iniciar Sesión</button>
    </form>
    
    <p class="mt-3">
        <a href="/registro">¿No tienes cuenta? Regístrate</a>
    </p>
</div>
```

## Proceso de Registro

Implementación de un formulario de registro:

```php
// En paginas/registro.php
<?php
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    $nombre = $_POST['nombre'] ?? '';
    
    // Verificar si el email ya existe
    $stmt = $pdo->prepare("SELECT id FROM usuarios WHERE email = :email");
    $stmt->execute(['email' => $email]);
    
    if ($stmt->rowCount() > 0) {
        $error = 'El email ya está registrado';
    } else {
        // Crear usuario
        $stmt = $pdo->prepare("INSERT INTO usuarios (username, email, password, nombre, fecha_registro, rol) VALUES (:username, :email, :password, :nombre, NOW(), 1)");
        $result = $stmt->execute([
            'username' => $username,
            'email' => $email,
            'password' => password_hash($password, PASSWORD_DEFAULT),
            'nombre' => $nombre
        ]);
        
        if ($result) {
            // Iniciar sesión automáticamente
            login($email, $password);
            redirect('/inicio');
        } else {
            $error = 'Error al registrar el usuario';
        }
    }
}
?>

<div class="container">
    <h1>Registro de Usuario</h1>
    
    <?php if ($error): ?>
        <div class="alert alert-danger"><?= $error ?></div>
    <?php endif; ?>
    
    <form method="post">
        <div class="form-group">
            <label for="username">Nombre de usuario:</label>
            <input type="text" id="username" name="username" class="form-control" required>
        </div>
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" class="form-control" required>
        </div>
        <div class="form-group">
            <label for="password">Contraseña:</label>
            <input type="password" id="password" name="password" class="form-control" required>
        </div>
        <div class="form-group">
            <label for="nombre">Nombre completo:</label>
            <input type="text" id="nombre" name="nombre" class="form-control" required>
        </div>
        <button type="submit" class="btn btn-primary">Registrarse</button>
    </form>
    
    <p class="mt-3">
        <a href="/login">¿Ya tienes cuenta? Inicia sesión</a>
    </p>
</div>
```

## Verificación por Código

FlexHTTP puede implementar un sistema de verificación en dos pasos:

```php
// Enviar código de verificación
function enviarCodigoVerificacion($email) {
    global $pdo;
    
    // Generar código aleatorio
    $codigo = mt_rand(100000, 999999);
    
    // Guardar código en la base de datos
    $stmt = $pdo->prepare("UPDATE usuarios SET codigo_verificacion = :codigo, expiracion_codigo = DATE_ADD(NOW(), INTERVAL 15 MINUTE) WHERE email = :email");
    $stmt->execute([
        'codigo' => $codigo,
        'email' => $email
    ]);
    
    // Enviar email con el código
    $asunto = "Código de verificación";
    $mensaje = "Tu código de verificación es: $codigo";
    mail($email, $asunto, $mensaje);
    
    return true;
}

// Verificar código
function verificarCodigo($email, $codigo) {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT id FROM usuarios WHERE email = :email AND codigo_verificacion = :codigo AND expiracion_codigo > NOW()");
    $stmt->execute([
        'email' => $email,
        'codigo' => $codigo
    ]);
    
    return $stmt->rowCount() > 0;
}
```

## Gestión de Permisos en Vistas

Las vistas pueden mostrar u ocultar elementos según el rol del usuario:

```php
<!-- En una vista -->
<nav class="navbar">
    <ul class="navbar-nav">
        <li class="nav-item"><a href="/inicio" class="nav-link">Inicio</a></li>
        <li class="nav-item"><a href="/productos" class="nav-link">Productos</a></li>
        
        <?php if (isLoggedIn()): ?>
            <li class="nav-item"><a href="/perfil" class="nav-link">Mi Perfil</a></li>
            
            <?php if (hasRole(5)): ?>
                <li class="nav-item"><a href="/admin/dashboard" class="nav-link">Administración</a></li>
            <?php endif; ?>
            
            <li class="nav-item">
                <a href="?fun[]=logout" class="nav-link">Cerrar Sesión</a>
            </li>
        <?php else: ?>
            <li class="nav-item"><a href="/login" class="nav-link">Iniciar Sesión</a></li>
            <li class="nav-item"><a href="/registro" class="nav-link">Registrarse</a></li>
        <?php endif; ?>
    </ul>
</nav>
```

## Recuperación de Contraseña

Implementación de un sistema de recuperación de contraseña:

```php
// Solicitar recuperación
function solicitarRecuperacion($email) {
    global $pdo;
    
    // Verificar si el email existe
    $stmt = $pdo->prepare("SELECT id FROM usuarios WHERE email = :email");
    $stmt->execute(['email' => $email]);
    
    if ($stmt->rowCount() === 0) {
        return false;
    }
    
    // Generar token único
    $token = bin2hex(random_bytes(32));
    
    // Guardar token en la base de datos
    $stmt = $pdo->prepare("UPDATE usuarios SET token_recuperacion = :token, expiracion_token = DATE_ADD(NOW(), INTERVAL 1 DAY) WHERE email = :email");
    $stmt->execute([
        'token' => $token,
        'email' => $email
    ]);
    
    // Enviar email con enlace de recuperación
    $enlace = "http://" . $_SERVER['HTTP_HOST'] . "/recuperar-password?token=$token";
    $asunto = "Recuperación de contraseña";
    $mensaje = "Para recuperar tu contraseña, haz clic en el siguiente enlace: $enlace";
    mail($email, $asunto, $mensaje);
    
    return true;
}

// Verificar token de recuperación
function verificarTokenRecuperacion($token) {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT id FROM usuarios WHERE token_recuperacion = :token AND expiracion_token > NOW()");
    $stmt->execute(['token' => $token]);
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

// Cambiar contraseña
function cambiarPassword($token, $newPassword) {
    global $pdo;
    
    $usuario = verificarTokenRecuperacion($token);
    
    if (!$usuario) {
        return false;
    }
    
    // Actualizar contraseña y limpiar token
    $stmt = $pdo->prepare("UPDATE usuarios SET password = :password, token_recuperacion = NULL, expiracion_token = NULL WHERE id = :id");
    $result = $stmt->execute([
        'password' => password_hash($newPassword, PASSWORD_DEFAULT),
        'id' => $usuario['id']
    ]);
    
    return $result;
}
```

## Ejemplos Prácticos

### Proteger una Sección Completa

```php
// En paginas/admin/index.php (archivo principal de la sección admin)
<?php
protectZone('admin', 8);  // Requiere rol 8 o superior

// Incluir el controlador de administración
include controller('admin');
?>

<div class="admin-container">
    <div class="sidebar">
        <h3>Administración</h3>
        <ul>
            <li><a href="/admin/dashboard">Dashboard</a></li>
            <li><a href="/admin/usuarios">Usuarios</a></li>
            <li><a href="/admin/productos">Productos</a></li>
            <li><a href="/admin/configuracion">Configuración</a></li>
        </ul>
    </div>
    
    <div class="content">
        <?php include $flex->view_path; ?>
    </div>
</div>
```

### Implementar Diferentes Niveles de Acceso

```php
// En un controlador
function getUsuarios() {
    global $pdo, $user;
    
    // Consulta base
    $sql = "SELECT * FROM usuarios";
    
    // Modificar consulta según el rol
    if ($user->rol < 10) {
        // Administradores normales no ven superadmins
        $sql .= " WHERE rol < 10";
    }
    
    if ($user->rol < 8) {
        // Usuarios normales solo ven su propio perfil
        $sql .= " WHERE id = " . $user->id;
    }
    
    $stmt = $pdo->query($sql);
    return $stmt->fetchAll(PDO::FETCH_OBJ);
}
```

### Mostrar Mensajes Según el Rol

```php
<!-- En una vista -->
<div class="dashboard">
    <h1>Bienvenido, <?= $user->nombre ?></h1>
    
    <?php if (hasRole(10)): ?>
        <div class="alert alert-warning">
            <strong>Modo Superadministrador:</strong> Tienes acceso completo al sistema.
        </div>
    <?php elseif (hasRole(8)): ?>
        <div class="alert alert-info">
            <strong>Modo Administrador:</strong> Puedes gestionar usuarios y contenido.
        </div>
    <?php elseif (hasRole(5)): ?>
        <div class="alert alert-success">
            <strong>Modo Editor:</strong> Puedes crear y editar contenido.
        </div>
    <?php else: ?>
        <div class="alert alert-secondary">
            <strong>Modo Usuario:</strong> Puedes ver y comentar contenido.
        </div>
    <?php endif; ?>
    
    <!-- Resto del contenido -->
</div>
```

## Siguientes Pasos

- Explora las [Utilidades](08_utilidades.md) para facilitar el desarrollo.
- Descubre las herramientas de [Depuración](09_depuracion.md) para solucionar problemas.
- Aprende sobre la [Integración con Frontend](10_frontend.md) para crear interfaces dinámicas.
