# Arquitectura del Framework FlexHTTP

FlexHTTP implementa una arquitectura MVC (Modelo-Vista-Controlador) ligera y flexible, diseñada para ser intuitiva y eficiente.

## Visión General

El framework se organiza en tres componentes principales:

1. **Modelo**: Generado automáticamente desde la base de datos, representa los datos y la lógica de negocio.
2. **Vista**: Sistema de plantillas basado en PHP nativo con componentes reutilizables (partials).
3. **Controlador**: Funciones PHP organizadas por secciones que manejan la lógica de la aplicación.

## Componentes Principales

### Clase Flex

La clase `Flex` es el núcleo del framework, responsable de gestionar las rutas, vistas y configuración:

```php
class Flex
{ 
    public readonly string $method, $is_page, $dev;
    public $view, $view_path, $args, $route, $error;
    public $router = ["parameters" => "", "route" => ""]; 

    public function __construct(array $config = [])
    {
        $this->args = $this->args();
        $this->view = $this->view();
        $this->view_path = $this->view_path();
        $this->method = strtolower($_SERVER["REQUEST_METHOD"]);
        $this->route = $this->route();
        $this->is_page = $this->_is_page();
        $this->dev = $this->dev();
        $this->zone = $this->uri(1);
        $this->router_route = "";
        $this->router_parameters = "";
        $this->path = $this->path();
    }
    
    // Métodos para gestión de rutas, vistas, etc.
}
```

Se accede a esta clase a través de la función global `flex()`:

```php
function flex() : Flex {
    global $flex;
    return $flex;
}
```

### Clase Config

La clase `Config` centraliza la configuración del framework y la aplicación:

```php
class Config
{
    public static $css = 'css';
    public static $files = "files";
    public static $uploads = "uploads";
    public static $home = 'inicio';
    public static $js = "js";
    public static $libs = 'libs';
    public static $page404 = '404.php';
    public static $pages = 'paginas';
    public static $controllers = 'controllers';
    public static $partials = 'partials';
    public static $extra = ["test"=> "hola"];

    // Set by functions in startup
    public static $dev;
    public static $uri;
    public static $page;
    public static $controller;

    static function add($value){
        self::$extra[] = $value;
    }
}
```

### Funciones del Framework

El archivo `framework.php` contiene las funciones principales que gestionan la inicialización, enrutamiento y carga de componentes:

```php
// Inicialización del framework
function init($opt)
{
    uri();
    set_dev($opt['dev']);
   
    foreach($opt as $key=>$value){
        if ($key == "dev") continue;
        if ($key == "extra") continue;
        Config::${$key} = $value;
    }

    // Configuración adicional
    Config::add('example', true);
}

// Carga de páginas
function page()
{
    // Determina la ruta del archivo de la página actual
    // ...
    return $fullpath;
}

// Carga de controladores
function controller()
{
    $name = uri(0);
    Config::$controller = $name; 
    $fullpath = Config::$controllers . "/$name". "Controller.php";
    
    if (file_exists($fullpath)) {
        return $fullpath;
    } 
}
```

## Flujo de Ejecución

1. **Inicialización**: El archivo `index.php` carga las dependencias y configura el entorno.
2. **Enrutamiento**: La clase `Flex` analiza la URL y determina la vista y controlador a cargar.
3. **Carga de Controlador**: Se incluye el controlador correspondiente a la sección actual.
4. **Ejecución de Funciones**: Si hay funciones dinámicas en la URL, se ejecutan.
5. **Carga de Vista**: Se incluye el archivo de vista correspondiente a la ruta actual.
6. **Renderizado**: Se muestra la página al usuario con los datos procesados.

### Diagrama de Flujo

```
[Solicitud HTTP] → [index.php] → [Inicialización del Framework]
                                       ↓
[Renderizado] ← [Carga de Vista] ← [Enrutamiento y Análisis de URL]
                                       ↓
                                 [Carga de Controlador]
                                       ↓
                                 [Ejecución de Funciones]
```

## Estructura de Archivos

```
/
├── includes/                # Núcleo del framework
│   ├── Flex.php            # Clase principal del framework
│   ├── framework.php       # Funciones y configuración base
│   ├── funciones_fun.php   # Sistema de ejecución dinámica
│   ├── auth.php            # Autenticación y autorización
│   ├── helpers.php         # Funciones auxiliares
│   └── pdo.php             # Conexión a base de datos
├── controllers/            # Controladores de la aplicación
├── models/                 # Modelos de datos
│   └── generate.php        # Generador automático de modelos
├── paginas/                # Vistas/páginas de la aplicación
├── partials/               # Componentes reutilizables
│   ├── head_*.php          # Cabeceras por sección
│   ├── nav_*.php           # Navegación por sección
│   └── footer_*.php        # Pies de página por sección
└── index.php               # Punto de entrada de la aplicación
```

## Características Arquitectónicas Clave

### Generación Automática de Modelos

El framework puede generar automáticamente clases PHP a partir de tablas de la base de datos:

```php
// Ejemplo de clase generada
/**
 * Representa la tabla 'usuarios' en la base de datos
 * @property int $id ID único del usuario
 * @property string $username Nombre de usuario
 * @property string $email Correo electrónico
 */
class Usuario {
    // Propiedades y métodos generados automáticamente
}
```

### Sistema de Plantillas

El sistema de plantillas utiliza PHP nativo con componentes reutilizables:

```php
function partial($name)
{
    $fullpath = Config::$partials . "/$name.php";
    if (file_exists($fullpath)) {
        return $fullpath;
    } else {
        throw new \Exception("El archivo parcial no existe en: $fullpath");
    }
}
```

### Detección de Entorno

El framework detecta automáticamente si está en entorno de desarrollo o producción:

```php
public function dev() : bool{
    if(PHP_OS == 'WINNT') {
        return true;
    }
    $dominio = $_SERVER['SERVER_NAME'];
    if (preg_match("/^dev./", $dominio) || preg_match("/.test$/", $dominio) || preg_match("/.dev$/", $dominio)) {
        return true;
    } else{
        return false;
    }
}
```

## Extensibilidad

FlexHTTP está diseñado para ser fácilmente extensible:

1. **Añadir Nuevas Funcionalidades**: Simplemente crea nuevas funciones en los controladores.
2. **Integrar Librerías Externas**: Usa Composer para gestionar dependencias.
3. **Personalizar Configuración**: Modifica la clase `Config` o pasa opciones al método `init()`.
4. **Crear Plugins**: Añade nuevas funcionalidades mediante archivos en la carpeta `includes`.

## Siguientes Pasos

- Explora el [Sistema de Enrutamiento](03_routing.md) para entender cómo se gestionan las URLs.
- Aprende sobre los [Controladores](04_controladores.md) y la ejecución dinámica de funciones.
- Descubre el [Sistema de Vistas](05_vistas.md) para crear interfaces de usuario.
