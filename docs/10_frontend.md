# Integración con Frontend en FlexHTTP

FlexHTTP proporciona herramientas y utilidades para integrar el backend con el frontend, facilitando la creación de interfaces de usuario dinámicas y reactivas.

## Estructura de Archivos Frontend

Los archivos frontend se organizan en directorios específicos:

```
/
├── css/              # Archivos CSS
├── js/               # Archivos JavaScript
│   ├── app.js        # JavaScript principal
│   └── components/   # Componentes JavaScript
├── libs/             # Bibliotecas de terceros
└── partials/         # Componentes de plantilla
    ├── footer.php    # Pie de página con scripts
    └── head.php      # Cabecera con estilos
```

## Inclusión de Recursos

FlexHTTP proporciona una forma sencilla de incluir recursos CSS y JavaScript:

```php
// En partials/head.php
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mi Aplicación</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="/<?= Config::$css ?>/styles.css">
    
    <!-- Bibliotecas externas -->
    <link rel="stylesheet" href="/<?= Config::$libs ?>/bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="/<?= Config::$libs ?>/fontawesome/css/all.min.css">
</head>
<body>

// En partials/footer.php
    <!-- JavaScript -->
    <script src="/<?= Config::$libs ?>/jquery/jquery.min.js"></script>
    <script src="/<?= Config::$libs ?>/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="/<?= Config::$js ?>/app.js"></script>
</body>
</html>
```

## Helpers JavaScript

FlexHTTP incluye funciones auxiliares en JavaScript para facilitar la interacción con el backend:

```javascript
// En js/app.js

// Función para llamar a funciones PHP desde JavaScript
async function callFunction(functionName, params = {}) {
    const url = `/api?getfun[]=${functionName}(${Object.entries(params).map(([key, value]) => `${key}:${value}`).join(',')})`;
    
    try {
        const response = await fetch(url);
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error al llamar a la función:', error);
        return { success: false, error: error.message };
    }
}

// Función para enviar formularios mediante AJAX
async function submitForm(form, functionName) {
    const formData = new FormData(form);
    const params = {};
    
    for (const [key, value] of formData.entries()) {
        params[key] = value;
    }
    
    return await callFunction(functionName, params);
}

// Función para cargar contenido dinámicamente
async function loadContent(container, url) {
    try {
        const response = await fetch(url);
        const html = await response.text();
        document.querySelector(container).innerHTML = html;
    } catch (error) {
        console.error('Error al cargar contenido:', error);
    }
}

// Función para mostrar notificaciones
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} notification`;
    notification.innerHTML = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}
```

## Integración con AJAX/Fetch

FlexHTTP facilita la creación de endpoints para AJAX:

```php
// En un controlador
function getProductos($categoria = null, $orden = 'nombre') {
    global $pdo;
    
    $sql = "SELECT * FROM productos WHERE activo = 1";
    $params = [];
    
    if ($categoria) {
        $sql .= " AND categoria_id = :categoria";
        $params['categoria'] = $categoria;
    }
    
    $sql .= " ORDER BY $orden";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $productos = $stmt->fetchAll(PDO::FETCH_OBJ);
    
    return $productos;
}
```

```javascript
// En JavaScript
document.addEventListener('DOMContentLoaded', async () => {
    const categoriaSelect = document.getElementById('categoria');
    const ordenSelect = document.getElementById('orden');
    const productosContainer = document.getElementById('productos');
    
    async function cargarProductos() {
        const categoria = categoriaSelect.value;
        const orden = ordenSelect.value;
        
        const resultado = await callFunction('getProductos', { categoria, orden });
        
        if (resultado.success && resultado.data) {
            productosContainer.innerHTML = '';
            
            resultado.data.forEach(producto => {
                const card = document.createElement('div');
                card.className = 'card producto';
                card.innerHTML = `
                    <img src="${producto.imagen}" class="card-img-top" alt="${producto.nombre}">
                    <div class="card-body">
                        <h5 class="card-title">${producto.nombre}</h5>
                        <p class="card-text">${producto.descripcion}</p>
                        <p class="card-price">${producto.precio} €</p>
                        <button class="btn btn-primary" onclick="agregarAlCarrito(${producto.id})">Añadir al carrito</button>
                    </div>
                `;
                
                productosContainer.appendChild(card);
            });
        } else {
            productosContainer.innerHTML = '<p>No se encontraron productos</p>';
        }
    }
    
    categoriaSelect.addEventListener('change', cargarProductos);
    ordenSelect.addEventListener('change', cargarProductos);
    
    // Cargar productos iniciales
    cargarProductos();
});
```

## Sistema de Pasos

FlexHTTP incluye un sistema para crear formularios por pasos:

```html
<div class="bolas">
    <div data="1" class="pasobtn <?=flex()->view == 'detalle' ? 'superado' : '' ?>">
        <div class="bola step1">1</div>
        <div class="boton2">
            <i class="fa-solid fa-address-card fa-xl"></i>
            <span>Detalle</span>
        </div>
    </div>
    <div data="2" class="pasobtn <?=flex()->view == 'fotos' ? 'superado' : '' ?>">
        <div class="bola step2">2</div>
        <div class="boton2">
            <i class="fa-solid fa-images fa-xl"></i>
            <span>Fotos</span>
        </div>
    </div>
    <!-- Más pasos... -->
</div>
```

```javascript
// En JavaScript
document.addEventListener('DOMContentLoaded', () => {
    const pasos = document.querySelectorAll('.pasobtn');
    
    pasos.forEach(paso => {
        paso.addEventListener('click', () => {
            const numeroPaso = paso.getAttribute('data');
            const destino = obtenerDestinoPaso(numeroPaso);
            
            if (destino) {
                window.location.href = destino;
            }
        });
    });
    
    function obtenerDestinoPaso(numeroPaso) {
        switch (numeroPaso) {
            case '1': return '/anuncio/detalle';
            case '2': return '/anuncio/fotos';
            case '3': return '/anuncio/ubicacion';
            case '4': return '/anuncio/publicar';
            default: return null;
        }
    }
});
```

## Mensajes al Usuario

FlexHTTP proporciona un sistema para mostrar mensajes y alertas al usuario:

```php
// En PHP
function mostrarMensaje($tipo, $mensaje) {
    $_SESSION['mensaje'] = [
        'tipo' => $tipo,  // 'success', 'error', 'warning', 'info'
        'texto' => $mensaje
    ];
}
```

```html
<!-- En la vista -->
<?php if (isset($_SESSION['mensaje'])): ?>
    <div class="alert alert-<?= $_SESSION['mensaje']['tipo'] ?>">
        <?= $_SESSION['mensaje']['texto'] ?>
    </div>
    <?php unset($_SESSION['mensaje']); ?>
<?php endif; ?>
```

```javascript
// En JavaScript
function mostrarMensaje(tipo, mensaje) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${tipo} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${mensaje}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Cerrar"></button>
    `;
    
    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Desaparecer automáticamente después de 5 segundos
    setTimeout(() => {
        alertDiv.classList.remove('show');
        setTimeout(() => {
            alertDiv.remove();
        }, 150);
    }, 5000);
}
```

## Validación de Formularios

FlexHTTP facilita la validación de formularios tanto en el servidor como en el cliente:

```php
// En PHP (servidor)
function validarFormulario($datos) {
    $errores = [];
    
    if (empty($datos['nombre'])) {
        $errores['nombre'] = 'El nombre es obligatorio';
    }
    
    if (empty($datos['email']) || !filter_var($datos['email'], FILTER_VALIDATE_EMAIL)) {
        $errores['email'] = 'El email no es válido';
    }
    
    if (empty($datos['password']) || strlen($datos['password']) < 6) {
        $errores['password'] = 'La contraseña debe tener al menos 6 caracteres';
    }
    
    return $errores;
}
```

```javascript
// En JavaScript (cliente)
document.addEventListener('DOMContentLoaded', () => {
    const form = document.getElementById('registro-form');
    
    form.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        // Limpiar errores anteriores
        document.querySelectorAll('.is-invalid').forEach(el => {
            el.classList.remove('is-invalid');
        });
        document.querySelectorAll('.invalid-feedback').forEach(el => {
            el.remove();
        });
        
        // Validar formulario en el cliente
        let valid = true;
        
        const nombre = form.querySelector('#nombre');
        if (!nombre.value.trim()) {
            mostrarError(nombre, 'El nombre es obligatorio');
            valid = false;
        }
        
        const email = form.querySelector('#email');
        if (!email.value.trim() || !isValidEmail(email.value)) {
            mostrarError(email, 'El email no es válido');
            valid = false;
        }
        
        const password = form.querySelector('#password');
        if (!password.value || password.value.length < 6) {
            mostrarError(password, 'La contraseña debe tener al menos 6 caracteres');
            valid = false;
        }
        
        if (valid) {
            // Enviar formulario al servidor
            const formData = new FormData(form);
            const params = {};
            
            for (const [key, value] of formData.entries()) {
                params[key] = value;
            }
            
            const resultado = await callFunction('registrarUsuario', params);
            
            if (resultado.success) {
                mostrarMensaje('success', 'Usuario registrado correctamente');
                setTimeout(() => {
                    window.location.href = '/login';
                }, 2000);
            } else if (resultado.errores) {
                // Mostrar errores del servidor
                for (const [campo, mensaje] of Object.entries(resultado.errores)) {
                    const input = form.querySelector(`#${campo}`);
                    if (input) {
                        mostrarError(input, mensaje);
                    }
                }
            } else {
                mostrarMensaje('error', 'Error al registrar el usuario');
            }
        }
    });
    
    function mostrarError(input, mensaje) {
        input.classList.add('is-invalid');
        
        const feedback = document.createElement('div');
        feedback.className = 'invalid-feedback';
        feedback.textContent = mensaje;
        
        input.parentNode.appendChild(feedback);
    }
    
    function isValidEmail(email) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    }
});
```

## Carga Dinámica de Contenido

FlexHTTP facilita la carga dinámica de contenido mediante AJAX:

```php
// En un controlador
function getProductoHTML($id) {
    $producto = getProducto($id);
    
    if (!$producto) {
        return '<p>Producto no encontrado</p>';
    }
    
    ob_start();
    ?>
    <div class="producto-detalle">
        <h2><?= $producto->nombre ?></h2>
        <img src="<?= $producto->imagen ?>" alt="<?= $producto->nombre ?>">
        <p class="descripcion"><?= $producto->descripcion ?></p>
        <p class="precio"><?= $producto->precio ?> €</p>
        <button class="btn btn-primary" onclick="agregarAlCarrito(<?= $producto->id ?>)">Añadir al carrito</button>
    </div>
    <?php
    return ob_get_clean();
}
```

```javascript
// En JavaScript
async function cargarProductoDetalle(id) {
    const resultado = await callFunction('getProductoHTML', { id });
    
    if (resultado.success && resultado.data) {
        document.getElementById('producto-container').innerHTML = resultado.data;
    } else {
        document.getElementById('producto-container').innerHTML = '<p>Error al cargar el producto</p>';
    }
}

// Uso
document.querySelectorAll('.ver-producto').forEach(btn => {
    btn.addEventListener('click', () => {
        const id = btn.getAttribute('data-id');
        cargarProductoDetalle(id);
    });
});
```

## Integración con Librerías Frontend

FlexHTTP se integra fácilmente con librerías frontend populares:

### Ejemplo con Bootstrap

```php
// En partials/head.php
<link rel="stylesheet" href="/<?= Config::$libs ?>/bootstrap/css/bootstrap.min.css">

// En partials/footer.php
<script src="/<?= Config::$libs ?>/bootstrap/js/bootstrap.bundle.min.js"></script>
```

### Ejemplo con Font Awesome

```php
// En partials/head.php
<link rel="stylesheet" href="/<?= Config::$libs ?>/fontawesome/css/all.min.css">
```

### Ejemplo con Select2

```php
// En partials/head.php (para una página específica)
<link rel="stylesheet" href="/<?= Config::$libs ?>/select2/css/select2.min.css">
<link rel="stylesheet" href="/<?= Config::$libs ?>/select2/css/select2-bootstrap4.min.css">

// En partials/footer.php (para una página específica)
<script src="/<?= Config::$libs ?>/select2/js/select2.min.js"></script>
<script>
    $(document).ready(function() {
        $('.select2').select2({
            theme: 'bootstrap4'
        });
    });
</script>
```

## Ejemplos Prácticos

### Formulario AJAX

```html
<!-- En una vista -->
<div class="container">
    <h1>Contacto</h1>
    
    <form id="contacto-form">
        <div class="mb-3">
            <label for="nombre" class="form-label">Nombre</label>
            <input type="text" class="form-control" id="nombre" name="nombre" required>
        </div>
        <div class="mb-3">
            <label for="email" class="form-label">Email</label>
            <input type="email" class="form-control" id="email" name="email" required>
        </div>
        <div class="mb-3">
            <label for="mensaje" class="form-label">Mensaje</label>
            <textarea class="form-control" id="mensaje" name="mensaje" rows="5" required></textarea>
        </div>
        <button type="submit" class="btn btn-primary">Enviar</button>
    </form>
    
    <div id="resultado" class="mt-3"></div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', () => {
        const form = document.getElementById('contacto-form');
        const resultado = document.getElementById('resultado');
        
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            resultado.innerHTML = '<div class="alert alert-info">Enviando mensaje...</div>';
            
            const formData = new FormData(form);
            const params = {};
            
            for (const [key, value] of formData.entries()) {
                params[key] = value;
            }
            
            const response = await callFunction('enviarContacto', params);
            
            if (response.success) {
                resultado.innerHTML = '<div class="alert alert-success">Mensaje enviado correctamente</div>';
                form.reset();
            } else {
                resultado.innerHTML = `<div class="alert alert-danger">Error: ${response.error || 'No se pudo enviar el mensaje'}</div>`;
            }
        });
    });
</script>
```

```php
// En un controlador
function enviarContacto($nombre, $email, $mensaje) {
    // Validar datos
    if (empty($nombre) || empty($email) || empty($mensaje)) {
        return [
            'success' => false,
            'error' => 'Todos los campos son obligatorios'
        ];
    }
    
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        return [
            'success' => false,
            'error' => 'El email no es válido'
        ];
    }
    
    // Enviar email
    $asunto = "Nuevo mensaje de contacto";
    $cuerpo = "Nombre: $nombre\nEmail: $email\nMensaje: $mensaje";
    $destinatario = "<EMAIL>";
    
    $enviado = mail($destinatario, $asunto, $cuerpo);
    
    if ($enviado) {
        // Guardar en base de datos
        global $pdo;
        $stmt = $pdo->prepare("INSERT INTO contactos (nombre, email, mensaje, fecha) VALUES (:nombre, :email, :mensaje, NOW())");
        $stmt->execute([
            'nombre' => $nombre,
            'email' => $email,
            'mensaje' => $mensaje
        ]);
        
        return [
            'success' => true,
            'mensaje' => 'Mensaje enviado correctamente'
        ];
    } else {
        return [
            'success' => false,
            'error' => 'No se pudo enviar el mensaje'
        ];
    }
}
```

### Filtrado Dinámico

```html
<!-- En una vista -->
<div class="container">
    <h1>Productos</h1>
    
    <div class="row mb-4">
        <div class="col-md-4">
            <label for="categoria" class="form-label">Categoría</label>
            <select id="categoria" class="form-select">
                <option value="">Todas las categorías</option>
                <?php foreach ($categorias as $categoria): ?>
                    <option value="<?= $categoria->id ?>"><?= $categoria->nombre ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="col-md-4">
            <label for="precio-min" class="form-label">Precio mínimo</label>
            <input type="number" id="precio-min" class="form-control" min="0" step="0.01">
        </div>
        <div class="col-md-4">
            <label for="precio-max" class="form-label">Precio máximo</label>
            <input type="number" id="precio-max" class="form-control" min="0" step="0.01">
        </div>
    </div>
    
    <div class="row mb-3">
        <div class="col-md-6">
            <label for="orden" class="form-label">Ordenar por</label>
            <select id="orden" class="form-select">
                <option value="nombre">Nombre (A-Z)</option>
                <option value="nombre DESC">Nombre (Z-A)</option>
                <option value="precio">Precio (menor a mayor)</option>
                <option value="precio DESC">Precio (mayor a menor)</option>
                <option value="fecha DESC">Más recientes</option>
            </select>
        </div>
        <div class="col-md-6">
            <label for="busqueda" class="form-label">Buscar</label>
            <input type="text" id="busqueda" class="form-control" placeholder="Buscar productos...">
        </div>
    </div>
    
    <div id="productos-container" class="row">
        <!-- Aquí se cargarán los productos -->
        <div class="col-12 text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Cargando...</span>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', () => {
        const categoriaSelect = document.getElementById('categoria');
        const precioMinInput = document.getElementById('precio-min');
        const precioMaxInput = document.getElementById('precio-max');
        const ordenSelect = document.getElementById('orden');
        const busquedaInput = document.getElementById('busqueda');
        const productosContainer = document.getElementById('productos-container');
        
        let timeoutId;
        
        async function cargarProductos() {
            const params = {
                categoria: categoriaSelect.value,
                precioMin: precioMinInput.value,
                precioMax: precioMaxInput.value,
                orden: ordenSelect.value,
                busqueda: busquedaInput.value
            };
            
            productosContainer.innerHTML = `
                <div class="col-12 text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Cargando...</span>
                    </div>
                </div>
            `;
            
            const resultado = await callFunction('filtrarProductos', params);
            
            if (resultado.success && resultado.data) {
                if (resultado.data.length > 0) {
                    productosContainer.innerHTML = '';
                    
                    resultado.data.forEach(producto => {
                        productosContainer.innerHTML += `
                            <div class="col-md-4 mb-4">
                                <div class="card h-100">
                                    <img src="${producto.imagen}" class="card-img-top" alt="${producto.nombre}">
                                    <div class="card-body">
                                        <h5 class="card-title">${producto.nombre}</h5>
                                        <p class="card-text">${truncateText(producto.descripcion, 100)}</p>
                                        <p class="card-price">${formatPrice(producto.precio)} €</p>
                                    </div>
                                    <div class="card-footer">
                                        <a href="/productos/detalle/${producto.id}" class="btn btn-primary">Ver detalle</a>
                                        <button class="btn btn-success" onclick="agregarAlCarrito(${producto.id})">
                                            <i class="fas fa-cart-plus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                } else {
                    productosContainer.innerHTML = `
                        <div class="col-12">
                            <div class="alert alert-info">
                                No se encontraron productos con los filtros seleccionados.
                            </div>
                        </div>
                    `;
                }
            } else {
                productosContainer.innerHTML = `
                    <div class="col-12">
                        <div class="alert alert-danger">
                            Error al cargar los productos.
                        </div>
                    </div>
                `;
            }
        }
        
        function truncateText(text, length) {
            if (text.length <= length) return text;
            return text.substring(0, length) + '...';
        }
        
        function formatPrice(price) {
            return parseFloat(price).toFixed(2).replace('.', ',');
        }
        
        // Eventos para filtros
        categoriaSelect.addEventListener('change', cargarProductos);
        ordenSelect.addEventListener('change', cargarProductos);
        
        precioMinInput.addEventListener('input', () => {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(cargarProductos, 500);
        });
        
        precioMaxInput.addEventListener('input', () => {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(cargarProductos, 500);
        });
        
        busquedaInput.addEventListener('input', () => {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(cargarProductos, 500);
        });
        
        // Cargar productos iniciales
        cargarProductos();
    });
</script>
```

```php
// En un controlador
function filtrarProductos($categoria = '', $precioMin = '', $precioMax = '', $orden = 'nombre', $busqueda = '') {
    global $pdo;
    
    $sql = "SELECT * FROM productos WHERE activo = 1";
    $params = [];
    
    if (!empty($categoria)) {
        $sql .= " AND categoria_id = :categoria";
        $params['categoria'] = $categoria;
    }
    
    if (!empty($precioMin)) {
        $sql .= " AND precio >= :precioMin";
        $params['precioMin'] = $precioMin;
    }
    
    if (!empty($precioMax)) {
        $sql .= " AND precio <= :precioMax";
        $params['precioMax'] = $precioMax;
    }
    
    if (!empty($busqueda)) {
        $sql .= " AND (nombre LIKE :busqueda OR descripcion LIKE :busqueda)";
        $params['busqueda'] = "%$busqueda%";
    }
    
    $sql .= " ORDER BY $orden";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $productos = $stmt->fetchAll(PDO::FETCH_OBJ);
    
    return $productos;
}
```

## Siguientes Pasos

- Explora el [Índice](00_indice.md) para encontrar más información sobre el framework.
- Revisa la [Introducción](01_introduccion.md) para entender los conceptos básicos del framework.
- Aprende sobre la [Arquitectura](02_arquitectura.md) para comprender la estructura interna del framework.
