# Documentación del Framework FlexHTTP

## Índice de Contenidos

1. [Introducción](01_introduccion.md) - Visión general del framework, características principales y requisitos
2. [Arquitectura](02_arquitectura.md) - Estructura MVC, componentes principales y flujo de ejecución
3. [Enrutamiento](03_routing.md) - Sistema de rutas, URLs limpias y parámetros
4. [Controladores](04_controladores.md) - Sistema de controladores y ejecución dinámica de funciones
5. [Vistas](05_vistas.md) - Sistema de plantillas, partials y layouts
6. [Modelos](06_modelos.md) - Generación automática de modelos desde la base de datos
7. [Autenticación](07_autenticacion.md) - Sistema de autenticación, autorización y roles
8. [Utilidades](08_utilidades.md) - Helpers y funciones auxiliares
9. [Depuración](09_depuracion.md) - Herramientas de depuración y desarrollo
10. [Frontend](10_frontend.md) - Integración con JavaScript y frontend

## Características Destacadas

- **Ligereza**: Framework completo en menos de 1000 líneas de código
- **Ejecución dinámica**: Llamada a funciones directamente desde URL
- **Generación automática de modelos**: Creación de clases PHP a partir de tablas de la base de datos
- **Sistema de plantillas nativo**: Basado en PHP sin dependencias adicionales
- **Depuración integrada**: Herramientas de desarrollo y monitoreo en tiempo real
- **Conversión array-objeto**: Transformación automática para mejor experiencia de desarrollo

## Recursos Adicionales

- [Ejemplos prácticos](ejemplos/)
- [Referencia de API](api_reference.md)
- [Guía de migración](migracion.md)
- [Preguntas frecuentes](faq.md)
