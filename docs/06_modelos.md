# Generación de Modelos en FlexHTTP

FlexHTTP incluye un sistema de generación automática de modelos a partir de la estructura de la base de datos, lo que facilita el trabajo con datos estructurados y proporciona autocompletado en IDEs.

## Concepto de Generación Automática

El sistema analiza las tablas de la base de datos y genera clases PHP que representan cada tabla, incluyendo:

- Propiedades tipadas para cada columna
- Documentación PHPDoc para autocompletado en IDEs
- Métodos para operaciones CRUD básicas
- Relaciones entre tablas

## Estructura de Modelos

Los modelos generados se almacenan en el directorio `models/`:

```
/models
├── generate.php         # Script de generación
├── Usuario.php          # Modelo generado para la tabla 'usuarios'
├── Producto.php         # Modelo generado para la tabla 'productos'
└── ...
```

## Proceso de Generación

El archivo `models/generate.php` contiene la lógica para generar los modelos:

```php
<?php
// models/generate.php

function generarModelos() {
    global $pdo;
    
    // Obtener todas las tablas de la base de datos
    $stmt = $pdo->query("SHOW TABLES");
    $tablas = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($tablas as $tabla) {
        // Obtener estructura de la tabla
        $stmt = $pdo->query("DESCRIBE `$tabla`");
        $columnas = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Generar nombre de clase (singular y capitalizado)
        $clase = singularizar(ucfirst($tabla));
        
        // Generar contenido del archivo
        $contenido = generarContenidoModelo($clase, $tabla, $columnas);
        
        // Guardar archivo
        file_put_contents("models/$clase.php", $contenido);
        
        echo "Modelo generado: $clase.php\n";
    }
}

function generarContenidoModelo($clase, $tabla, $columnas) {
    // Generar PHPDoc
    $phpDoc = "/**\n";
    $phpDoc .= " * Representa la tabla '$tabla' en la base de datos\n";
    
    foreach ($columnas as $columna) {
        $tipo = mapearTipo($columna['Type']);
        $phpDoc .= " * @property $tipo \${$columna['Field']} {$columna['Comment']}\n";
    }
    
    $phpDoc .= " */\n";
    
    // Generar clase
    $contenido = "<?php\n\n";
    $contenido .= $phpDoc;
    $contenido .= "class $clase {\n";
    
    // Propiedades
    foreach ($columnas as $columna) {
        $tipo = mapearTipo($columna['Type']);
        $contenido .= "    public $tipo \${$columna['Field']};\n";
    }
    
    // Métodos CRUD básicos
    $contenido .= generarMetodosCRUD($clase, $tabla, $columnas);
    
    $contenido .= "}\n";
    
    return $contenido;
}

function mapearTipo($tipoSQL) {
    if (strpos($tipoSQL, 'int') !== false) return 'int';
    if (strpos($tipoSQL, 'float') !== false || strpos($tipoSQL, 'double') !== false || strpos($tipoSQL, 'decimal') !== false) return 'float';
    if (strpos($tipoSQL, 'bool') !== false) return 'bool';
    return 'string';
}

function generarMetodosCRUD($clase, $tabla, $columnas) {
    // Implementación de métodos CRUD
    // ...
}

function singularizar($palabra) {
    // Reglas básicas de singularización
    $reglas = [
        '/es$/' => '',
        '/s$/' => ''
    ];
    
    foreach ($reglas as $patron => $reemplazo) {
        if (preg_match($patron, $palabra)) {
            return preg_replace($patron, $reemplazo, $palabra);
        }
    }
    
    return $palabra;
}

// Ejecutar generación
generarModelos();
```

## Ejemplo de Modelo Generado

```php
<?php

/**
 * Representa la tabla 'usuarios' en la base de datos
 * @property int $id ID único del usuario
 * @property string $username Nombre de usuario
 * @property string $email Correo electrónico
 * @property string $password Contraseña encriptada
 * @property string $nombre Nombre completo
 * @property string $fecha_registro Fecha de registro
 * @property int $rol Nivel de acceso (0-10)
 */
class Usuario {
    public int $id;
    public string $username;
    public string $email;
    public string $password;
    public string $nombre;
    public string $fecha_registro;
    public int $rol;
    
    /**
     * Obtiene un usuario por su ID
     * @param int $id ID del usuario
     * @return Usuario|null Usuario encontrado o null
     */
    public static function obtenerPorId($id) {
        global $pdo;
        $stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = :id");
        $stmt->execute(['id' => $id]);
        $datos = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$datos) return null;
        
        return self::crearDesdeArray($datos);
    }
    
    /**
     * Crea un objeto Usuario a partir de un array
     * @param array $datos Datos del usuario
     * @return Usuario
     */
    public static function crearDesdeArray($datos) {
        $usuario = new Usuario();
        
        foreach ($datos as $campo => $valor) {
            if (property_exists($usuario, $campo)) {
                $usuario->$campo = $valor;
            }
        }
        
        return $usuario;
    }
    
    /**
     * Guarda el usuario en la base de datos
     * @return bool Resultado de la operación
     */
    public function guardar() {
        global $pdo;
        
        if (isset($this->id) && $this->id > 0) {
            // Actualizar
            $sql = "UPDATE usuarios SET username = :username, email = :email, nombre = :nombre, rol = :rol WHERE id = :id";
            $stmt = $pdo->prepare($sql);
            return $stmt->execute([
                'id' => $this->id,
                'username' => $this->username,
                'email' => $this->email,
                'nombre' => $this->nombre,
                'rol' => $this->rol
            ]);
        } else {
            // Insertar
            $sql = "INSERT INTO usuarios (username, email, password, nombre, fecha_registro, rol) VALUES (:username, :email, :password, :nombre, NOW(), :rol)";
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([
                'username' => $this->username,
                'email' => $this->email,
                'password' => $this->password,
                'nombre' => $this->nombre,
                'rol' => $this->rol
            ]);
            
            if ($result) {
                $this->id = $pdo->lastInsertId();
            }
            
            return $result;
        }
    }
    
    /**
     * Elimina el usuario de la base de datos
     * @return bool Resultado de la operación
     */
    public function eliminar() {
        global $pdo;
        $stmt = $pdo->prepare("DELETE FROM usuarios WHERE id = :id");
        return $stmt->execute(['id' => $this->id]);
    }
    
    /**
     * Lista todos los usuarios
     * @return Usuario[] Array de usuarios
     */
    public static function listarTodos() {
        global $pdo;
        $stmt = $pdo->query("SELECT * FROM usuarios");
        $datos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $usuarios = [];
        foreach ($datos as $dato) {
            $usuarios[] = self::crearDesdeArray($dato);
        }
        
        return $usuarios;
    }
}
```

## Uso de Modelos Generados

### Obtener un Registro

```php
// Usando la clase directamente
$usuario = Usuario::obtenerPorId(123);
echo $usuario->nombre;

// Usando getfun()
/** @var \Usuario $usuario */
$usuario = getfun("getUsuario(id:123)")["data"];
echo $usuario->nombre;
```

### Crear un Nuevo Registro

```php
$usuario = new Usuario();
$usuario->username = "juanperez";
$usuario->email = "<EMAIL>";
$usuario->password = password_hash("123456", PASSWORD_DEFAULT);
$usuario->nombre = "Juan Pérez";
$usuario->rol = 1;

if ($usuario->guardar()) {
    echo "Usuario guardado con ID: " . $usuario->id;
} else {
    echo "Error al guardar el usuario";
}
```

### Actualizar un Registro

```php
$usuario = Usuario::obtenerPorId(123);
$usuario->email = "<EMAIL>";
$usuario->nombre = "Juan Pérez Actualizado";

if ($usuario->guardar()) {
    echo "Usuario actualizado correctamente";
} else {
    echo "Error al actualizar el usuario";
}
```

### Eliminar un Registro

```php
$usuario = Usuario::obtenerPorId(123);

if ($usuario->eliminar()) {
    echo "Usuario eliminado correctamente";
} else {
    echo "Error al eliminar el usuario";
}
```

### Listar Registros

```php
$usuarios = Usuario::listarTodos();

foreach ($usuarios as $usuario) {
    echo "ID: {$usuario->id}, Nombre: {$usuario->nombre}, Email: {$usuario->email}<br>";
}
```

## Beneficios del Autocompletado

El uso de PHPDoc en los modelos generados proporciona autocompletado en IDEs como PhpStorm, VSCode, etc.:

```php
/** @var \Usuario $usuario */
$usuario = getfun("getUsuario(id:123)")["data"];

// El IDE sugerirá las propiedades disponibles
$usuario->nombre;  // Autocompletado funciona
$usuario->email;   // Autocompletado funciona
```

## Relaciones entre Modelos

Los modelos generados pueden incluir métodos para gestionar relaciones:

```php
/**
 * Representa la tabla 'productos' en la base de datos
 * @property int $id ID único del producto
 * @property string $nombre Nombre del producto
 * @property string $descripcion Descripción del producto
 * @property float $precio Precio del producto
 * @property int $categoria_id ID de la categoría
 */
class Producto {
    // Propiedades...
    
    /**
     * Obtiene la categoría del producto
     * @return Categoria Categoría del producto
     */
    public function categoria() {
        return Categoria::obtenerPorId($this->categoria_id);
    }
    
    // Otros métodos...
}

/**
 * Representa la tabla 'categorias' en la base de datos
 * @property int $id ID único de la categoría
 * @property string $nombre Nombre de la categoría
 */
class Categoria {
    // Propiedades...
    
    /**
     * Obtiene los productos de esta categoría
     * @return Producto[] Array de productos
     */
    public function productos() {
        global $pdo;
        $stmt = $pdo->prepare("SELECT * FROM productos WHERE categoria_id = :id");
        $stmt->execute(['id' => $this->id]);
        $datos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $productos = [];
        foreach ($datos as $dato) {
            $productos[] = Producto::crearDesdeArray($dato);
        }
        
        return $productos;
    }
    
    // Otros métodos...
}
```

Uso de relaciones:

```php
$producto = Producto::obtenerPorId(123);
$categoria = $producto->categoria();
echo "El producto {$producto->nombre} pertenece a la categoría {$categoria->nombre}";

$categoria = Categoria::obtenerPorId(5);
$productos = $categoria->productos();
echo "La categoría {$categoria->nombre} tiene " . count($productos) . " productos";
```

## Personalización de Modelos

Los modelos generados pueden ser personalizados añadiendo métodos adicionales:

```php
// En models/Usuario.php (después de la generación)

// Añadir método personalizado
/**
 * Verifica si la contraseña es correcta
 * @param string $password Contraseña a verificar
 * @return bool Resultado de la verificación
 */
public function verificarPassword($password) {
    return password_verify($password, $this->password);
}

/**
 * Busca usuarios por nombre o email
 * @param string $termino Término de búsqueda
 * @return Usuario[] Usuarios encontrados
 */
public static function buscar($termino) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM usuarios WHERE nombre LIKE :termino OR email LIKE :termino");
    $stmt->execute(['termino' => "%$termino%"]);
    $datos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $usuarios = [];
    foreach ($datos as $dato) {
        $usuarios[] = self::crearDesdeArray($dato);
    }
    
    return $usuarios;
}
```

## Regeneración de Modelos

Para mantener los modelos actualizados con la estructura de la base de datos, se puede ejecutar el script de generación periódicamente:

```php
// Desde línea de comandos
php models/generate.php

// O desde una página de administración
if (hasRole(10)) {  // Solo administradores
    include "models/generate.php";
}
```

## Siguientes Pasos

- Aprende sobre el [Sistema de Autenticación](07_autenticacion.md) para proteger tus rutas y funciones.
- Explora las [Utilidades](08_utilidades.md) para facilitar el desarrollo.
- Descubre las herramientas de [Depuración](09_depuracion.md) para solucionar problemas.
