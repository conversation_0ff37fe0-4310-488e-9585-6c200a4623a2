# Sistema de Enrutamiento en FlexHTTP

El sistema de enrutamiento de FlexHTTP permite gestionar las URLs de forma limpia y eficiente, facilitando la creación de aplicaciones con URLs amigables y SEO-friendly.

## Conceptos Básicos

FlexHTTP utiliza un sistema de enrutamiento basado en la estructura de directorios y archivos, complementado con funciones para analizar y manipular las URLs.

### Estructura de URL

Las URLs en FlexHTTP siguen este patrón:

```
http://sitio.com/[seccion]/[pagina]/[parametros]
```

Donde:
- **seccion**: Opcional, representa una sección o área de la aplicación
- **pagina**: Nombre de la página a cargar
- **parametros**: Parámetros adicionales para la página

## Función URI

La función `uri()` es el corazón del sistema de enrutamiento, permitiendo obtener y manipular los segmentos de la URL:

```php
// En Flex.php
public function uri($param = null, $param2 = null)
{
    $request = isset($_GET['url']) ? strtolower($_GET['url']) : '/';
    $request = "/" . trim($request, '/') . "/";
    $requestArray = explode('/', $request);
    // Limpia el array eliminando elementos vacíos
    $requestArray = array_filter($requestArray, function ($value) {
        return $value !== '' && $value !== null;
    });

    if (($param === null || $param === 0) && $param2 === null) return $request;

    // Devuelve la URL completa
    if ($param === "full") return $_SERVER["REQUEST_URI"];
    // Devuelve los segmentos como un array
    if ($param === "a") return $requestArray;
    // Devuelve los segmentos como JSON
    if ($param === "json") return json_encode($requestArray, JSON_UNESCAPED_SLASHES);

    if (is_string($param)) {
        if (str_starts_with($param, ":")) {
            // Búsqueda de expresiones regulares
            $pattern = '/' . substr($param, 1) . '/';
            $full_request = $_SERVER["SERVER_NAME"].$request;
            preg_match($pattern, $full_request, $matches);
            return $matches[0] ?? false;
        }
        return in_array($param, $requestArray);   
    }
    
    // Obtener segmento específico por índice
    if (is_numeric($param)) {
        // Índice negativo para contar desde el final
        if ($param < 0) {
            $param = count($requestArray) + $param;
        }
        return $requestArray[$param] ?? false;
    }
    
    // Recortar segmentos
    if (is_numeric($param) && is_numeric($param2)) {
        // Implementación para recortar segmentos
    }
}
```

### Ejemplos de Uso de URI

```php
// Obtener la URL completa
$fullUrl = flex()->uri("full");  // Ej: "/usuarios/perfil/123"

// Obtener el primer segmento
$seccion = flex()->uri(1);  // Ej: "usuarios"

// Obtener el segundo segmento
$pagina = flex()->uri(2);   // Ej: "perfil"

// Verificar si un segmento existe en la URL
$tieneUsuarios = flex()->uri("usuarios");  // true/false

// Buscar con expresión regular
$esDominioDev = flex()->uri(":^dev.");  // true/false

// Obtener todos los segmentos como array
$segmentos = flex()->uri("a");  // ["usuarios", "perfil", "123"]
```

## Carga de Páginas

La función `page()` determina qué archivo de vista debe cargarse según la URL actual:

```php
function page()
{
    // Admin zone
    if (strlen(uri(1)) > 0 && uri(0) != 'anuncio'){
        $section = uri(0);
        $page = uri(1);
        $path = "/$section/$page";
    } else {
        $section = "";
        $page = uri(0);
        $path = "/$page";
    } 

    $fullpath = Config::$pages.$path.".php";   
 
    if (file_exists($fullpath)) {
        return $fullpath;
    } else {
        http_response_code(404);
        echo "<script>console.log('No se encontra la página $fullpath')</script>"; 
        header("Location: /inicio/");
    }
}
```

## Reescritura de URL con .htaccess

FlexHTTP utiliza reglas de reescritura en Apache para crear URLs limpias:

```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php?url=$1 [QSA,L]
```

Esto permite transformar URLs como:
- `http://sitio.com/index.php?url=usuarios/perfil/123`

En URLs limpias:
- `http://sitio.com/usuarios/perfil/123`

## Redirecciones

FlexHTTP proporciona funciones para realizar redirecciones:

```php
// En Flex.php
public function redirect($dest = false){  
    $url = $dest ? $dest : flex()->path;
    echo "<script>window.location.href='$url'</script>"; 
}

// En framework.php
function redirect($dest = false){
    global $uri;
    $dest = $dest ? $dest : $uri;
    echo "<script>window.location.href='$dest'</script>"; 
}
```

## Estructura de Secciones

FlexHTTP organiza las páginas en secciones, lo que permite una estructura jerárquica:

```
/paginas
├── inicio.php           # Accesible como /inicio
├── contacto.php         # Accesible como /contacto
├── admin/               # Sección "admin"
│   ├── dashboard.php    # Accesible como /admin/dashboard
│   └── usuarios.php     # Accesible como /admin/usuarios
└── blog/                # Sección "blog"
    ├── articulos.php    # Accesible como /blog/articulos
    └── categorias.php   # Accesible como /blog/categorias
```

La función `section()` permite determinar la sección actual:

```php
function section(){
    return strlen(uri(1)) > 0 ? uri(0) : false; 
}
```

## Verificación de Página Actual

La función `ispage()` permite verificar si la página actual coincide con alguna de las especificadas:

```php
function ispage(...$args){
    foreach($args as $arg){
        if (uri(0) == $arg){
            return true;
        }
    }
    return false;
}
```

Ejemplo de uso:

```php
if (ispage('inicio', 'home')) {
    // Código específico para la página de inicio
}
```

## Rutas Protegidas

Para proteger rutas según el rol del usuario:

```php
function protectZone($zone, $minRole)
{
    if (!hasRole($minRole)) {
        redirect('/login?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    }
}
```

Ejemplo de uso:

```php
// En una página de administración
protectZone('admin', 5);  // Requiere rol 5 o superior
```

## Detección de API

FlexHTTP puede detectar si una solicitud es para la API:

```php
function api(){
    if (isset($_GET['api'])) return true; 
}
```

Esto permite crear endpoints de API junto con páginas normales:

```php
if (api()) {
    // Devolver datos en formato JSON
    header('Content-Type: application/json');
    echo json_encode($datos);
    exit;
} else {
    // Mostrar página normal
    include $vista;
}
```

## Ejemplos Prácticos

### Crear una Ruta Básica

1. Crea un archivo en `paginas/productos.php`:
```php
<h1>Listado de Productos</h1>
<!-- Contenido de la página -->
```

2. Accede a través de `http://sitio.com/productos`

### Crear una Ruta con Sección

1. Crea un archivo en `paginas/admin/productos.php`:
```php
<?php protectZone('admin', 5); ?>
<h1>Administración de Productos</h1>
<!-- Contenido de la página -->
```

2. Accede a través de `http://sitio.com/admin/productos`

### Crear una Ruta con Parámetros

1. En un controlador, crea una función para obtener detalles de un producto:
```php
function obtenerProducto($id) {
    // Lógica para obtener el producto
    return $producto;
}
```

2. En `paginas/productos/detalle.php`:
```php
<?php
$id = flex()->uri(3); // Obtiene el tercer segmento
$producto = obtenerProducto($id);
?>
<h1>Detalle del Producto: <?= $producto->nombre ?></h1>
<!-- Contenido de la página -->
```

3. Accede a través de `http://sitio.com/productos/detalle/123`

## Siguientes Pasos

- Aprende sobre los [Controladores](04_controladores.md) y cómo implementar la lógica de negocio.
- Explora el [Sistema de Vistas](05_vistas.md) para crear interfaces de usuario.
- Descubre cómo utilizar la [Ejecución Dinámica de Funciones](04_controladores.md#ejecucion-dinamica) para llamar a funciones desde la URL.
