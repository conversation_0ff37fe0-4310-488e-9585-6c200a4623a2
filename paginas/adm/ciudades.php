<?php

// Fetch all cities
$ciudades = flex()->fetchAll("SELECT * FROM ciudades ORDER BY pais ASC, nombre ASC");

// Group cities by country
$ciudadesPorPais = [];
foreach ($ciudades as $ciudad) {
    if (!isset($ciudadesPorPais[$ciudad->pais])) {
        $ciudadesPorPais[$ciudad->pais] = [];
    }
    $ciudadesPorPais[$ciudad->pais][] = $ciudad;
}

// Sort countries alphabetically
ksort($ciudadesPorPais);

// Handle delete action
if (isset($_POST['delete'])) {
    $id = $_POST['delete'];
    flex()->query("DELETE FROM ciudades WHERE id = $id");
    flex()->redirect(); 
}

// Recuperar el nombre del pais por alpha_2
function getPaisName($alpha_2){
    $pais = flex()->fetch("SELECT name FROM paises WHERE alpha_2 = '" . $alpha_2 . "'");
    return $pais->name;
}
 

?>

<div class="container mt-4">
    <h3 class="text-center mb-4">Gestión de Ciudades</h3>
    
    <div class="card shadow-sm">
        <div class="card-header bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Listado de Ciudades</h5>
                <a href="/adm/ciudad-agregar" class="btn btn-sm btn-primary">
                    <i class="bi bi-plus-circle me-1"></i>Agregar Ciudad
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <form method="POST">
                <table class="table table-hover table-striped align-middle">
                    <thead class="table-light">
                        <tr>
                            <th scope="col">País</th>
                            <th scope="col">Ciudad</th>
                            <th scope="col" class="text-end">Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach($ciudadesPorPais as $pais => $ciudadesDelPais): ?>
                            <tr class="table-secondary">
                                <td colspan="3"><strong><?= htmlspecialchars(getPaisName($pais)) ?></strong> (<?= count($ciudadesDelPais) ?> ciudades)</td>
                            </tr>
                            <?php foreach($ciudadesDelPais as $ciudad): ?>
                                <tr>
                                    <td></td>
                                    <td><?= htmlspecialchars($ciudad->nombre) ?></td>
                                    <td class="text-end">
                                        <button type="submit" name="delete" value="<?= $ciudad->id ?>" class="btn btn-sm btn-outline-danger">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                </form>
            </div>
        </div>
    </div>
</div>