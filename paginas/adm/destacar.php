<?php
 
    $id = app\uri(1);
    $anuncio = $pdo->query("SELECT * FROM anuncios WHERE id=$id")->fetch();
    $fotos = $pdo->query( "SELECT * FROM fotos WHERE id_anuncio=$anuncio->id ORDER BY pos ASC")->fetchAll(); // Crea OBJ con los datos para listar en html
    $recuperarEventos= "SELECT * FROM eventos WHERE id_anuncio=$anuncio->id AND estado=1 AND tipo='destacado' ORDER BY accion ASC";
    $eventos= $pdo->query($recuperarEventos)->fetchAll();
  
?>

<div class="destacar" style="max-width: 700px; margin: 0 auto">

    <h4>Destacado anuncio id: <?=$id?></h4>

    <form action='/anuncios/&fun=anuncioDestacar()' method="post"> 

    <input type="hidden" name="id" value="<?=$id?>">
    
        <div class="mb-3">
            <label for="dias" class="form-label">Número de días:</label>
            <input type="text" class="form-control" name="dias" id="dias" placeholder="Escribe los días a destacar">
        </div>

        <div class="mb-3">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" name="super" value="true" id="super">
                <label class="form-check-label" for="super">
                    Super destacado
                </label>
            </div>
        </div>

        <div class="mb-3">
            <label for="fecha" class="form-label">Fecha de publicación (Seleccione hoy para publicar ahora)</label>
            <input type="datetime-local" class="form-control" name="fecha" id="fecha">
        </div>

        <input type="submit" class="btn btn-success" value="Aceptar">
        <a href="/anuncios" class="btn btn-danger">Cancelar</a>

        
    </form>

</div>