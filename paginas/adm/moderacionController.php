<?php

 
function aprobarfoto($opt = []){
    $id = $opt['id'];
    $sql = "UPDATE fotos SET estado = 1 WHERE id = $id";
    flex()->query($sql);
    //echo "<script>pushMessage('Foto aprobada')</script>";
};



function denegarfoto($opt = []){
    $id = $opt['id'];
    $sql = "UPDATE fotos SET estado = 3 WHERE id = $id";
    flex()->query($sql);
    return ["sql"=>$sql, "data"=>$_POST]; 
}

function aprobarVideo($opt = []){
    $id = $opt['id'];
    $sql = "UPDATE anuncios SET videoAprobado=1 WHERE id=$id";
    flex()->query($sql); 
}

function denegarVideo($opt = []){
    $id = $opt['id'];
    $sql = "UPDATE anuncios SET videoAprobado=3 WHERE id=$id";
    flex()->query($sql); 
}
    