
<?php
$paises = flex()->fetchAll("SELECT name as label, alpha_2 as value FROM paises");
$paisesJS = json_encode($paises);


if (isset($_POST['id_pais'])){
    $pais = $_POST['id_pais'];
    $ciudad = $_POST['ciudad'];
    // Comprobar que no existe ya la ciudad
    if(empty(flex()->fetchAll("SELECT * FROM ciudades WHERE nombre = '"  . $ciudad . "' AND pais = '" . $pais . "'"))){
     
        flex()->query("INSERT INTO ciudades (pais, nombre) VALUES ('$pais', '$ciudad')");
       
        pushMessage("Ciudad agregada correctamente", "success");
    }else{
        pushMessage("La ciudad ya existe", "error");
    }
    flex()->redirect("/adm/ciudades");
}


?>

<br><br>
        <div class="card shadow-sm mb-4 mx-auto" style="max-width: 500px;">
    <div class="card-header text-white">
        <h3 class="mb-0">Agregar una ciudad a un País</h3>
    </div>
    <div class="card-body">
        <form action="" method="post">
            <div class="mb-3">
                <label for="ciudad" class="form-label">Ciudad:</label>
                <input type="text" class="form-control" id="ciudad" name="ciudad" placeholder="Escribe la ciudad..." required>
            </div>
            <div class="mb-3">
                <label for="pais" class="form-label">País o nacionalidad:</label>
                <input type="text" class="form-control" id="pais" name="pais" placeholder="Escribe tu país..." autocomplete="off" required>
                <input type="hidden" id="pais_value" name="id_pais" value="">
            </div>
            <div class="mt-4 text-center">
                <button type="submit" class="btn btn-primary px-4">
                    <i class="bi bi-plus-circle me-2"></i>Agregar 
                </button>
            </div>
        </form>
    </div>
</div>

<script>

<!-- PAISES -->
 
    const paises = <?= $paisesJS ?>;
    const field = document.getElementById('pais');
    let isClicked = false

    const ac = new Autocomplete(field, {
        data: [{
            label: "I'm a label",
            value: 42
        }],
        maximumItems: 5,
        threshold: 1,
        onSelectItem: ({
            label,
            value
        }) => {
            $pais_value = document.getElementById("pais_value");
            $pais_value.setAttribute("value", value);

        }
    });

    // perder foco con click en body
    ac.setData(paises)

    document.getElementById("pais").focus()

    field.addEventListener("blur", () => {
        esPaisValido = paises.some((element) => element.label === field.value);
        if (!esPaisValido) {
            field.value = ""
            $pais_value = document.getElementById("pais_value");
            $pais_value.setAttribute("value", '');
        }

        //isClicked=false;

    })

 
</script>

<style>
    .form-switch {
        transform: scale(.9);
        flex-direction: column;
        margin-top: 1px;
        padding: 0px 7px;
    }

    .form-check-label {
        padding-bottom: 15px;
    }

    .bordergrey {
        border: solid 1px #d9cdcd;
        padding: 10px 15px;
        border-radius: 3px;

    }

    .form-switch-horizonal {
        flex-direction: row;
    }

    .form-check-label-horizonal {
        padding-bottom: 2px !important;
    }

    main.admin {
        padding-top: 0px;
    }
</style>
 