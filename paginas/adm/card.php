<div class='col-sm-12 col-md-6 col-lg-4 col-xl-4 col-xxl-3'>

    <?php

    /* -------- Modales ---------- */
    include app\part('modalExtender');
    include app\part('modalPublicar');  
    include app\part('modalCaducar');
    include app\part('modalNumeroFotosyVideo');
    include app\part('posicionarDelante');
    
    $portadasSQL = json_decode($anuncio->portadas, true);
    @$portadasAnuncio=$portadasSQL['anuncio'];
    @$portadasDestacado=$portadasSQL['destacado'];
    $portadas= $anuncio->estado_destacado==1&&isset($portadasDestacado[0])?$portadasDestacado:$portadasAnuncio;

    $portadasJSON= str_replace('"',"'",json_encode($portadas));
    $portada = isset($portadas[0]) ? "/" . app\Config::$uploads . "/fotos/" . $portadas[0] : "/" . app\Config::$files . "/vacia.jpg";
    /* Email y tel de usuario */

    $usuario = getfun("getUsuario(\"id\":$anuncio->id_usuario)")["data"];

    ?>

    <div class="card-anuncio px-2 py-2 m-2 mb-3" style="min-width: 315px; background: white; border-radius: 2px; box-shadow: 0px 2px 2px 0px rgba(0,0,0,0.14),0px 2px 1px -1px rgba(0,0,0,0.12),0px 1px 3px 0px rgba(0,0,0,0.2);">

    <div class="card-header d-flex justify-content-between">
        
<p><?= "$anuncio->nombre (id: $anuncio->id)" ?> <?= $anuncio->estado_destacado ? "(Destacado)" : '' ?></p>
<button type="button" class="btn btn-success dropdown-toggle btn-sm mb-2" data-bs-toggle="dropdown" aria-expanded="false"> Editar </button>
<ul class="dropdown-menu">
    <?php if ($estado == 0) : // Caducados ?>
    
        <li><a class="dropdown-item" href="" data-bs-toggle="modal" data-bs-target="#modalPublicar<?= $anuncio->id ?>">Publicar</a></li>
        <li><a class='dropdown-item' href='<?=$uri?>&fun=anuncioEliminar("id":<?=$anuncio->id?>)'>Eliminar</a></li>
    <?php endif // fin caducados ?> 

    <?php if ($estado == 1 or $estado == 3 or $estado == 4) : ?>
        <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#modalCaducar<?= $anuncio->id ?>">Caducar</a></li>
        <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#modalExtender<?= $anuncio->id ?>">Extender días</a></li>
        <?php if ($anuncio->estado_destacado) : ?>
            <li><button class="dropdown-item" onclick="destacadoEliminar(<?= $anuncio->id ?>)">Eliminar destacado</button></li>
        <?php endif ?>
        <li><a class='dropdown-item' href='<?=$uri?>&fun=anuncioSubir("id":<?=$anuncio->id?>)'>Subir anuncio</a></li>
    <?php endif ?>

    <?php if ($estado == 2) : // anuncios programados  ?>
        <li><a class='dropdown-item' href='<?=$uri?>&fun=anuncio_desprogramar("id":<?=$anuncio->id?>)'>Desprogramar anuncio</a></li>
    <?php endif ?>

    <?php if ($estado == 3) : // destacados programados  ?>
        <li><a class='dropdown-item' href='<?=$uri?>&fun=destacado_desprogramar("id":<?=$anuncio->id?>)'>Desprogramar destacado</a></li>
    <?php endif ?>

    <?php if ($estado != 3 or $estado != 4) : ?>
        <li><a class="dropdown-item" href="/adm/destacar/<?= $anuncio->id ?>/">Destacar</a></li>
    <?php endif ?>

    <li><a class="dropdown-item bg-warning bg-opacity-10" href="#" data-bs-toggle="modal" data-bs-target="#modalNumeroFotosyVideo<?= $anuncio->id ?>">Numero de fotos y video</a></li>
    <li><a class="dropdown-item" href="/adm/anuncio/<?= $anuncio->id ?>/">Editar anuncio</a></li>
    <li><a class="dropdown-item" href="/adm/opciones/<?= $anuncio->id ?>/">Opciones</a></li>
    <li><a class="dropdown-item" href="/adm/horario/<?= $anuncio->id ?>/">Horario</a></li>
    <li><a class="dropdown-item" href="/adm/mapa/<?= $anuncio->id ?>/">Mapa</a></li>
    <li><a class="dropdown-item" href="/adm/multimedia/<?= $anuncio->id ?>/">Fotos y videos</a></li>
    <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#posicionarDelante<?= $anuncio->id ?>">Posicionar delante de este a</a></li>
    <li><a class="dropdown-item bg-warning bg-opacity-10" href="/adm/usuario/<?= $anuncio->id ?>/"> <?= $anuncio->id_usuario == 1 ? 'Asignar usuario' : 'Cambiar usuario' ?></a></li> 
    <div class="dropdown-divider"></div>
    <li><a class="dropdown-item bg-warning bg-opacity-10" href="/adm/alcance/<?= $anuncio->id ?>/">Alcance</a></li>
</ul>
    </div>


        <a href="<?= "/anuncio/$anuncio->id" ?>" target="_blank">

        <img data-active-image="0" data-images="<?= $portadasJSON ?>" style="object-fit:contain; width: 200px; height: 200px" class="img-fluid mx-auto d-block mb-1 imgAnuncio" src="<?= $portada ?>" at="">
        </a>
        <br>Publicación: <?= $anuncio->fecha_publicacion ?><hr>
        Usuario: <?=$usuario->email?><hr>
        <strong>Nota admin :</strong> <?= $anuncio->notas_admin ?> <hr>
        <?php if ($estado == 3) : ?> <strong>Destacado programado:</strong> <?= $anuncio->fecha_destacar ?> <hr> <?php endif?>
             Fecha posicionado: <?= $anuncio->fecha_posicionado ?><hr> 
            <!-- QUEDAN CADICADO -->
            <?php if ($estado == 0) : ?>
                <strong>Caducó:</strong> <?= fechaSimple($anuncio->fecha_caducidad) ?><hr>
                <strong>Quedan: </strong> <?= seconds2human($anuncio->duracion) ?> <a class="btn btn-danger btn-sm" href='&fun=anuncio_reset_duracion("id":<?=$anuncio->id?>)'>Reset</a>

            <?php endif ?>
            <?php if ($estado == 2) : ?>
                <strong>Caduca:</strong> <?= fechaSimple($anuncio->fecha_caducidad) ?><hr>
                Quedan: <?= seconds2human($anuncio->duracion) ?>
            <?php endif ?>
            <?php if ($estado == 1) : ?>

                <strong>Caduca:</strong> <?= fechaSimple($anuncio->fecha_caducidad) ?><hr>
                Quedan: <?= seconds2human((strtotime($anuncio->fecha_caducidad) - strtotime(date("Y-m-d H:i:s")))) ?>
            <?php endif ?>
 


        <!-- QUEDAN PROGRAMADO -->


        <!-- QUEDAN PUBLICADOS -->


       
    </div> <!-- card-anuncio -->
</div> <!-- col-md-12 col-lg-6 col-xl-4 col-xxl-3 -->