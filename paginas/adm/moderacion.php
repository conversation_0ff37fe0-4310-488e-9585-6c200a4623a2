<?php

  $fotos = flex()->fetchAll("SELECT * FROM fotos WHERE estado = 0");

  // recupera los id sin repetir que tengan fotos en estado por moderar
    $ids_anuncios = flex()->fetchAll("SELECT DISTINCT id_anuncio FROM fotos WHERE estado = 0");

    $anuncios = [];
    foreach ($ids_anuncios as $id){
        $anu = getAnuncio(["id"=>$id->id_anuncio])["data"];
        $anuncios[] = $anu;
    }
 

?>

<div class="row mt-4">
    <div class="col-12 text-center">
        <h3>Moderación</h3>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12 text-center">
        <h4>Fotos</h4>
    </div>
</div>

<div class="row">

    <?php foreach ($anuncios as $anuncio):?>
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <?php $usuario = getUsuario(["id"=>$anuncio->id_usuario])["data"]?>
                    <p class="card-title"> Anuncio: <?=$anuncio->nombre?> (id:<?=$anuncio->id?>) <br> Anunciante: <?= $usuario->email?> (id:<?=$usuario->id?>) </p>
                </div>
                <div class="row p-3">
                    <?php
                    $fotos = flex()->fetchAll("SELECT * FROM fotos WHERE estado = 0 AND id_anuncio = $anuncio->id");
                    foreach ($fotos as $foto):?>
                            <?php $foto_url = "http://". $_SERVER['SERVER_NAME']."/uploads/fotos/$foto->filename"?>
                            <div class="col-md-6 col-lg-3 card-body mx-auto" id="card<?=$foto->id?>" style="max-width: 250px">
                                <div class="card">
                                    <div class="card-header text-center">
                                        <a type="button" href="<?=$foto_url?>" class="btn btn-outline-dark"><i class="fa-solid fa-magnifying-glass"></i> Ampliar</a>
                                    </div>
                                    <div class="card-body">
                                        <div class="imagen">
                                            <a href="<?=$foto_url?>"><img style="height: 160px; object-fit: contain" src="<?=$foto_url?>" class="card-img-top">
                                            </a>
                                        </div>
                                    </div>
                                    <?php
                                        $msg = "Le avisamos que la foto $foto_url ha sido denegada por no cumplir con las normas de la web, eliminela en su panel y si lo desea suba otra en su lugar";
                                    ?>
                                    <div class="card-footer text-center">
                                        <div class="form-check form-switch mt-2 mx-auto" style="width: 150px" >
                                            <label class="form-check-label me-3" for="foto">Aprobar </label>
                                            <input hx-post="<?=flex()->path?>/&fun[]=aprobarfoto(id:<?=$foto->id?>)" hx-swap="delete" hx-target="#card<?=$foto->id?>" class="form-check-input" type="checkbox" name="foto">
                                        </div>
                                        <hr>
                                        <button type="button" hx-post="<?=flex()->path?>/&fun[]=denegarfoto(id:<?=$foto->id?>)" hx-swap="delete" hx-target="#card<?=$foto->id?>" class="mt-2 mb-2 btn btn-danger">Denegar</button>
                                        <p>Avisos: </p>
                                        <a type="button" class="btn btn-success" href="https://wa.me/601418518?text=<?=$msg?>" target="_blank"><i class="fa-brands fa-whatsapp"></i></i> Usuario</a>
                                        <a type="button" class="mt-2 btn btn-success" href="https://wa.me/601418518?text=<?=$msg?>" target="_blank"><i class="fa-brands fa-whatsapp"></i></i> Anuncio</a>

                                    </div>
                                </div>
                            </div>


                    <?php endforeach ?>

                </div> <!-- row -->
            </div> <!-- card name -->
        </div> <!-- col-12 -->
    <?php endforeach ?>
</div>


<div class="row mt-4">
    <div class="col-12 text-center">
        <h4>Videos</h4>
    </div>
</div>


<?php 

// Anuncios con videos por moderar
$anuncios = flex()->fetchAll("SELECT * FROM anuncios WHERE videoContratado = 1 AND videoAprobado = 0");

?> 
     
<div class="row">

    <?php foreach ($anuncios as $anuncio):?>
        <?php 
            $video_url = '/uploads/videos/' . $anuncio->id . ".mp4";
            // Comprobar si el video existe
            if(!videoExiste($anuncio->id)) continue; 
            ?>
            <div class="col-sm-12 col-md-6 col-lg-4" id="card<?=$anuncio->id?>">
                <div class="card">
                    <div class="card-header text-center"> 
                        <p class="card-title"> Anuncio: <?=$anuncio->nombre?> (id:<?=$anuncio->id?>) <br> Anunciante: <?= $usuario->email?> (id:<?=$usuario->id?>) </p>
                    </div> 
                    <div class="card-body text-center">
                        <div class="video">
                            <video controls style="" src="<?=$video_url?>" class="card-img-top"></video> 
                        </div>
                    </div>
                    <div class="card-footer text-center">
                        <div class="form-check form-switch mt-2 mx-auto" style="width: 150px" >
                            <label class="form-check-label me-3" for="foto">Aprobar </label>
                            <input hx-post="&fun[]=aprobarVideo(id:<?=$anuncio->id?>)&x&action" hx-swap="delete" hx-target="#card<?=$anuncio->id?>" class="form-check-input" type="checkbox" name="foto">
                        </div>
                        <hr>
                        <button type="button" hx-post="&fun[]=denegarVideo(id:<?=$anuncio->id?>)&x&action" hx-swap="delete" hx-target="#card<?=$anuncio->id?>" class="mt-2 mb-2 btn btn-danger">Denegar</button>
                        <p>Avisos: </p>
                        <div class="d-flex justify-content-center">
                            <div class="d-flex flex-column">
                                <a type="button" class="btn btn-success" href="https://wa.me/601418518?text=<?=$msg?>" target="_blank"><i class="fa-brands fa-whatsapp"></i></i> Usuario</a>
                                <a type="button" class="mt-2 btn btn-success" href="https://wa.me/601418518?text=<?=$msg?>" target="_blank"><i class="fa-brands fa-whatsapp"></i></i> Anuncio</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    <?php endforeach ?> 

</div> <!-- row --> 
                      

