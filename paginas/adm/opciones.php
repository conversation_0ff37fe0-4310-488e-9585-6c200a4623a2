<?php
$id = app\uri(2);
$anuncio = getfun("getAnuncio(\"id\":$id)")["data"];



?>

<span class="m-3">

</span>

<div class="row justify-content-center">
    <div class="col-12 col-md-6">
        <form method="POST" action='/adm/x/&fun=anuncioOpciones("id":<?=$id?>)'> 

            <div class="mb-3">
                <label class="form-label">Fotos</label>
                <input type="text" class="form-control" name="fotos" value="<?= $anuncio->fotos ?>">
            </div>
  
            <div class="mb-3">
            <label for="titulo" class="form-label">Notas admin: </label>
            <input type="text" name="notas_admin" id="titulo" class="form-control" placeholder="Notas administración" value="<?= @$anuncio->notas_admin ?? '' ?>">

             </div> 
            
            <a href="/adm/anuncios/" class="btn btn-danger">Cancelar y salir</a>
            <button type="submit" class="btn btn-success">Guardar y continuar</button>
        </form>
    </div>

</div>