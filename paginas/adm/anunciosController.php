
<?php
function filtroAdm($opt = []){


$orderbyList = [
    "fecha_posicionado" => "Posición fecha",
    "nombre" => "Nombre",
    "fecha_publicacion" => "Fecha de publicación",
    "fecha_caducidad" => "Fecha de caducidad"
];

$categoriaList = ["Sin filtros", "Chicas", "Chicos", "Transgénero", "Alojamientos"];

$ciudadList = ["Todas", "Navarra", "Logroño", "Bilbao"];

$orderList = ["desc" => "Descendente", "asc" => "Ascendente"];

$estadoList = ["Caducados", "Publicados", "Programados", "Destacado programado", "Destacados"];

/* Default filter adm */
if ( !isset($_SESSION['filtrosAdmSQL']) )
{
        $filtrosAdmSQL = [
        "categoria" => 0,
        "ciudad" => 0,
        "estado" => 1,
        "limit" => 20,
        "orderby" => "fecha_posicionado",
        "order" => "desc"
    ]; 

    $_SESSION['filtrosAdmSQL'] = $filtrosAdmSQL; 

     /* Names */ 
     foreach ($filtrosAdmSQL as $key => $value){ 
        $filtrosAdmName[$key] = isset( ${$key."List"} ) ? ${$key."List"}[$value] : $value;
    } 

    $_SESSION['filtrosAdmName'] = $filtrosAdmName; 
}

// update
if ( count($opt) >= 1 ) {
     // Recupera valores actuales
     $filtrosAdmSQL = $_SESSION['filtrosAdmSQL'];
     $filtrosAdmName = $_SESSION['filtrosAdmName'];
     // por cada opción actualiza los valores y nombres
    foreach ($opt as $key => $value){
        $filtrosAdmSQL[$key] = $value;
        $filtrosAdmName[$key] = isset( ${$key."List"} ) ? ${$key."List"}[$value] : $value;
        //if (isset( ${$key."List"}) ) $filtrosAdmName[$key] =  ${$key."List"}[$value];
    } 
    
    $_SESSION['filtrosAdmName'] = $filtrosAdmName; 
    $_SESSION['filtrosAdmSQL'] = $filtrosAdmSQL; 
}
 

//  if(count($opt) >=1) header("Location: ". app\redirect(app\uri()));
return [$_SESSION['filtrosAdmSQL'], $_SESSION['filtrosAdmName']];

//   

};


if (!isset($_SESSION['filtrosAdmSQL'])) filtroAdm();




function getAnunciosAdm($opt = []){
 
    global $pageN;
    $estado = $_SESSION['filtrosAdmSQL']['estado'];
    $id_ciudad = $_SESSION['filtrosAdmSQL']['ciudad'];
    $categoriaNumero = $_SESSION['filtrosAdmSQL']['categoria']; 
    $anuncio_orderby = $_SESSION['filtrosAdmSQL']['orderby'];
    $anuncioOrder =  $_SESSION['filtrosAdmSQL']['order'];
    $pageSize = $_SESSION['filtrosAdmSQL']['limit'];
    // Limite de paginación
    
    $pageN = isset($_GET['page']) ? $_GET['page'] : 1; 
    $offset = ($pageN - 1) * $pageSize;

   
    $tabla = 'anuncios';
    
switch ($estado) {
    case 0:
        $sql_estado_anuncio = "estado_anuncio = 0 ";
        break;
    case 1:
        $sql_estado_anuncio = "estado_anuncio = 1 ";
        break;
    case 2:
        $sql_estado_anuncio = "estado_anuncio = 2";
        break;
    case 3:
        $tabla= 'admin_destacados_programados';
        $sql_estado_anuncio = "estado_destacado = 2";
        break;
    case 4:
        $sql_estado_anuncio = "estado_destacado = 1 AND estado_anuncio=1";
        break;
}

$sql_categoria =  $categoriaNumero ? " AND id_categoria=$categoriaNumero" : '';
$sql_ciudad = $id_ciudad ? " AND id_ciudad=$id_ciudad" : '';

if (isset($_GET['page'])){

    $sql_pagination = " limit ". $_SESSION['filtrosAdmSQL']['limit'] . " OFFSET " . $offset; 

}else{

    $sql_pagination = " limit ". $_SESSION['filtrosAdmSQL']['limit']; 
}



    

    global $pdo;

    if (isset($_GET['buscar'])){
        $buscar = $_GET['buscar'];
     
        if (!preg_match('/^[0-9]+$/', $buscar)){ // Nombre
            $sql = "SELECT * FROM anuncios WHERE nombre LIKE '%$buscar%' ORDER BY $anuncio_orderby $anuncioOrder"; 
    
        }else{ //TELEFONO O ID
            $buscar = str_replace(' ', '', $buscar);
            $sql = "SELECT * FROM anuncios WHERE id=$buscar OR telefono='$buscar' OR telefono2='$buscar' ORDER BY $anuncio_orderby $anuncioOrder"; 
        }  
        //app\debug($sql);
        
    } else {  
        $sql = "SELECT * FROM $tabla WHERE $sql_estado_anuncio $sql_categoria $sql_ciudad ORDER BY $anuncio_orderby $anuncioOrder $sql_pagination";
        
    }
    $anuncios = $pdo->query($sql)->fetchAll();
    
    return ["sql"=>$sql,"data"=>$anuncios];

}

function anuncio_desprogramar($opt = []){
    global $pdo;
    $id = $opt['id'];

    // Eliminar eventos del anuncio
    $sql_eliminar_eventos = "DELETE from eventos WHERE id_anuncio = $id AND tipo = 'anuncio' ";

    // Cambiar estado del anuncio a caducado
    $sql_cambiar_estado = "UPDATE anuncios SET estado_anuncio = 0 WHERE id = $id";

    // Ejecución db
    $pdo->query($sql_eliminar_eventos);
    $pdo->query($sql_cambiar_estado);

    // Cambiar filtro a vista caducados
    getfun('filtroAdm("estado":0)');
 
    // retorno
    return [$sql_eliminar_eventos, $sql_cambiar_estado];
   
 }

 function destacado_desprogramar($opt = []){
    global $pdo;
    $id = $opt['id'];

    // Eliminar eventos del anuncio
    $sql_eliminar_eventos = "DELETE from eventos WHERE id_anuncio = $id AND tipo = 'destacado' ";

    // Ejecución db
    $pdo->query($sql_eliminar_eventos);
 
    // retorno
    return $sql_eliminar_eventos;
   
 }

 
function anuncio_reset_duracion($opt = []){
    global $pdo;
    $id = $opt['id'];
    $sql = "UPDATE anuncios SET duracion = 0 WHERE id = $id";
    $pdo->query($sql);
    return $sql;
  
}

 function anuncioDestacar($opt = false){ 
    global $pdo;
    $id = isset($_POST['id']) ? $_POST['id'] : $opt['id'];

    $hoy = date('Y-m-d H:i:s');
    $dias = $_POST['dias'];
    $super= isset($_POST['super'])?1:0;
    $fecha_destacado = date_format(date_create($_POST['fecha']), 'Y-m-d H:i:s');

    $fecha_quitar_destacado = addTimeToTimestamp($fecha_destacado, $dias, 'days');
    $sqlBorrarEventos = "DELETE FROM eventos WHERE id_anuncio=$id AND tipo='destacado' AND estado=1";
    $pdo->query($sqlBorrarEventos);
    // ----------- PUBLICAR AHORA
    if (strtotime($hoy) >= strtotime($fecha_destacado)) {
        $sql = "UPDATE anuncios SET estado_destacado=1,destacado_tipo=$super WHERE id=$id"; 
    // ----------- DESTACAR PROGRAMADO
    } else {
        $sql = "UPDATE anuncios SET destacado_tipo=$super, estado_destacado=2 WHERE id=$id";
       
        $sqlEvento = "INSERT INTO eventos VALUES(0,$id,'$hoy','$fecha_destacado','destacado','publicar',true)";
        $pdo->query($sqlEvento);
    }
    $pdo->query($sql);
    $sqlEvento = "INSERT INTO eventos VALUES(0,$id,'$hoy','$fecha_quitar_destacado','destacado','despublicar',true)";
    $pdo->query($sqlEvento);
    pushMessage("Anuncio $id destacado");
  //  app\redirect('/anuncios');
     
    return [$sql, $sqlEvento];
 }

 

/* Llamado po rel modal modalRenovar o programar*/

function anuncioPublicar(){ 
 
    $fecha_caducidad_actual = date_format(date_create($_POST['fecha_caducidad']), 'Y-m-d H:i:s');

    $id = $_POST['id'];
    
    $hoy = date('Y-m-d H:i:s');
 
   // duracion: tiempo de duracion en segundos
         // $duracion = $_POST['duracion'] ? $_POST['duracion'] : $dias_a_renovar * 24 * 3600;
  
    // Anuncio sin tiempo
    if( isset($_POST['dias_a_renovar']) ){
        $dias_en_segundos =  $_POST['dias_a_renovar'] > 0 ? $_POST['dias_a_renovar'] * 24 * 3600 : 0;
        $minutos_en_segundos = $_POST['minutos_a_renovar'] > 0 ? $_POST['minutos_a_renovar'] * 60 : 0;
        $horas_en_segundos = $_POST['horas_a_renovar'] > 0  ? $_POST['horas_a_renovar'] * 3600 : 0;
        $duracion = $dias_en_segundos + $minutos_en_segundos +  $horas_en_segundos; 
    }else{
        $duracion = $_POST['duracion'];
    }
    

    $diasDeCaducidad = strtotime($hoy) - strtotime($fecha_caducidad_actual);

    $diasDeCaducidad = $diasDeCaducidad / 3600 / 24;
   
    $fecha_publicacion = $_POST['fecha'];
    $fecha_publicacion=strtotime($fecha_publicacion); 
    $fecha_publicacion= date('Y-m-d H:i:s', $fecha_publicacion);

    //$diasDeCaducidad/3600.0/24.0>1
    if ($diasDeCaducidad > 6) {
        //crearPrimeraPosicion($id, $id_ciudad);
        $fecha_posicionado = $hoy;
        $sql_fecha_posicionado= ", fecha_posicionado = '$hoy'";
        $msg = "renovado tras mas de 7 ($diasDeCaducidad) días";
    } else {
        $descuento = strtotime($fecha_caducidad_actual) - strtotime($fecha_publicacion);
        $fecha_posicionado = addTimeToTimestamp($hoy, -$descuento, 'seconds');
        $sql_fecha_posicionado= "";
        $msg = "renovado menos de 7 ($diasDeCaducidad) dias";
        // insertarAnuncioEnPosicion($id, $nuevaPos, $id_ciudad);
        // if fecha_posicionado = Lunes 17h, pero se caduca martes 20h y se renueva en miercoles 10am 
        // fecha posicionado =miercoles-10am (fecha actual ) - fecha_se_caduco( martes 20h) = cantidad de horas caducado = 14h
        // fecha posicionado = fecha_posicionado + 14h
    }
 
    // ------------------ PROGRAMADOS
    if (strtotime($fecha_publicacion) > strtotime($hoy)) {
        $fecha_caducidad = addTimeToTimestamp($fecha_publicacion, $duracion, "seconds" );   
        $sqlEvento = "INSERT INTO eventos VALUES(0,$id,'$hoy','$fecha_publicacion','anuncio','publicar',true)";
        flex()->query($sqlEvento); 
        $sql = "UPDATE anuncios SET estado_anuncio=2, fecha_caducidad='$fecha_caducidad' $sql_fecha_posicionado, duracion=$duracion WHERE id=$id";

    // ------------------ PUBLICACIÓN INMEDIATA
    }else{  
        $fecha_caducidad = addTimeToTimestamp($hoy, $duracion, "seconds"); 
        $sql = "UPDATE anuncios SET estado_anuncio=1, fecha_caducidad='$fecha_caducidad' $sql_fecha_posicionado, duracion=$duracion WHERE id=$id";
    }
    $sqlEvento = "INSERT INTO eventos VALUES(0,$id,'$hoy','$fecha_caducidad','anuncio','despublicar',true)";
   
   flex()->query($sqlEvento);
   flex()->query($sql);
 
   pushMessage("Anuncio $id Renovado");

    // Cambiar filtro a vista publicados
    getfun('filtroAdm("estado":1)');

    return ["sql"=>$sql, "sqlEvento"=>$sqlEvento, "msg"=>$msg, "post"=>$_POST]; 

}



 
// /adm/x/&fun=getAnunciosFromUsuario("id":50)

function getAnunciosFromUsuario($opt = []){
    if (flex()->user->rol < 5) die("No tienes permisos para ejecutar esta funcion"); 

    global $pdo; 
    $usuario = $opt['id']; 
    if ($usuario == 1){
        echo "Administrador";
        return false;
    }

    $estado = isset($_POST['estado']) ? $_POST['estado'] : 1; 
    $sql = "SELECT nombre, id FROM anuncios WHERE id_usuario=$usuario AND estado_anuncio=$estado order by nombre";
    $anuncios = $pdo->query($sql)->fetchAll(); 
    $sql2 = "SELECT id FROM anuncios WHERE id_usuario=$usuario";
    $total = $pdo->query($sql2)->fetchAll();
    $total = count($total);
    
    if($anuncios):?>

        <div class="anunciosfromusuario"> 
        TOTAL: <?=$total?> PUBLICADOS: <?= count($anuncios)?>;
 
            <?php foreach($anuncios as $anuncio): ?> 
                    <div class="anuncio">
                    <a href="/anuncio/<?=$anuncio->id?>"><?=$anuncio->nombre?> </a>
                    <span><?=$anuncio->id?></span> 
                    </div>
            <?php endforeach?> 

        </div>

        <style>
                .anunciosfromusuario{
                    font-size: .9rem;
                }

                .anunciosfromusuario .anuncio{
                    display: inline-block;
                    position: relative;
                }
 
                .anunciosfromusuario a{
                    margin: 7px 6px; 
                    line-height: 12px;
                    display:inline-block; 
                    border-radius: 5px; 
                    padding: 5px 8px; 
                    color: white; 
                    text-decoration: none; 
                    background-color: green;
                }

                .anunciosfromusuario span{
                    background-color: black;
                    color: white;
                    padding: 0px 3px;
                    font-size: .8rem;
                    position: absolute;
                    top: -6px;
                    border-radius: 5px;
                    right: -8px;
                    min-width: 20px;
                    text-align: center;
                }
            
            </style>

    <?php else:?>

       <p>El usuario no tiene anuncios;</p>

    <?php endif?>

    <?php 
    //return ["sql"=>$sql,"data"=>$datos];
 
}   

 // Usado en adm/anuncios modal modalNumeroFotosyVideo
function establecerNumeroFotosAndVideo($opt = []){  
    $id = $opt['id'];
    $videoContratado = isset($_POST['videocontratado'])  ? 1 : 0;
    $numeroFotos = $_POST['fotos'];
    $sql = "UPDATE anuncios SET fotos = $numeroFotos, videoContratado=$videoContratado WHERE id = $id"; 
    flex()->query($sql);  
    // Descomentar para depuración en POSTMAN
    // var_dump($_POST); 

    // Retorna el sql y el data para debug EN LA PESTAÑA DE DEBUG
    return ["sql"=>$sql, "data"=>$_POST]; 
}


