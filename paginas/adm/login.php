<?php 
 
    $_SESSION['login-error'] = isset($_SESSION['login-error']) ? $_SESSION['login-error'] : false;
 

      // Estas enviado el form con el password
      if(isset($_POST['password'])){

          // forzar redirección a admin si ya tienes la sesión admin auth
          
            msgbar("se envio datos");
            $password = $_POST['password'];
            $user = $_POST['user'];
          
            if ($password == 'bremen@1381' & $user == '<EMAIL>'){

              flex()->user = getfun("getUsuario(email:$user)")["data"];

                $_SESSION['adm'] = true;
                $_SESSION['user'] = flex()->user;

                
                $_SESSION['login-error'] = false;
                msgbar("Constraseña correcta");
                flex()->redirect("/adm/anuncios");
            }else{
                pushMessage("Contraseña o usuario incorrectos", 'error');
                $_SESSION['login-error'] = "Contraseña o usuario incorrectos";
                flex()->redirect("/adm/login");
                msgbar("password incorrecto");
            }

        }  
   
          ?> 
          
          <div class="vh-100 d-flex justify-content-center align-items-center">
            <div class="container">
              <div class="row d-flex justify-content-center">
                <div class="col-12 col-md-8 col-lg-6">
                  <div class="border border-3 border-danger"></div>
                  <div class="card bg-white">
                    <div class="card-body p-5">

                      <form class="mb-3 mt-md-4" action="" method="POST">
                        <h2 class="mb-2 text-uppercase">Citas</h2>
                        <h2 class=" mb-5">Acceso a la administración</h2>

                        <?php if($_SESSION['login-error']): ?>
                              <div class="alert alert-danger" role="alert"> 
                              <?=$_SESSION['login-error']?>

                              </div>
                        <?php endif ?>


                        <div class="mb-3">
                          <label for="user" class="form-label ">Usuario</label>
                          <input type="text" class="form-control" name="user" id="user" placeholder="">
                        </div>
                        <div class="mb-3">
                          <label for="password" class="form-label ">Contraseña</label>
                          <input type="password" class="form-control" name="password" id="password" placeholder="*******">
                        </div>
                        
                        <div class="d-grid">
                          <button class="btn btn-danger" type="submit">Entrar</button>
                        </div>
                      </form>
                    

                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <style>

          .main{
              background-color: black !important;
          }

          .card{
                  border-radius: 0px 0px 5px 5px;
          }

          h2{
              color: black;
              text-align: center;
          }

          @media (min-width: 1200px){

              .p-xl-4 {
                  padding: 0px !important;
              }
          }

          </style>

 <?php 

$_SESSION['login-error'] = false;

?>