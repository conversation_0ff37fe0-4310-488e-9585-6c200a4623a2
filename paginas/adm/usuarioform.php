<?php
$idusuario = app\uri(2);
$usuario = getfun("getUsuario(id:$idusuario)")["data"];

?>

<span class="m-3">

</span>

<form method="POST" action='/adm/usuarios/&fun=usuarioEdit(id:<?=$idusuario?>)'> 
<div class="row justify-content-center">
    <div class="col-12 col-md-5"> 

            <div class="mb-3">
                <label class="form-label">Email</label>
                <input type="text" class="form-control" name="email" value="<?= $usuario->email ?>">
            </div>
            <div class="row">

            <div class="col-6 mb-3">
                <label class="form-label">Teléfono</label>
                <input type="text" class="form-control" name="tel" value="<?= $usuario->tel ?>">
            </div>


                <div class="col-6 mb-3">
                <label class="form-label">Rol</label>
                <input type="number" class="form-control" name="rol" value="<?= $usuario->rol ?>">
            </div>


            </div>
          

            <a href="/adm/usuarios/" class="btn btn-danger">Cancelar y salir</a>
            <button type="submit" class="btn btn-success">Guardar y continuar</button>
       
    </div> 
</div>

</form>