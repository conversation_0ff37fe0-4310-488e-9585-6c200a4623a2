<?php

$id = flex()->uri(3);
 
 
// para el autocompletado de paises
$paises = flex()->fetchAll("SELECT name as label, alpha_2 as value FROM paises");
$paisesJS = json_encode($paises);

function getPaisName($alpha_2){
    return flex()->fetch("SELECT name FROM paises WHERE alpha_2 = '" . $alpha_2 . "'")->name;
}

function getCiudades(){
    return flex()->fetchAll("SELECT * FROM ciudades ORDER BY pais, nombre"); 
}
$anuncio = getfun("getAnuncio(\"id\":$id)")["data"];

// Obtener la lista de ciudades de la tabla ciudades
$ciudades = getCiudades();

// Agrupar ciudades por pais
$ciudades_por_pais = [];
foreach($ciudades as $ciudad) {
    if(!isset($ciudades_por_pais[$ciudad->pais])) {
        $ciudades_por_pais[$ciudad->pais] = [];
    }
    $ciudades_por_pais[$ciudad->pais][] = $ciudad;
}

// Obtener las ciudades asociadas al anuncio actual
$ciudades_anuncio = flex()->fetchAll("SELECT c.* FROM ciudades c 
                                   INNER JOIN anuncio_ciudades ac ON c.id = ac.id_ciudad 
                                   WHERE ac.id_anuncio = $id
                                   ORDER BY c.pais, c.nombre");

// Crear un array con los IDs de las ciudades asociadas para facilitar la comprobación
$ciudades_seleccionadas = [];
foreach($ciudades_anuncio as $ciudad) {
    $ciudades_seleccionadas[] = $ciudad->id;
}

// Agrupar ciudades seleccionadas por pais
$ciudades_anuncio_por_pais = [];
foreach($ciudades_anuncio as $ciudad) {
    if(!isset($ciudades_anuncio_por_pais[$ciudad->pais])) {
        $ciudades_anuncio_por_pais[$ciudad->pais] = [];
    }
    $ciudades_anuncio_por_pais[$ciudad->pais][] = $ciudad;
}
// PARA MOSTRAR EL PAIS EN EL SELECT
$pais = flex()->fetch("SELECT name FROM paises WHERE alpha_2 = '" . $anuncio->alcance_pais . "'")->name; 


// ---------------- CAMBIO DE ALCANCE -------------------

if(isset($_POST['alcance'])){
    $alcance = $_POST['alcance'];
    // update anuncio.alcance
    flex()->query("UPDATE anuncios SET alcance = '" . $alcance . "' WHERE id = " . $id);
    flex()->redirect(); 
}

// -------------------- AGREGAR CIUDADES ------------------------
if (isset($_POST['ciudades'])) {  
    
    // Manejar las ciudades seleccionadas
    // Primero eliminar todas las relaciones existentes
    flex()->query("DELETE FROM anuncio_ciudades WHERE id_anuncio = $id");
    
    // Luego añadir las nuevas relaciones si se seleccionaron ciudades
    if(isset($_POST['ciudades']) && is_array($_POST['ciudades'])) {
        foreach($_POST['ciudades'] as $id_ciudad) {
            flex()->query("INSERT INTO anuncio_ciudades (id_anuncio, id_ciudad) VALUES ($id, $id_ciudad)");
        }
    }
    flex()->redirect(); 
 
}

if (isset($_POST['id_pais'])) { 
    $id_pais = $_POST['id_pais'];
    // update anuncio.alcance 
   flex()->query( "UPDATE anuncios SET alcance_pais = '" . $id_pais . "' WHERE id = " . $id); // UPDATE anuncios SET alcance_pais = 'es' WHERE id = 827
   flex()->redirect();
}

  
?>
<br>
<h3>Alcance de anuncio <?= $id ?></h3>
<br>


<form action="" method="post">
        <div class="row">
            <div class="col-6">
                <label for="alcance" class="form-label">Alcance</label>
                <select name="alcance" id="alcance" class="form-select mb-2">
                    <option <?= $anuncio->alcance == 'ciudad' ? 'selected' : '' ?> value="ciudad">Ciudad</option>
                    <option <?= $anuncio->alcance == 'pais' ? 'selected' : '' ?> value="pais">Pais</option>
                    <option <?= $anuncio->alcance == 'global' ? 'selected' : '' ?> value="global">Global</option>
                </select>
    
            </div>
        </div> 
        <button type="submit" class="btn btn-primary">Guardar</button>
    </form>
<br>
 

<?php if($anuncio->alcance == 'ciudad' ): ?>

    <!-- formulario bootstrap 5 seleccion de los 3 alcances,enum('ciudad', 'pais', 'global', '')\t -->
    <form action="" method="post">
        <div class="row">
            <div class="col-6">
                
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Ciudades disponibles</h5>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        <?php foreach ($ciudades_por_pais as $pais => $ciudades_pais) : ?>
                            <div class="mb-3">
                                <h6 class="border-bottom pb-2"><?= $pais ?></h6>
                                <div class="row ms-2">
                                    <?php foreach ($ciudades_pais as $ciudad) : ?>
                                        <div class="col-md-6 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" 
                                                    name="ciudades[]" 
                                                    id="ciudad_<?= $ciudad->id ?>" 
                                                    value="<?= $ciudad->id ?>" 
                                                    <?= in_array($ciudad->id, $ciudades_seleccionadas) ? 'checked' : '' ?>>
                                                <label class="form-check-label" for="ciudad_<?= $ciudad->id ?>">
                                                    <?= $ciudad->nombre ?>
                                                </label>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-6">
                <div class="card mt-5">
                    <div class="card-header">
                        <h5 class="mb-0">Ciudades seleccionadas</h5>
                    </div>
                    <div class="card-body">
                        <?php if (count($ciudades_anuncio) > 0) : ?>
                            <?php foreach ($ciudades_anuncio_por_pais as $pais => $ciudades_pais) : ?>
                                <h6 class="border-bottom pb-2"><?= $pais ?></h6>
                                <ul class="list-group mb-3">
                                    <?php foreach ($ciudades_pais as $ciudad) : ?>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <?= $ciudad->nombre ?>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            <?php endforeach; ?>
                        <?php else : ?>
                            <p class="text-muted">No hay ciudades seleccionadas</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div> 
        
        <button type="submit" class="btn btn-primary">Guardar</button>
    </form>
<?php endif; ?>


 
<?php if($anuncio->alcance == 'pais' ): ?>

        <form action="" method="post">
        <div class="row">
            <div class="col-6"> 
                <div class="mb-3">
                    <label for="pais" class="form-label">País o nacionalidad:</label>
                    <input type="text" class="form-control" id="pais" name="pais" placeholder="Escribe tu país..." autocomplete="off">
                    <input type="hidden" id="pais_value" name="id_pais" value="">
                </div> 
            </div>
        </div> 
        <button type="submit" class="btn btn-primary">Guardar</button>
        </form>

        


        <script>

        <!-- PAISES -->
        
            const paises = <?= $paisesJS ?>;
            const field = document.getElementById('pais');
            let isClicked = false
            field.value = "<?= $anuncio->alcance_pais ? $pais: '' ?>";
  
            const ac = new Autocomplete(field, {
                data: [{
                    label: "I'm a label",
                    value: 42
                }],
                maximumItems: 5,
                threshold: 1,
                onSelectItem: ({
                    label,
                    value
                }) => {
                    $pais_value = document.getElementById("pais_value");
                    $pais_value.setAttribute("value", value);

                }
            });

            // perder foco con click en body
            ac.setData(paises)

          //  document.getElementById("pais").focus()

            field.addEventListener("blur", () => {
                esPaisValido = paises.some((element) => element.label === field.value);
                if (!esPaisValido) {
                    field.value = ""
                    $pais_value = document.getElementById("pais_value");
                    $pais_value.setAttribute("value", '');
                }

                //isClicked=false;

            })

        
        </script>

        <style>
            .form-switch {
                transform: scale(.9);
                flex-direction: column;
                margin-top: 1px;
                padding: 0px 7px;
            }

            .form-check-label {
                padding-bottom: 15px;
            }

            .bordergrey {
                border: solid 1px #d9cdcd;
                padding: 10px 15px;
                border-radius: 3px;

            }

            .form-switch-horizonal {
                flex-direction: row;
            }

            .form-check-label-horizonal {
                padding-bottom: 2px !important;
            }

            main.admin {
                padding-top: 0px;
            }
        </style>

<?php endif; ?>