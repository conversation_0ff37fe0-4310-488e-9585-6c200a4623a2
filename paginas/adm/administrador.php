
<?php 




// Nombres de las carpetas a comprimir
$folders = ['folder-1','folder-2'];

// Archivos o Carpetas que serán ignoradas
$ignoreFiles = ['node_modules/', 'src/', '.idea/', '.git/'];

// Folder donde se guardaran los archivos ZIP
$destFolder = 'zips';

?>

<style>

h2{
    color: black; 
}
</style>

<h2>
Administración y seguridad 2
</h2>
<form action='<?=$uri?>&fun=modomantenimiento()' method='POST'>
<div class="row justify-content-start mb-3">
                    <div class="col">
                        <div class="form-check form-switch">
                            <label class="form-check-label me-4" for="destacado"> Modo mantenimiento </label>
                            
                            <?php $checked = file_exists("mantenimiento_1.txt") ? "checked='checked'" : '' ?>

                            <input class="form-check-input" <?= $checked ?> onchange="submit()" type="checkbox" name="modomantenimiento">
                        </div>
                    </div>
 </div> <!-- destacar foto-->    
</form>
 
<p class="mt-4">Copias disponibles: </p>
<span>Última: </span>
    <?php 
 $fecha_backup = trim( file_get_contents(__DIR__."/../mantenimiento/fecha.txt") ); 
 $fecha_backup = date_create_from_format("Y-m-d_H_i-s", $fecha_backup); 
 echo date_format($fecha_backup, "Y-m-d (H:i)") ;
  
?>
<a href="/mantenimiento/inicio#preview" type="button" class="btn btn-secondary btn-sm ms-2"> Ver</a>
<a href="/mantenimiento.tar.gz" type="button" class="btn btn-success btn-sm ms-2"> Descargar</a>

   
<?php
 
 $backups = glob(__DIR__."/../mantenimiento_backups/*");
 $carpetas = []; 
 foreach($backups as $backup){

    if (is_dir($backup) ){
        $carpetas[] = basename($backup);
    } 
}

foreach ($carpetas as $carpeta): ?>

<p class="mt-3">
<?=$carpeta?>.tar.gz  <a href="/mantenimiento_backups/<?=$carpeta?>.tar.gz" type="button" class="btn btn-primary btn-sm ms-2"> Descargar</a>

<a href='/mantenimiento_backups/<?=$carpeta?>/inicio#preview' type='button' class='btn btn-success btn-sm ms-2'> ver</a>
</p>

<?php endforeach ?>


<?php

 