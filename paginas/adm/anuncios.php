<?php

/* -------------------------- ANUNCIO LISTADO  ( /anuncios ) -------------------*/

$diaSemana = date('w');

$hora = date('H:i:s');

/* Usado en cards */
$meses = ["Ene", "Feb", "Mar", "Abr", "May", "Jun", "Jul", "Ago", "Sep", "Oct", "Nov", "Dic"];

$destacados = isset($_GET['destacados']);

$diaSemana = $diaSemana == 0 ? 6 : $diaSemana - 1;
 
$categoriaNumero = $_SESSION['filtrosAdmSQL']['categoria'];
$anuncio_categoria_nombre = $_SESSION['filtrosAdmName']['categoria'];

$anuncio_orderby = $_SESSION['filtrosAdmSQL']['orderby'];
$anuncioOrder =  $_SESSION['filtrosAdmSQL']['order'];
$orderbyName =  $_SESSION['filtrosAdmName']['orderby'];
$orderName =  $_SESSION['filtrosAdmName']['order'];

// estado
$anuncio_estado_nombre = $_SESSION['filtrosAdmName']['estado'];
$estado = $_SESSION['filtrosAdmSQL']['estado'];

//ciudad
$id_ciudad = $_SESSION['filtrosAdmSQL']['ciudad'];
$ciudad = $_SESSION['filtrosAdmName']['ciudad'];

$pageSize =  $_SESSION['filtrosAdmSQL']['limit'];    
$pageN = 1;
/*
   CREATE VIEW admin_destacados_programados AS
    WITH eventos_destacar AS(
        SELECT id_anuncio, fecha_ejecucion as fecha_destacar FROM eventos WHERE tipo='destacado' AND accion='publicar' AND estado=true
    )
    SELECT anuncios.*,eventos_destacar.fecha_destacar FROM eventos_destacar INNER JOIN anuncios
    ON anuncios.id=eventos_destacar.id_anuncio
    WHERE anuncios.estado_destacado=2

    
 */

 if ( isset($_GET['buscar']) ){ 
     $buscar = $_GET['buscar'];
     $anuncios = getfun("getAnunciosAdm(\"buscar\":$buscar)")['data']; 
 }else{
    $anuncios = getfun("getAnunciosAdm()")['data'];  
 }

// HACER UN JOIN DE LAS FOTOS CON LOS ANUNCIOS

/*
if (isset($_GET['search'])) {
    $value = $_GET['search'];
 
    if (ctype_alpha($value)){
        msgbar('Es un string de texto');
        $sql = "SELECT * FROM $tabla WHERE nombre LIKE '%$value%' AND $sql_estado_anuncio ORDER BY $anuncio_orderby $anuncioOrder $sql_pagination";

    }else{
        $value = str_replace(' ', '', $value);
        $sql = "SELECT * FROM $tabla WHERE id='$value' OR telefono='$value' OR telefono2='$value' ORDER BY $anuncio_orderby $anuncioOrder $sql_pagination";

    }  
    //app\debug($sql);
    $anuncios = $pdo->query($sql)->fetchAll();
} else { 
    $sql_ciudad = $id_ciudad ? "AND id_ciudad=$id_ciudad" : '';
    $sql = "SELECT * FROM $tabla WHERE $sql_estado_anuncio $sql_categoria $sql_ciudad ORDER BY $anuncio_orderby $anuncioOrder $sql_pagination";
   // logsql($sql);
    $anuncios = $pdo->query($sql)->fetchAll(); // Crea OBJ con los datos para listar en html

}
// app\debug($anuncios)
 */
?>
 
<!-- --------------------------------- PILLS FILTROS SUBNAV ----------------------------------------------------- !-->

<div class="col-12 mb-2" style="margin-top: 50px">
 
    <div class="dropdown" style="display: inline-block">
        <span> Categoría: </span>
        <button class="btn btn-primary dropdown-toggle" type="button" id="submenuAnunciosCategorias" data-bs-toggle="dropdown" aria-expanded="false">
            <?= $anuncio_categoria_nombre ?>
            <!-- sesion categoria -->
        </button>

        <ul class="dropdown-menu" aria-labelledby="submenuAnunciosCategorias">
            <li><a class="dropdown-item" href='<?=$uri?>&fun=filtroAdm("categoria":0)'>Todas</a></li>
            <li><a class="dropdown-item" href='<?=$uri?>&fun=filtroAdm("categoria":1)'>Chicas</a></li>
            <li><a class="dropdown-item" href='<?=$uri?>&fun=filtroAdm("categoria":2)'>Chicos</a></li>
            <li><a class="dropdown-item" href='<?=$uri?>&fun=filtroAdm("categoria":3)'>Travestis</a></li>
            <li><a class="dropdown-item" href='<?=$uri?>&fun=filtroAdm("categoria":4)'>Alojamientos</a></li>
        </ul>
    </div>

    <div class="dropdown" style="display: inline-block">
        <span> Orden Por: </span>
        <button class="btn btn-primary dropdown-toggle" type="button" id="submenuAnunciosListado" data-bs-toggle="dropdown" aria-expanded="false">

            <?= "$orderbyName ($anuncioOrder)"?>
        </button>

        <ul class="dropdown-menu" aria-labelledby="submenuAnunciosListado">
            <li><a class="dropdown-item" href='<?=$uri?>&fun=filtroAdm("orderby":"fecha_posicionado","order":"desc")'>Posición</a></li>
            <li><a class="dropdown-item" href='<?=$uri?>&fun=filtroAdm("orderby":"fecha_publicacion","order":"desc")'>Fecha de publicación</a></li> 
            <li><a class="dropdown-item" href='<?=$uri?>&fun=filtroAdm("orderby":"fecha_caducidad","order":"asc")'>Caducan próximamente</a></li>  
            <li><a class="dropdown-item" href='<?=$uri?>&fun=filtroAdm("orderby":"nombre","order":"asc")'>Nombre</a></li>
        </ul>
    </div>
 

    <div class="dropdown" style="display: inline-block">
        <span>Estado:</span>
        <button class="btn btn-primary dropdown-toggle" type="button" id="submenuAnunciosListado" data-bs-toggle="dropdown" aria-expanded="false">
            <?= $anuncio_estado_nombre ?>
        </button>

        <ul class="dropdown-menu" aria-labelledby="submenuAnunciosListado">
            <li><a class="dropdown-item" href='<?=$uri?>&fun=filtroAdm("estado":1)'>Publicados</a></li>
            <li><a class="dropdown-item" href='<?=$uri?>&fun=filtroAdm("estado":2)'>Programados</a></li>
            <li><a class="dropdown-item" href='<?=$uri?>&fun=filtroAdm("estado":0)'>Caducados</a></li>
            <li><a class="dropdown-item" href='<?=$uri?>&fun=filtroAdm("estado":4)'>Destacados</a></li>
            <li><a class="dropdown-item" href='<?=$uri?>&fun=filtroAdm("estado":3)'>Destacado programado</a></li>
        </ul>
    </div>

    <div class="dropdown" style="display: inline-block">
        <span> Límite: </span>
        <button class="btn btn-primary dropdown-toggle" type="button" id="menuLimite" data-bs-toggle="dropdown" aria-expanded="false">
            <?= $pageSize ?>
        </button>

        <ul class="dropdown-menu" aria-labelledby="menuLimite">
            <li><a class="dropdown-item" href='<?=$uri?>&fun=filtroAdm("limit":10)'>10</a></li>
            <li><a class="dropdown-item" href='<?=$uri?>&fun=filtroAdm("limit":20)'>20</a></li>
            <li><a class="dropdown-item" href='<?=$uri?>&fun=filtroAdm("limit":50)'>50</a></li> 
        </ul>
    </div>
  <!--
    <div class="dropdown" style="display: inline-block">
        <span> Ciudad: </span>
        <button class="btn btn-primary dropdown-toggle" type="button" id="menuLimite" data-bs-toggle="dropdown" aria-expanded="false">
            <?= $ciudad ?>
        </button>

        <ul class="dropdown-menu" aria-labelledby="menuLimite">
            <li><a class="dropdown-item" href='<?=$uri?>&fun=filtroAdm("ciudad":0)'>Todas</a></li>
            <li><a class="dropdown-item" href='<?=$uri?>&fun=filtroAdm("ciudad":1)'>Navarra</a></li>
            <li><a class="dropdown-item" href='<?=$uri?>&fun=filtroAdm("ciudad":2)'>Logroño</a></li>
            <li><a class="dropdown-item" href='<?=$uri?>&fun=filtroAdm("ciudad":3)'>Bilbao</a></li>
        </ul>
    </div>
-->

    <?php if (isset($_GET['buscar'])) : ?>
        <span>Busqueda: </span>
        <a href="<?=app\uri()?>">
            <button class="btn btn-outline-primary"><?= $_GET['buscar'] ?><span style="margin-left: 15px" class="btn-close" aria-label="Close"></span></button>
        </a>
    <?php endif ?>

</div> <!-- Pills -->
<!-- ------------ FIN  PILLS FILTROS SUBNAV  ------------------>


<div class="row justify-content-center">
    <div class="col-12 col-lg-4 m-2">
        
            <!-- Form buscador -->
            <form action="&fun=buscar()" method="POST" class="d-flex">
        
        <input class="form-control me-2" type="search" placeholder="Busca por Nombre, id o tel" aria-label="Search" value="<?=isset($_GET['buscar']) ? $_GET['buscar']: ''?>" name="buscar">
       <?php if(isset($_GET['buscar'])): ?>
       <a href="<?=app\uri()?>"><button class="btn btn-success" type="button">X</button></a> 
       <?php else: ?>
        <button class="btn btn-success" type="submit">Buscar</button>
       <?php endif ?>
         
     </form>
     <!-- Fin form buscador -->
    </div>
</div>

<div class="row">
    <!-- card anuncio -->

    <?php foreach ($anuncios as $anuncio) : ?>

        <?php include app\part('card'); ?>

    <?php endforeach ?>
</div> <!-- row -->
<p style="margin-top: 20px; text-align: center"> Página <?= $pageN ?>  </p>
<div class="d-flex justify-content-center">

       
    <?php if ($pageN != 1) : ?>
        <a href="<?= "$uri&page=" . $pageN - 1 ?>" class="btn btn-primary m-3">Atras</a>
    <?php endif // pagesize = 30 lastpage != pagesize 
    ?>
    <?php if ($pageSize == count($anuncios)) : ?>
        <a href="<?= "$uri&page=" . $pageN + 1 ?>" class="btn btn-primary m-3">Siguiente</a>
    <?php endif ?>
</div>

 

<script>
   

    async function destacadoEliminar(id) {
      
        try {
            const response = await fetch(`<?=$uri?>&api&fun=destacado_eliminar("id":${id})`, {
                method: "GET",
            })
            const json = await response.json();
            console.log(json);
        } catch (err) {
            console.log(err);
        }
        location.reload();
    }

    function alternarFotos() {
        $imgAnuncios = Array.from(document.querySelectorAll(".imgAnuncio"));

        $imgAnuncios.forEach(
            ($imgAnuncio) => {
                $src = $imgAnuncio.getAttribute("src");

                $fotos = JSON.parse($imgAnuncio.getAttribute("data-images").replace(/'/g, '"'));
                $activeIndex = parseInt($imgAnuncio.getAttribute("data-active-image"));
                $activeFoto = $fotos[$activeIndex];
                let $newIndex = ($activeIndex >= $fotos.length - 1) ? 0 : $activeIndex + 1;
                $newSrc = $src.replace($activeFoto, $fotos[$newIndex]);

                //console.log($src, $newSrc);
                $imgAnuncio.setAttribute("src", $newSrc);
                $imgAnuncio.setAttribute("data-active-image", $newIndex);

            }
        )
    }


    
    setInterval(
        () => {
            alternarFotos();
        }, 1000
    )
</script>