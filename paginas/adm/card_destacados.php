<div class='col-md-12 col-lg-6 col-xl-4 col-xxl-3'>

    <?php
    /* -------- <PERSON>dales ---------- */
    include app\part('modalExtender'); 
    include app\part('modalDestacar');
    include app\part('modalRenovar');
    include app\part('modalCaducar');
    ?>

    <div class="card-anuncio p-3 m-2"
        style="background: white; border-radius: 2px; box-shadow: 0px 2px 2px 0px rgba(0,0,0,0.14),0px 2px 1px -1px rgba(0,0,0,0.12),0px 1px 3px 0px rgba(0,0,0,0.2);">
        <p><?="$anuncio->nombre (id: $anuncio->id)"?></p>
        <a href="<?="/anuncio/$anuncio->id"?>">
         <img class="img-fluid mx-auto d-block mb-1" src="https://via.placeholder.com/300">
         </a>
        <p>Publicación: <?=$anuncio->fecha_publicacion ?><br>
     
        <!-- QUEDAN CADICADO -->
        <?php if( $_SESSION['estado'] == 0) : ?>
        <strong>Caducó:</strong> <?= fechaSimple($anuncio->fecha_caducidad) ?><br> 
        Quedan: <?=seconds2human( $anuncio->duracion) ?></p>
        <?php endif ?> 

         <!-- QUEDAN PROGRAMADO -->
        <?php if( $_SESSION['estado'] == 2) : ?>
        <strong>Caduca:</strong> <?= fechaSimple($anuncio->fecha_caducidad) ?><br> 
        Quedan: <?=seconds2human( $anuncio->duracion) ?></p>
        <?php endif ?> 

        <!-- QUEDAN PUBLICADOS -->

        <?php if( $_SESSION['estado'] == 1) : ?>
       
        <strong>Caduca:</strong> <?= fechaSimple($anuncio->fecha_caducidad) ?><br> 
        Quedan: <?=seconds2human( (strtotime($anuncio->fecha_caducidad)-strtotime(date("Y-m-d H:i:s"))) )?></p>
        <form action='/anuncios&destacados' method='POST' style='display: inline-block'>
            <div class="form-group">
                <input type='hidden' value='<?=$anuncio->id?>' name='id' /> 
                <input type='hidden' value='<?=$anuncio->id_ciudad?>' name='id_ciudad' />
                <input type='submit' class='btn btn-primary' value='Mover' />

                
            </div> <!-- form-group -->
        </form>
        <?php endif ?>
      
        <a class="btn btn-success" href="#" data-bs-toggle="modal" data-bs-target="#modalDestacar<?= $anuncio->id ?>">Editar destacado</a> 
        
    </div> <!-- card-anuncio -->
</div> <!-- col-md-12 col-lg-6 col-xl-4 col-xxl-3 -->