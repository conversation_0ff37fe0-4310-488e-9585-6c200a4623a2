
<?php

$id = app\uri(2);


    $fotos = getfun("getFotos(\"id\":$id)");

    $anuncio = getfun("getAnuncio(\"id\":$id)")["data"];

?>

    
        
   
    <div class="row justify-content-center ">

        <!-- AGREGAR FOTOS -->
        <div class="col-11 col-lg-4 metabox m-2 pt-3 pb-2">
            <form action='<?=$uri?>&fun=setFotos()' enctype='multipart/form-data' method='POST'>
                <input type="hidden"  name="id" value="<?=$id?>">
                <label for="fotos" class="form-label">Fotos:  (<?=count($fotos)?>) encontradas</label>
                <input type="file" class="form-control" name="fotos[]" id="fotos" multiple>
                <!-- Borrar todas las fotos -->
                <div class="row mt-3">

                    <div class="col-6">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-success d-block">Subir fotos</button>
                        </div>
                    </div>
                    <div class="col-6">  
                        <a href='&fun=fotosBorrar("id":<?=$id?>)' class="btn btn-danger d-block <?= count($fotos) ? '': 'd-none' ?>">Borrar todas</a>  
                    </div> 

                </div> <!-- row -->  
            </form>  
        </div>  <!-- col --> 
        
        <!-- AGREGAR VIDEO -->
        <div class="col-11 col-lg-4 metabox pt-3 m-2" style="position: relative">
            <form action='<?=$uri?>&fun=videoSubir("id":<?=$id ?>)' enctype='multipart/form-data' method='POST'>

                <div style="position: absolute; right: 20px; top: 5px">
                    <div class="form-check form-switch">
                        <label class="form-check-label me-4" for="video-check"> <?=@$anuncio->hayVideo ? 'Activado' : 'Desactivado'?> </label>
                        <input onchange="activarVideo(<?=$id?>)" class="form-check-input" type="checkbox" id="video-check" name="hayVideo"
                            <?=@$anuncio->hayVideo ? 'checked' : ''?>>
                    </div>
                </div>

            <label for="video" class="form-label"><?=videoExiste($id) ? 'Video: (1) encontrado' : 'Video: (0)'?></label>
            <input type="file" class="form-control" name="video" id="video">
            <!-- CHECKBOX ACTIVAR VIDEO -->
            <div class="row mt-3 mb-2 justify-content-between">
                <div class="col-6">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-success d-block"><?=videoExiste($id) ? 'Cambiar video' : 'Subir video'?></button>
                        </div>
                </div>
                 <div class="col-6"> 
                    <div class="d-grid gap-2">
                        <a href='<?=$uri?>&fun=borrarVideo("id":<?=$id?>)' class="btn btn-danger <?=videoExiste($id) ? '' : 'd-none' ?>">Borrar video</a>  
                    </div>
                </div>
               
                
            </div> <!-- ACTIVAR VIDEO --> 
        </form>    
        </div> <!-- col -->
  
    </div> <!-- row  -->
   
   
    
     
    

 
<!-- GALERIA DEF FOTOS DEL ANUNCIO -->
<div class="row mt-2 justify-content-center" style="max-width: 1440px; margin: 0 auto">
    <?php if(is_array($fotos)):

        foreach ($fotos as $foto) : ?> 
        <div class="col-3 text-center metabox m-2" style="width: 200px;"> 
        <div class="cabecera d-flex justify-content-between">
            <a style="text-decoration: none; padding: 1px 3px; color:  #1895f9; border-radius: 10px" href='<?=$uri?>&fun=subirFoto("id":<?=$foto->id?>,"pos":<?=$foto->pos?>,"id_anuncio":<?=$foto->id_anuncio?>)'><i class="bi bi-upload"></i></a>
            <span class=""><?= $foto->pos ?> </span>
            <a style="color: red; padding: 1px 3px; text-decoration: none; border-radius: 10px" href='<?=$uri?>&fun=borrarFoto("id":<?=$foto->id?>)'><i class="bi bi-x-lg"></i></a> 
        </div>
       

        <div class="content d-flex justify-content-center flex-column align-items-center" style="height: 150px; border: solid 1px #d5d5d5">
            <img style="max-width: 140px; max-height: 140px" src="/uploads/fotos/<?= $foto->filename ?>" alt="">  
        </div>
        
            

        <!--
                <input class="destacado" onchange='destacadoFoto(<?="$foto->id, $foto->id_anuncio"?>)' name="destacado" id="destacado<?= $foto->id ?>" <?= $foto->destacado ? 'checked' : '' ?> type="checkbox">
                <label for="destacado<?= $foto->id ?>">Destacado</label> <br>
        -->

        <!-- Destacar foto -->
        <div class="row justify-content-start align-items-end">
                    <div class="col">
                        <div class="form-check form-switch">
                            <label class="form-check-label me-4" for="destacado"> Destacar </label>
                            <input class="form-check-input" onchange='destacadoFoto(<?="$foto->id, $foto->id_anuncio"?>)' type="checkbox" id="destacado<?=$foto->id?>" name="destacado"
                                <?=@$foto->destacado ? 'checked' : ''?>>
                        </div>
                    </div>
        </div> <!-- destacar foto-->    
  
        </div><!-- col foto -->
        <?php endforeach;

    endif;
    ?>
 </div>


<script>
 
  
 async function destacadoFoto(id, id_anuncio) { 
    el = this.event.currentTarget;
    const set = this.event.currentTarget.checked ? 1 : 0;
    const data = new FormData(); 
    data.append('id', id);
    data.append('set', set); 
    console.log("destacadoFoto",id,set); 
    url = `<?=$uri?>&api&fun=destacadoFoto()`;
     try { 
         const response = await fetch(url, { 
            method: 'POST',
            body: data
        }) 
         const json = await response.json(); 
         if(response.status != 200){
            toast("Superado el limite de 2 fotos, deseleccione primero") 
            el.click();
         }


     } catch (err) {
         console.log(err);
        
         
     }
   // location.reload(); 
 }

 async function activarVideo(id) { 
    el = this.event.currentTarget;
    const set = el.checked ? 1 : 0;
    const data = new FormData(); 
    data.append('id', id);
    data.append('set', set); 
    console.log("activarvideo",id,set); 
    url = `<?=$uri?>&api&fun=activarVideo()`;
     try { 
         const response = await fetch(url, { 
            method: 'POST',
            body: data
        }) 
         const json = await response.json(); 
         if(response.status != 200){
            toast("Error") 
            el.click();
         }else{
            toast("Se aplico el cambio video")
         } 
     } catch (err) {
         console.log(err);
        
         
     }
   // location.reload(); 
 }
</script>