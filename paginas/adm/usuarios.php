<?php

$buscar = isset($_GET["buscar"]) ? $_GET["buscar"] : false;
if ($buscar) {
        $busqueda_tipo = str_contains($buscar, "@") ? "email" : "numerico";
        if($busqueda_tipo == "numerico"){
            $busqueda_tipo = strlen($buscar) >= 9 ? "tel" : "id";
        }
        flex()->debugbarInfo("busqueda tipo: $busqueda_tipo");
    $usuarios = getfun("buscarUsuario(valor:$buscar,campo:$busqueda_tipo)")["data"];
}else{
    $usuarios = getfun("getUsuariosConPublicados()")["data"];
}
?>
<br>
<div class="row">
    <div class="col-12 col-md-6 mx-auto mb-3 pt-3 pb-2" style="border: 1px solid #E3E9FF; box-shadow: 1px 1px 1px grey"> <!-- buscador -->
        <form action="/adm/usuarios" method="GET">
            <div class="row mb-3">
                <div class="col-9 d-flex align-items-center">
                    <input type="text" id="buscarUsuario" name="buscar" class="form-control" placeholder="Buscar usuario">

                </div>
                <div class="col-3 d-flex align-items-center">
                    <button class="btn btn-success" type="submit">Buscar</button>
                </div>
            </div>
        </form>

    </div> <!-- buscador -->
    <?php if ( $buscar ):?>
        <h3 style="color: grey; font-size: 1.1rem; text-align: center">Resultados de la busqueda por <?=$busqueda_tipo?>: <?=$buscar?> </h3>
    <?php else: ?>
        <h3 style="color: grey; font-size: 1.1rem; text-align:center">Mostrando usuarios con anuncios publicados</h3>
    <?php endif ?>
    <hr>
</div>

<div class="mt-2 row text-center">

    <?php foreach ($usuarios as $usuario) : ?>


        <div class="col-12 col-md-6 col-lg-4 mt-3">

            <!------------------------ ASIGNAR ANUNCIO MODAL -------------------------- !-->
            <form action="/adm/usuarios/&fun=usuarioAsignarAnuncio()" method="POST">
                <div class="modal face" id="modalAsignar<?= $usuario->id ?>" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Asignar anuncio a:<br> <?= "$usuario->email ( $usuario->id )" ?></h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">

                                <div class="mb-3">

                                    <label class="form-label">IDs de anuncio a asignar</label>
                                    <input type="text" name="anuncios" class="form-control" placeholder="ID de anuncio">

                                    <input type="hidden" name="id" value="<?= $usuario->id ?>">
                                </div>

                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                                <button type="submit" class="btn btn-primary">Guardar</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>

            <!-- -------------------------------------- FIN ASIGNAR MODAL ----------------------------->

            <div class="card">
                <div class="card-body" style="height: 230px">
                    <p class="card-title">
                    <div class="d-flex justify-content-between">
                        <div class="nombre mb-2">
                            <?= $usuario->email ?>
                        </div>
                        <div><i class="fa-brands fa-whatsapp"> </i> <?= $usuario->tel ?></div>
                        <div style="text-align: center; min-width: 20px; height: 20px; background-color: #e6e6e6; padding: 0 2px; border-radius: 5px; color: black"><?= $usuario->id ?></div>

                    </div>
                    <?php
                    $expired_time = addTimeToTimestamp($usuario->code_time, 10, "minutes");
                    $ahora = date_create(date('Y-m-d H:i'));
                    $expire = date_create($expired_time);
                    $diff_mins = round(abs($ahora->getTimestamp() - $expire->getTimestamp()) / 60);
                    $estado_code = $ahora > $expire ? "EXPIRADO" : "Expira en $diff_mins min";

                    //  $expired = round(abs($time - $expire) / 60,2). " minute";
                    //    $momentoactual = date_format(date_create( $time ),'h:m');
                    //  $diferencia = time() - strtotime($expired_time);
                    //   $expired = date_format(date_create(time()),'m');

                    ?>
                    <!-- <p class="card-text"> Código generado en: <?= $usuario->code_time ?></p>
                    <p class="card-text"> Expira en 10 min en: <?= $expired_time ?></p>
                    <p class="card-text"> El codigo actual esta: <?= $estado_code ?></p> -->



                    <div class="card-text tabs">

                        <!-- Nav tabs -->
                        <div class="nav nav-tabs" id="tabs<?= $usuario->id ?>" role="tablist">
                            <button class="nav-link active" id="home-tab" data-bs-toggle="tab" data-bs-target="#home-<?= $usuario->id ?>" type="button" role="tab" aria-controls="home" aria-selected="true"><i class="fa-solid fa-address-card fa-lg"></i></button>
                            <button hx-target='#anuncios<?= $usuario->id ?>' hx-post='/adm/x/&fun=getNumeroAnunciosFromUsuario("id":<?= $usuario->id ?>)&htmx' class='nav-link' id='anuncios-tab' data-bs-toggle='tab' data-bs-target='#anuncios-<?= $usuario->id ?>' type='button' role='tab' aria-controls='anuncios' aria-selected='false'><i class='fa-solid fa-rocket fa-lg'></i></button>
                            <button class="nav-link" id="saldo-tab" data-bs-toggle="tab" data-bs-target="#saldo-<?= $usuario->id ?>" type="button" role="tab" aria-controls="saldo" aria-selected="false"><i class="fa-solid fa-dollar-sign fa-lg"></i></button>
                            <button class="nav-link" id="notes-tab" data-bs-toggle="tab" data-bs-target="#notes-<?= $usuario->id ?>" type="button" role="tab" aria-controls="notes" aria-selected="false"><i class="fa-solid fa-file-lines fa-lg"></i></button>
                            <div class="dropdown">
                                <button class="nav-link btn btn-secondary text-center" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-expanded="false" style="--bs-btn-active-bg:none; --bs-btn-bg: none; --bs-btn-hover-bg:none; border: none">
                                    <i class="fa-solid fa-ellipsis-vertical fa-xl"></i>
                                </button>
                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                    <li><a class="dropdown-item" href="/adm/usuarioform/<?= $usuario->id ?>">Editar</a></li>
                                    <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#modalAsignar<?= $usuario->id ?>">Asignar anuncio</a></li>
                                </ul>
                            </div>


                        </div>

                        <div class="tab-content">

                            <!-- home content -->
                            <div class="tab-pane active" id="home-<?= $usuario->id ?>" role="tabpanel" aria-labelledby="home-tab" tabindex="0">
                                <p class="card-text pt-3">

                                    <span id="code<?= $usuario->id ?>"><?="CODIGO: <strong>$usuario->code </strong>"?>
                                         <a type='button' hx-swap="outerHTML" hx-target="#code<?=$usuario->id?>" hx-post="/adm/x/usuarios-resetcode/<?=$usuario->id?>" class='btn btn-outline-secondary ms-2' style="--bs-btn-padding-y: .25rem; --bs-btn-padding-x: .5rem; --bs-btn-font-size: .75rem;">
                                         <i class='fa-solid fa-rotate-right fa-sm'></i> ReGenerar
                                    </a>
                                        <br>
                                        <span style="line-height: 1.5rem;" hx-get='/adm/x/usuarios-codestatus/<?=$usuario->id?>' hx-trigger='load, every 5s' hx-swap='innerHTML'> </span></span>



                                </p>
                                <hr>
                                <p>Crear link de acceso: PENDIENTE</p>
                            </div>

                            <!-- Anuncios content  -->
                            <div class="tab-pane" id="anuncios-<?= $usuario->id ?>" role="tabpanel" aria-labelledby="profile-tab" tabindex="0">
                                <span id="anuncios<?= $usuario->id ?>">anuncios</span>
                            </div>

                            <!-- saldo content  -->
                            <div class="tab-pane" id="saldo-<?= $usuario->id ?>" role="tabpanel" aria-labelledby="profile-tab" tabindex="0">saldo

                            </div>
                            <!-- notes content  -->
                            <div class="tab-pane" id="notes-<?= $usuario->id ?>" role="tabpanel" aria-labelledby="messages-tab" tabindex="0" style="overflow-y:scroll; max-height: 120px">
                                Lorem, ipsum dolor sit amet consectetur adipisicing elit. Adipisci architecto dolore nam consequuntur modi amet alias saepe laudantium rerum maiores reiciendis neque, sunt et explicabo pariatur doloremque, rem distinctio quis ex deleniti! Possimus odit quod molestiae nobis, rem culpa fugiat, dicta sequi ratione alias a maxime suscipit pariatur minus voluptas blanditiis aliquam corrupti doloremque. Iusto sapiente necessitatibus voluptates repellat consectetur nisi dolorum, facilis deleniti atque cumque! Maxime deleniti rem quaerat porro mollitia magnam? Placeat recusandae itaque architecto fuga, perferendis molestiae beatae quae porro laudantium iste nesciunt. Quae obcaecati alias facere ex minima itaque reiciendis maxime magnam ad! Odit, deserunt dolorem?
                            </div>

                        </div>



                    </div>





                </div>
            </div>
        </div> <!-- col -->


    <?php endforeach ?>

</div> <!-- row -->

<!-- modal asignar anuncios -->

<style>
    .card {
        font-size: .8rem;
        --bs-card-spacer-y: 0.2rem;
        --bs-card-spacer-x: 0.5rem;
    }

    .card-title {
        display: none;
    }

    .card .nav-tabs .nav-link {
        border: 1px solid #dad6d6;
        border-bottom: none;
        color: grey;
        line-height: 1.2rem;
    }

    .nav-tabs .nav-link.active {
        color: blue;
    }
</style>