<?php

$id = app\uri(2);
$paises = $pdo->query("SELECT name as label, alpha_2 as value FROM paises")->fetchAll();
$paisesJS = json_encode($paises);
$categorias = $pdo->query("SELECT * FROM categorias ORDER BY id ASC")->fetchAll(); // Crea OBJ con los datos para listar en html



// $anuncio = fun("getAnuncio(\"id\":$id)");

$anuncio = getfun("getAnuncio(\"id\":$id)")["data"];
$servicios = explode(",", $anuncio->servicios);

$acompa = str_contains($anuncio->servicios, "Acompañamientos");
$masajes = str_contains($anuncio->servicios, "Masajes");
$modelo = str_contains($anuncio->servicios, "Modelo");

$notas = getfun("getUserNotes(\"id\":$id)")["data"];

// Usuario del anuncio
$usuario = $pdo->query("SELECT * from usuarios WHERE id = $anuncio->id_usuario")->fetch();
$anuncios_de_usuario = $pdo->query("SELECT id, nombre from anuncios WHERE id_usuario = $usuario->id AND estado_anuncio=1")->fetchAll(); 
$anuncios_de_usuario_caducados = $pdo->query("SELECT id, nombre from anuncios WHERE id_usuario = $usuario->id AND estado_anuncio!=1")->fetchAll(); 

?>
<form action='/anuncios/&fun=anuncioActualizar()' method='POST'>

    <div class="anuncioForm" style="max-width: 1440px; margin: 0 auto">

        <input type="hidden" name="id" value="<?= $id ?>">
        <div class="row justify-content-center p-2 p-sm-1"> <!-- nombre y pais -->

            <div class="col-12 col-lg-4 bordergrey me-lg-2">

                <div class="row">
                    <div class="col-8">
                        <label for="nombre" class="form-label">Nombre: </label>
                        <input type="text" name="nombre" id="nombre" class="form-control" placeholder="Nombre" value="<?= $anuncio->nombre ?? '' ?>">
                    </div> <!-- col -->
                    <div class="col-4">
                        <label for="edad" class="form-label">Edad: </label>
                        <input type="text" name="edad" id="edad" class="form-control" placeholder="edad" value="<?= $anuncio->edad ?? '' ?>">
                    </div> <!-- col -->
                </div> <!-- row -->

                <div class="row">
                    <div class="col-12 col-lg-8">
                        <label for="pais" class="form-label mt-2">País o nacionalidad: </label>
                        <input type="text" autofocus="false" class="form-control" id="pais" name="pais" placeholder="Escribe tu pais ..." autocomplete="off" aria-describedby="helpId" value="<?= $anuncio->pais ?? '' ?>">
                        <input type="hidden" id="pais_value" name="id_pais" value="<?= $anuncio->id_pais ?? '' ?>" />
                    </div> <!-- col -->

                    <div class="col-12 col-lg-4">
                        <!-- -----  CATEGORIA ---- -->
                        <label for="categoria" class="form-label mt-2">Categoria: </label>
                        <select class="form-select" name="id_categoria" id="categoria">
                            <?php foreach ($categorias as $categoria) : ?>
                                <!-- Edición recuperar categoria del actual anuncio -->
                                <?php if ($id) : ?>
                                    <option <?= $categoria->id == $anuncio->id_categoria ? 'selected' : '' ?> value="<?= $categoria->id ?>">
                                        <?= $categoria->nombre ?>
                                    <?php else : ?>
                                        <!-- Nuevo poner por defecto a chica -->
                                    <option <?= $categoria->nombre == 'chica' ? 'selected' : '' ?> value="<?= $categoria->id ?>">
                                        <?= $categoria->nombre ?>
                                    <?php endif ?>
                                    </option>
                                <?php endforeach ?>
                        </select>
                    </div> <!-- col -->
                </div> <!-- row -->

                <div class="row"> <!-- Pago y salidas -->
                    <div class="col-3">
                        <div class="form-check form-switch">
                            <label class="form-check-label" for="salidas">Salidas</label>
                            <input class="form-check-input" type="checkbox" id="salidas" name="salidas" <?= @$anuncio->salidas ? 'checked' : '' ?>>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="form-check form-switch">
                            <label class="form-check-label" for="visa">VISA</label>
                            <input class="form-check-input" type="checkbox" id="visa" name="visa" <?= @$anuncio->visa ? 'checked' : '' ?>>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="form-check form-switch">
                            <label class="form-check-label" for="bizum">Bizum</label>
                            <input class="form-check-input" type="checkbox" id="bizum" name="bizum" <?= @$anuncio->bizum ? 'checked' : '' ?>>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="form-check form-switch">
                            <label class="form-check-label" for="casa">Casa</label>
                            <input class="form-check-input" type="checkbox" id="casa" name="casa" <?= @$anuncio->casa ? 'checked' : '' ?>>
                        </div>
                    </div>
                </div>

            </div> <!-- col -->

            <div class="col-12 col-lg-4 telefonos bordergrey mt-2 mt-lg-0">
                <div class="row">
                    <div class="col-5">Teléfonos: </div>
                    <div class="col-6 text-center">Whastapp</div>
                    <hr class="mt-2">
                </div>
                <div class="row">
                    <div class="col-5">

                        <label for="telefono" class="form-label">Teléfono 1: </label>
                        <input type="tel" name="telefono" id="telefono" class="form-control" placeholder="Teléfono 1" value="<?= $anuncio->telefono ?? '' ?>">

                    </div>
                    <div class="col-6">
                        <div class="form-check form-switch">
                            <label class="form-check-label" for="hayWhatsapp1">Activar en tel 1</label>
                            <input class="form-check-input" type="checkbox" id="hayWhatsapp1" name="hayWhatsapp1" <?= @$anuncio->hayWhatsapp1 ? 'checked' : '' ?>>
                        </div>
                    </div>
                </div>

                <div class="row mt-2">
                    <div class="col-5">

                        <label for="telefono2" class="form-label">Teléfono 2: </label>
                        <input type="tel" name="telefono2" id="telefono2" class="form-control" placeholder="Telefono 2" value="<?= $anuncio->telefono2 ?? '' ?>">

                    </div>


                    <div class="col-6">
                        <div class="form-check form-switch">
                            <label class="form-check-label" for="hayWhatsapp2">Activar en tel 2</label>
                            <input class="form-check-input" type="checkbox" id="hayWhatsapp2" name="hayWhatsapp2" <?= @$anuncio->hayWhatsapp2 ? 'checked' : '' ?>>
                        </div>
                    </div>
                </div>

            </div> <!-- row titulo y texto -->

            <!--  titulo y texto -->
            <div class="col-12 col-lg-5 telefonos bordergrey mt-2 me-lg-2">
                <div class="row">
                    <div class="col-12 text-center">Detalles</div>
                    <hr class="mt-2">
                </div>
                <div class="row">
                    <div class="col-12">

                        <label for="titulo" class="form-label">Título del anuncio: </label>
                        <input type="text" name="titulo" id="titulo" class="form-control" placeholder="Título" value="<?= @$anuncio->titulo ?? '' ?>">

                    </div>

                </div>

                <div class="row mt-2">
                    <div class="col-12">
                        <label for="descripcion" class="form-label">Texto descriptivo: </label>
                        <textarea rows="3" name="descripcion" id="descripcion" class="form-control" placeholder="Descripción"><?= @$anuncio->descripcion ?? '' ?></textarea>

                    </div>

                </div>

                <!-- NOTAS -->
                <div class="row mt-2">
                    <div class="col-12">
                        <p>Notas: <a class="btn btn-secondary btn-outline btn-sm" href="/adm/notas&id=<?= $id ?>" role="button">Editar Notas</a> </p>

                        <?php if ($notas) : ?>

                            <?php foreach ($notas as $key => $value) : ?>

                                <div class="row justify-content-center mt-2">

                                    <?= $value["tipo"] . " - " . $value["texto"] ?>

                                </div>


                            <?php endforeach ?>

                        <?php else : ?>

                            <p class="text-center">No hay ninguna nota para este anuncio</p>

                        <?php endif ?>


                    </div>

                </div>
                <!-- fin notas -->

            </div> <!--  titulo y texto-->

            <div class="col-12 col-lg-3 telefonos bordergrey mt-2">
                <div class="row">
                    <div class="col-12 text-center">Servicios</div>
                    <hr class="mt-2">
                </div>
                <div class="row">
                    <div class="col-12 mb-3">
                        <div class="form-check form-switch" style="flex-direction: row; justify-content: space-between">
                            <label class="form-check-label" style="padding-bottom: 0px" for="acompa">Acompañamientos</label>
                            <input class="form-check-input mb-1" style="margin-left: 23px" type="checkbox" id="acompa" name="servicios[Acompañamientos]" <?= @$acompa ? 'checked' : '' ?>>
                        </div>
                    </div>
                    <hr>
                    <div class="col-12 mb-3">
                        <div class="form-check form-switch" style="flex-direction: row; justify-content: space-between">
                            <label class="form-check-label" style="padding-bottom: 0px" for="masajes">Masajes</label>
                            <input class="form-check-input mb-1" style="align-self: end" type="checkbox" id="masajes" name="servicios[Masajes]" <?= @$masajes ? 'checked' : '' ?>>
                        </div>
                    </div>
                    <hr>
                    <div class="col-12 mb-3">
                        <div class="form-check form-switch" style="flex-direction: row; justify-content: space-between">
                            <label class="form-check-label" style="padding-bottom: 0px" for="modelo">Modelo</label>
                            <input class="form-check-input mb-1" style="align-self: end" type="checkbox" id="modelo" name="servicios[Modelo]" <?= @$modelo ? 'checked' : '' ?>>
                        </div>
                    </div>
                    <hr>
                </div>


            </div> <!-- servicios -->


            <!-- super admin -->

            

          
        </div> <!-- anuncioForm -->

 


        <div class="row">
            <div class="d-grid gap-2 col-3 mx-auto">
                <input class="mt-3 btn btn-success" type="submit" value="Guardar">
            </div>
        </div><!-- row submit -->



    </div>


    <!-- PAISES -->
    <script>
        const paises = <?= $paisesJS ?>;
        const field = document.getElementById('pais');
        let isClicked = false

        const ac = new Autocomplete(field, {
            data: [{
                label: "I'm a label",
                value: 42
            }],
            maximumItems: 5,
            threshold: 1,
            onSelectItem: ({
                label,
                value
            }) => {
                $pais_value = document.getElementById("pais_value");
                $pais_value.setAttribute("value", value);

            }
        });

        // perder foco con click en body
        ac.setData(paises)

        document.getElementById("nombre").focus()

        field.addEventListener("blur", () => {
            esPaisValido = paises.some((element) => element.label === field.value);
            if (!esPaisValido) {
                field.value = ""
                $pais_value = document.getElementById("pais_value");
                $pais_value.setAttribute("value", '');
            }

            //isClicked=false;

        })
    </script>

    <style>
        .form-switch {
            transform: scale(.9);
            flex-direction: column;
            margin-top: 1px;
            padding: 0px 7px;
        }

        .form-switch .form-check-input {
    transform: scale(1.8);
    margin-right: -2px;
    margin-left: 3px;
    float: none;
    min-width: 2rem;
    margin-top: 0;
}

        .form-check-label {
            padding-bottom: 15px;
        }

        .bordergrey {
            border: solid 1px #d9cdcd;
            padding: 10px 15px;
            border-radius: 3px;

        }

        .form-switch-horizonal {
            flex-direction: row;
        }

        .form-check-label-horizonal {
            padding-bottom: 2px !important;
        }

        main.admin {
            padding-top: 0px;
        }
    </style>