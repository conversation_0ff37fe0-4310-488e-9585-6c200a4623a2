<?php


/*
$data = [
    ["tipo" => "info", "texto" => "uno"],
    ["tipo" => "si", "texto" => "dos"],
    ["tipo" => "no", "texto" => "tres"],
    ["tipo" => "si", "texto" => "cuatro"]
];
*/

$data = getfun("getUserNotes(\"id\":$id)")["data"];
?>
 
<h3 class="text-center" >Notas del anuncio con id <?= $id ?></h3>

<form action='/form-anuncio/<?=$id?>&fun=userNotesSave()' method='POST'>
 
   <?php if ($data) : ?> 

        <?php foreach ($data as $key => $value) : ?>

            <div class="row justify-content-center mt-2">

                <div class="col-4">
                    <select class="form-select" name="notas[<?=$key?>][tipo]">
                        <!-- Edición recuperar categoria del actual anuncio -->
                        <option value="info" <?= $value["tipo"] == 'info' ? 'selected' : '' ?>>Info</option>
                        <option value="no" <?= $value["tipo"] == 'no' ? 'selected' : '' ?>>No</option>
                        <option value="si" <?= $value["tipo"] == 'si' ? 'selected' : '' ?>> Si</option>
                    </select>
                </div>
                <div class="col-6">
                    <input type="text" class="form-control" value="<?= $value["texto"] ?>" name="notas[<?=$key?>][texto]" placeholder="Nombre">
                </div>
                <div class="col-1">
                    <button type="button" onclick="remove(this)" class="btn btn-danger">X</button>
                </div>

            </div>


        <?php endforeach ?>

    <?php else: ?>

        <p class="text-center" >No hay ninguna nota para este anuncio</p>

    <?php endif ?>



    <div class="row d-flex justify-content-center mt-4">

        <div class="col-8 ">
        
            <div class="row">
                <div class="col-6 d-grid gap-2 ">
                    <button type="submit" class="btn btn-success">Guardar</button> 

                </div>
                <div class="col-6 d-grid gap-2 ">

                    <a href="/form-notas/&fun=userNotesNueva()&id=<?=$id?>" type="button" class="btn btn-primary">Nueva</a>
                </div>
            </div>

        </div>
      

    </div>

    
</form>
 
 <script>

        function remove(e){
            elemento = e.parentNode.parentNode 

            elemento.remove();
        }
 

 </script>