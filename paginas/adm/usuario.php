<?php

$id = app\uri(2);
$anuncio = getfun("getAnuncio(\"id\":$id)")["data"];
$usuario = $pdo->query("SELECT * from usuarios WHERE id = $anuncio->id_usuario")->fetch(); 


?>
<br>
<h3>Asignar usuario anuncio <?= $id ?></h3>

<form action='/anuncios/&fun=usuarioCrear()' method='POST'>

    <div class="anuncioForm" style="max-width: 1440px; margin: 0 auto">

        <input type="hidden" name="id" value="<?= $id ?>">
        <div class="row justify-content-center p-2 p-sm-1"> <!-- nombre y pais -->

        
        <div class="col-6 mb-3"> 

                    <label for="usuario_email" class="form-label"><?= $usuario->id == 1 ? 'Asignar el anuncio a: ' : 'Cambiar de usuario: '?></label>
                    <input type="text" name="usuario_email" id="usuario_email" class="form-control" placeholder="Usuario Email" value="<?= $usuario->id == 1 ? '' : @$usuario->email ?>">
       </div>

        </div> <!-- row -->



        
    <div class="row">
        <div class="d-grid gap-2 col-3 mx-auto">
            <input class="mt-3 btn btn-success" type="submit" value="Guardar">
        </div>
    </div><!-- row submit -->


    </div> <!-- anuncioForm -->


</form>