<?php

function getAnuncios(){
    global $pdo;
     $categoriaNumero = $_SESSION['filtros']['categoria']['id'];
 
     $gpsdb = $_SESSION['userGPS']['db'];

     $orden = $_SESSION['filtros']['orden']['sql'];
     

    $sql_categoria =  $categoriaNumero ? " AND id_categoria=$categoriaNumero" : '';  

    // horarios_anuncios
 
    if (isset($_GET['search'])) {
        $value = $_GET['search'];
    
        if (is_numeric($value)) $value = str_replace(' ', '', $value);
    
        $sqlAnuncios = "SELECT *, ROUND(st_distance_sphere($gpsdb, gps)/1000,2) as distancia FROM anuncios WHERE estado_anuncio=1  AND (nombre ='$value' OR telefono='$value' OR telefono2='$value') ORDER BY $orden";    //app\debug($sql);
        $anuncios = $pdo->query($sqlAnuncios )->fetchAll();
        
    } else {
        $sqlAnuncios = "SELECT *, ROUND(st_distance_sphere($gpsdb, gps)/1000,2) as distancia FROM anuncios WHERE estado_anuncio=1 $sql_categoria ORDER BY $orden";    //app\debug($sql);
        $anuncios = $pdo->query($sqlAnuncios)->fetchAll(); // Crea OBJ con los datos para listar en html  

        /* EJEMPLO DE SQL PARA QUE UN ANUNCIO QUE TIENE alcance = ciudad, se muestre en las ciudades en anuncio_ciudad disponibles */
        /* NOTA: Previamente podria haber leido los id de las ciudades que el anuncio tiene en anuncio_ciudades (array con los id de las ciudades) */
        /* 
            $ciudad = '21'

            SELECT 
                *, 
                ROUND(st_distance_sphere($gpsdb, gps)/1000,2) as distancia  
            FROM anuncios 
            WHERE 
            estado_anuncio=1 
            AND alcance='ciudad'
            AND id IN (SELECT id_anuncio FROM anuncio_ciudades WHERE id_ciudad=$ciudad) 
            ORDER BY $orden
        
        */
    }  


    return ["sql"=>$sqlAnuncios, "data"=>$anuncios]; 
 }

 
 /*  

Retorna SI|NO_ HORAS CAMBIO

ORDEN
----- Si esta disponible el 2 valor sera de mayor a menor
1_24: 24 horas disponible
1_10: 10 horas disponible
1_2: 2horas disponible

----- si no esta disponible el 2 valor sera de menor a mayor
0_1: disponible en 1 hora
0_6: disponible en 6 horas


*/

function trabajaAhora($opt){ // params = id, dia 0|6 , hora 0|23
 
    $id = $opt['id'];
    $hoy = intval(date('N')) - 1;
    $dia = isset($opt['dia']) ? $opt['dia'] : $hoy;
    $hora = isset($opt['hora']) ? $opt['hora'] : intval(date('G')); 
    $hora = intval($hora);
    //descanso horas cambio pensar como hacer
 
    $horario = getHorarioDia(['id'=>$id,'dia'=>$dia]);

    $hora_inicio = $horario[1];
    $hora_fin = $horario[2];


    if($hora_fin == 0){ 
        $disponiblepor = 24 - $hora  >= 0 ? 24 - $hora : 0;  
    } else{
        $disponiblepor = $hora_fin - $hora  >= 0 ? $hora_fin - $hora : 0; 
    }

   // $vuelve = 
   
    // No esta trabajando hoy
    if ($horario[0] == false) return [0, $disponiblepor];

     
    // Las 24h
    if ($hora_inicio == 0 & $hora_fin == 0) return [1, 24];

     // de madrugada
     if($hora_fin < $hora_inicio && $hora_fin != 0){
        if ($hora < 7 & $hora >= $hora_fin ) return [0, 0]; 
        if ($hora < 7 & $hora < $hora_fin) return [1, $disponiblepor];
        if ($hora > 7 & $hora > $hora_fin) return [1, 23 - $hora + $hora_fin];
    }

    // trabajando a esa hora termina a las 0h
    if( $hora_fin == 0){
        if ($hora >= $hora_inicio && $hora <= 23) return [1, $disponiblepor];
    }

    // Trabajando a esa hora
    if ($hora >= $hora_inicio && $hora < $hora_fin) return [1, $disponiblepor];
 
   

    // No esta trabajando 

    return [0,0];

}


// filtros() lee el array de session o por defecto y lo retorna
// ?fun=filtros("orden":"ultimos-dias","categoria":2) lee el array de session, lo modifica y retorna
  
function categoriaSingular($id){    
    $categorias = ["Sin categoria", "Chica", "Chico", "Transgénero", "Alojamiento"];

    return $categorias[$id];
}
 

 function filtros($opt = []){

    $order_disponible =
    " activo DESC," .
    " dia_inicio ASC," .
    " CASE ACTIVO" .
    " WHEN 1 THEN timestamp_fin" .
    " ELSE timestamp_inicio" .
    " END ASC," .
    " CASE ACTIVO" .
    " WHEN 1 THEN timestamp_inicio" .
    " ELSE timestamp_fin" .
    " END ASC";

    $ordenes = [
        "posicion" => ["slug" => "posicion", "nombre" => "Posición", "sql" => " fecha_posicionado DESC"],
        "disponibilidad" => ["slug" => "disponibilidad", "nombre" => "Disponibilidad", "sql" => $order_disponible ],
        "ultimos-dias" => ["slug" => "ultimos-dias", "nombre" => "Últimos días", "sql" => " fecha_caducidad ASC"],
        "mas-cercanos" => ["slug" => "mas-cercanos", "nombre" => "Más cercanos", "sql" => " distancia ASC"],
        "novedades" => ["slug" => "novedades", "nombre" => "Novedades", "sql" => " fecha_publicacion DESC"],
        "mas-jovenes" => ["slug" => "mas-jovenes", "nombre" => "Más jóvenes", "sql" => " edad ASC"],
        "mas-mayores" => ["slug" => "mas-mayores", "nombre" => "Más mayores", "sql" => " edad DESC"]
    ];

    $categorias = [
        ["nombre" => "Filtrar ", "id" => 0],
        ["nombre" => "Chicas", "id" => 1, "singular"=>"Chica"], 
        ["nombre" => "Chicos", "id" => 2, "singular"=>"Chico"], 
        ["nombre" => "Transgénero", "id" => 3, "singular"=>"Transgénero"], 
        ["nombre" => "Alogamientos", "id" => 4, "singular"=>"Alojamiento"]
    ];

    if( !isset($_SESSION["filtros"]) ){
        $_SESSION["filtros"] = [
            "orden" => $ordenes["posicion"],
            "categoria" => $categorias[0]
        ];
    }
 
  // Si GET modifica el valor de SESSION se setea nuevo estado

  // if (isset($opt["orden"])) $_SESSION["filtros"]["orden"] = $ordenes[ $opt["orden"] ];

  if( isset($opt["categoria"]) ){
    $_SESSION["filtros"]["categoria"] = $categorias[ $opt["categoria"] ];
  }
   
  if( isset($opt["orden"]) ){
    $_SESSION["filtros"]["orden"] = $ordenes[ $opt["orden"] ];
  }
 
  return $_SESSION["filtros"];
    
 }

 if ( !isset($_SESSION["filtros"]) ) filtros();
 
 //if (!isset($_SESSION['filtros'])) filtroAdm();



/* 

 Usando para disponible ahora 

 Dependende de getHorario()

 Params ( id, dia semana 1|6 )

 Return [ true|false, hora_inicio, hora_fin] 
 
*/

function getHorarioDia($opt){
    global $pdo;

    $id = $opt['id'];
    $hoy = intval(date('N')) - 1;
    $dia = isset($opt['dia']) ? $opt['dia'] : $hoy; 
    $horario = getHorario(["id"=>$id])['data'];

    foreach($horario as $rango){
 
        $dia_inicio = $rango[0];
        $dia_fin = $rango[1];
        $hora_inicio = $rango[2];
        $hora_fin = $rango[3];
      
        /* disponible */
        if ($dia >= $dia_inicio & $dia <= $dia_fin){
            if($rango['active'] != true) continue;
 
            return [true, $hora_inicio, $hora_fin];
        }
      
    }

    return [false, 0, 0];

}


/* Depende de getHorario y usado para horario portadas string*/ 

function getHorarioHoyStr($opt){ 

    $id = $opt['id'];

    $hoy = intval(date("N")) - 1; 

    $dia = isset($opt['dia']) ? $opt['dia'] : $hoy;

    $horario = getHorarioDia(['id'=>$id,'dia'=>$dia]);
     
    if ($horario[0] == false) return "No trabaja";
    $hora_inicio = $horario[1];
    $hora_fin = $horario[2];

    if ($hora_inicio == 0 & $hora_fin == 0) return "24H";
    return $hora_inicio."H a ". $hora_fin . "H";

 
}