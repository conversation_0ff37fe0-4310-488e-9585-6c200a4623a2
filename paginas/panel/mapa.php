
<?php

$id = flex()->uri(-1);

$anuncio = getfun("getAnuncio(\"id\":$id)")['data']; 

$anuncioGPS = $anuncio->gps;
// $anuncioGPS =  isset($anuncio->gps) ? getGps($anuncio->gps) : false;
 

?>
<script src="https://maps.googleapis.com/maps/api/js?key=<?= $MAPKEY ?>&libraries=places&callback=initMap&v=weekly" defer></script> 


<div class="row justify-content-center">

<div class="col-12 col-lg-6">
      
<form class="panel" action='&fun[]=setCoords()&fun[]=redirect(url:/panel/)' method='POST'>
    <input type="hidden" name="id" value="<?=$id?>">
<label for="dirección" class="form-label">Dirección: </label>
            <input type="text" class="form-control" id="direccion" placeholder="Escribe dirección">
            <label for="coords" class="form-label">Coordenadas: </label>
            <input class="form-control" id="coords" name="gps" type="text"> <br>

            <div class="row justify-content-center">
                <div class="col text-center">
                    <div id="map" style="width:100%;height:350px"></div>
                </div>
            </div>


            <div class="col text-center">
    <button type="submit" value="Submit" class="mt-3 mb-3 btn btn-success btn-lg">Guardar</button>
    <a type="button" href="/anuncios" class="mt-3 mb-3 btn btn-danger btn-lg">Cancelar y volver</a>

    </form>
</div>
</div>


<!-- MAPA -->
<script>
let current_gps = <?= $anuncioGPS ?>;
let map;
let marker;
let autocomplete;
const initMap = async () => {

    const placeMarker = (position) => {
        if (marker == null) {
            marker = new google.maps.Marker({
                position,
                map
            });
        } else {
            marker.setPosition(position)
        }
    }
    const $direccion = document.getElementById('direccion');
    const options = {
         
        fields: ["address_components", "geometry", "icon", "name"],
        strictBounds: false
    }
    const $divMap = document.getElementById("map");
    map = new google.maps.Map($divMap, {
        center: current_gps,
        zoom: 12
    })

    

    const geocodeLatLng = async (latlng) => {
        const geocoder = new google.maps.Geocoder();
        const response = await geocoder.geocode({
            location: latlng
        });
        if (response.results[0]) {
            return response.results[0].formatted_address;
        }
        return "No se encontró ubicación";
    }
    map.addListener('click', async (e) => {

        const position = e.latLng;
        const {
            lat,
            lng
        } = position;
        const positionJSON = {
            lat: lat(),
            lng: lng()
        };
        const positionText = `${lat()},${lng()}`
        const $coords = document.getElementById('coords');
        console.log(positionJSON);
        const direccion = await geocodeLatLng(positionJSON);
        $direccion.value = direccion;
        $coords.value = positionText;
        placeMarker(position)
    })
    placeMarker(current_gps);
    const direccion = await geocodeLatLng(current_gps);
    $direccion.value = direccion;

    const positionText = `${current_gps.lat},${current_gps.lng}`
    const $coords = document.getElementById('coords');
    $coords.value = positionText;
    autocomplete = new google.maps.places.Autocomplete($direccion, options)
    autocomplete.addListener("place_changed", () => {
        const place = autocomplete.getPlace();
        const position = place.geometry.location;
        const {
            lat,
            lng
        } = position;
        const positionJSON = `${lat()},${lng()}`
        console.log(positionJSON);
        placeMarker(position);
        map.setCenter(position);
        map.setZoom(17);
        const $coords = document.getElementById('coords');
        $coords.value = positionJSON;
    })

}
window.initMap = initMap
</script>