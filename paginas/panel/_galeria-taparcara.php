
<?php
/** @var $fotoid id
 * @var $id id
 * @var $file string
 */

// detect File size
$size = list($width, $height) = getimagesize("uploads/fotos/".$file);
$imgHeight = $size[1];
$imgWidth = $size[0];
?>

                    <div class="img-container mb-2" style="margin: 0 auto">
                        <img src="/uploads/fotos/<?=$file?>?rel=<?= time() ?>">
                    </div>
                    <!--  <p class="text-center"><?= "Tamaño imágen: $imgWidth x $imgHeight" ?></p> -->
                    <form action="/paginas/panel/_galeria-taparcara-procesar.php" method="POST">
                        <input type="hidden" name="fotoid" value="<?=$fotoid?>">
                        <input type="hidden" name="anuncio" value="<?=$id?>">
                        <input id="filename" name="filename" type="hidden" value="<?=$file?>" readonly>

                        <input name="imgWidth" type="hidden" value="<?= $imgWidth ?>">
                        <input name="imgHeight" type="hidden" value="<?= $imgHeight ?>">


                        <div class="row justify-content-center p-1">
                            <div class="row mb-3" style="max-width: 320px; box-shadow: 1px 2px 2px #d1d1d1; border: solid 1px #bdc0c2; padding: 10px">
                                <div class="col-6 text-center" style="border-right: 1px solid #bdc0c2">
                                    <label for="intensidad" class="form-label">Intensidad:</label>
                                    <input type="range" class="form-range" id="intensidad" name="intensidad" min="1" max="3" value="2">
                                </div>
                                <div class="col-6">
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="radio" name="filter" value="desenfocar" id="desenfocar">
                                        <label class="form-check-label" for="desenfocar">
                                            Desenfocar.
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="filter" value="pixelar" id="pixelar" checked>
                                        <label class="form-check-label" for="pixelar">
                                            Pixelar.
                                        </label>
                                    </div>
                                </div>
                            </div>

                        </div>


                        <div class="row justify-content-center" style="display:none">
                            <p class="text-center">Selección</p>
                            <div class="col-12 col-md-5">
                                <div class="row text-center">
                                    <div class="col">
                                        <label for="selectWidth">Ancho:</label>
                                        <input class="ocultar" placeholder="anchura" name="selectWidth" id="selectWidth" style="width: 40px" readonly>
                                    </div>
                                    <div class="col">
                                        <label for="selectHeight">Alto:</label>
                                        <input class="ocultar" placeholder="altura" name="selectHeight" id="selectHeight" style="width: 40px" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row justify-content-center" style="display:none">
                            <div class="col-12 col-md-4">
                                <div class="row text-center">
                                    <div class="col">
                                        <label for="xCoord">X:</label>
                                        <input id="xCoord" style="width: 40px" name="xCoord">

                                    </div>
                                    <div class="col">
                                        <label for="yCoord">Y:</label>
                                        <input id="yCoord" style="width: 40px" name="yCoord">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="d-grid gap-2 col-4 mx-auto mt-4">
                            <button class="btn btn-warning pe-2" type="submit" id="blurButton">PREVISUALIZAR </button>
                        </div>

                    </form>




<div class="row mt-4" style="width:100%; max-width: 600px; margin: 0 auto">

    <div class="d-flex justify-content-center">

                <form action="/paginas/panel/_galeria-taparcara-procesar.php" method="POST" style="width:50%">
                    <input type="hidden" name="fotoid" value="<?=$fotoid?>">
                    <input type="hidden" name="anuncio" value="<?=$id?>">
                    <input type="hidden" name="restaurar" value="1">
                    <input type="hidden" name="filename" value="<?=$file?>">
                    <button type="submit" class="btn btn-danger" style="width:100%">Restaurar</button>
                </form>

                <a href="/panel/galeria/<?=$id?>/&step=2&edit" style="display:block; width:50%">
                    <button type="button" class="btn btn-success ms-2" style="display:block; width:100%">Guardar</button>
                </a>
     </div>
</div>


<!-- Tapar cara script -->


<script>
    const image = document.querySelector("img");
    const cropper = new Cropper(image, {
        zoomable: false,
        zoomOnWheel: false,
        wheelZoomRatio: false,
        zoomOnTouch: false,
        minContainerWidth: 240,
        minContainerHeight: 240,
        responsive: true,

        crop(event) {
            // convertir a enteros

            document.getElementById("xCoord").value = parseInt(event.detail.x);
            document.getElementById("yCoord").value = parseInt(event.detail.y);
            document.getElementById("selectWidth").value = parseInt(event.detail.width);
            document.getElementById("selectHeight").value = parseInt(event.detail.height);
        },
    });
</script>


<style>

    body{
        overflow: hidden;
    }

    .main{
        padding: 5px !important;
        min-height: 100vh;
    }

    .control-container label {
        margin: 0 5px;
    }

    .cropper-container{
        margin: 0 auto;
    }

    #canvas {
        border: 1px solid #000;
    }

    img {
        display: block;
    }

    .img-container{
        width: 95%;
        max-width: 600px;
        overflow: hidden;
        height: 240px;
    }

</style>