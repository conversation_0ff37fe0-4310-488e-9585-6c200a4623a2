<?php
// Obtenemos la respuesta de choices content 
$client = new \GuzzleHttp\Client();

// Procesar y validar los datos de entrada
$text = isset($_POST['text']) ? trim(htmlspecialchars($_POST['text'])) : '';
$edad = isset($_POST['edad']) && !empty($_POST['edad']) ? intval(trim($_POST['edad'])) : '';
$pais = isset($_POST['pais']) && !empty($_POST['pais']) ? trim(htmlspecialchars($_POST['pais'])) : '';
$nombre = isset($_POST['nombre']) && !empty($_POST['nombre']) ? trim(htmlspecialchars($_POST['nombre'])) : 'Anónimo';
$gustos = isset($_POST['gustos']) ? array_map('htmlspecialchars', $_POST['gustos']) : [];
$gustos = !empty($gustos) ? implode(', ', $gustos) : '';
$personalidad = isset($_POST['personalidad']) && !empty($_POST['personalidad']) ? trim(htmlspecialchars($_POST['personalidad'])) : '';
$caracter = isset($_POST['caracter']) && !empty($_POST['caracter']) ? trim(htmlspecialchars($_POST['caracter'])) : '';

// Recuperar checkboxes
$whatsapp = isset($_POST['whatsapp']) ? filter_var($_POST['whatsapp'], FILTER_VALIDATE_BOOLEAN) : false;
$salidas = isset($_POST['salidas']) ? filter_var($_POST['salidas'], FILTER_VALIDATE_BOOLEAN) : false;
$formadecontacto = $whatsapp ? 'whatsapp' : 'telefono';

// Procesar aspectos físicos
$altura = isset($_POST['altura']) && !empty($_POST['altura']) ? intval(trim($_POST['altura'])) : '';
$color_pelo = isset($_POST['color_pelo']) && !empty($_POST['color_pelo']) ? trim(htmlspecialchars($_POST['color_pelo'])) : '';
$color_ojos = isset($_POST['color_ojos']) && !empty($_POST['color_ojos']) ? trim(htmlspecialchars($_POST['color_ojos'])) : '';
$medidas = isset($_POST['medidas']) && !empty($_POST['medidas']) ? trim(htmlspecialchars($_POST['medidas'])) : '';
$complexion = isset($_POST['complexion']) && !empty($_POST['complexion']) ? trim(htmlspecialchars($_POST['complexion'])) : '';

$aspectoFisico = [];
if (!empty($altura)) $aspectoFisico[] = "altura: $altura cm";
if (!empty($color_pelo)) $aspectoFisico[] = "color de pelo: $color_pelo";
if (!empty($color_ojos)) $aspectoFisico[] = "color de ojos: $color_ojos";
if (!empty($medidas)) $aspectoFisico[] = "medidas: $medidas";
if (!empty($complexion)) $aspectoFisico[] = "complexión: $complexion";
$aspectoFisicoStr = !empty($aspectoFisico) ? " Mi aspecto físico: " . implode(', ', $aspectoFisico) : '';

try {
    $response = $client->request('POST', 'https://api.mistral.ai/v1/chat/completions', [
        'headers' => [
            'Authorization' => 'Bearer wWB8ZQS70RNcGfQ73wTrjz9T4jWsglQn',
            'Content-Type' => 'application/json',
            'Accept' => 'application/json'
        ],
        'json' => [
            'model' => 'mistral-small-2501',
            'messages' => [
                ['role' => 'system', 'content' => "Eres un asistente de IA que genera perfiles de citas en formato HTML.
                
                ESTRUCTURA HTML REQUERIDA:
                <div class='respuesta'>
                    <h3 class='titulo-perfil'>[título atractivo]</h3>
                    <p class='descripcion'>[contenido principal]</p>
                </div>

                INSTRUCCIONES PARA CONTENIDO PERSONALIZADO:
                - MÁXIMA PRIORIDAD: Todo el contenido del texto adicional debe incluirse en el perfil
                - Usar etiquetas <strong> SOLO en la descripción, NUNCA en el título
                - Si hay instrucciones sobre el título, aplicarlas exactamente pero sin negritas
                - Si el texto adicional contiene frases o palabras clave, incluirlas en negrita SOLO en la descripción
                - NUNCA inventes o asumas una edad si no se proporciona explícitamente

                REGLAS PARA EL TÍTULO (H3):
                1. Si hay instrucciones específicas en el texto adicional:
                   - Usar exactamente lo especificado, SIN etiquetas <strong>
                   - Construir título dinámicamente según datos disponibles y sin superar las 6 o 7 palabras.
                2. Si NO hay instrucciones específicas:
                   - SOLO mencionar la edad si se proporciona explícitamente
                   - Si hay edad proporcionada: '<nombre>, <edad> años, [resto del título]'
                   - Si NO hay edad proporcionada: NUNCA menciones ni inventes una edad
                   - Si hay país: '<nombre> de <nacionalidad>'
                   - Si no hay datos demográficos: Usar '<nombre>, [frase descriptiva basada en personalidad/gustos]'
                   - Terminar siempre con frase seductora y llamada a la accion.
                
                
                REGLAS PARA LA DESCRIPCIÓN (P):
                - OBLIGATORIO: Incluir TODO el contenido del texto adicional en <strong>
                - NUNCA mencionar ni asumir una edad si no fue proporcionada
                - Comenzar con 'Hola, soy...'
                - Integrar naturalmente el contenido adicional en negrita
                - Tono sensual y picaresco pero elegante
                - Si le gusta salir, enfatizar actividades exteriores
                - Si prefiere interior, enfatizar ambientes íntimos
                - Incluir gustos y aspectos físicos naturalmente
                - Terminar invitando a contactar por el medio especificado"],
                ['role' => 'user', 'content' => "Tengo " . ($edad ? "$edad años" : "") . 
                (empty($edad) ? "" : ", ") . 
                (empty($pais) ? "" : "soy de $pais") . 
                ", me llamo $nombre. " . 
                (!empty($gustos) ? "Mis gustos son: $gustos. " : "") . 
                (!empty($personalidad) ? "Mi personalidad es $personalidad " : "") . 
                (!empty($caracter) ? "y mi carácter es $caracter. " : "") . 
                "Prefiero contactar por $formadecontacto. " . 
                ($salidas ? 'Me encanta salir y conocer nuevos lugares.' : 'Prefiero quedarme en casa y disfrutar de ambientes más íntimos.') . 
                $aspectoFisicoStr . 
                (!empty($text) ? ". Texto adicional: $text" : "")]
            ],
            'temperature' => 0.7,
            'max_tokens' => 1000
        ]
    ]);

    // Decodificar la respuesta
    $responseData = json_decode($response->getBody(), true);
    
    // Obtener el contenido de la respuesta
    $responseContent = $responseData['choices'][0]['message']['content'];
    
    // Función para limpiar el contenido de markdown y asegurar HTML válido
    function cleanContent($content) {
        // Eliminar backticks y marcadores de código
        $content = preg_replace('/```html\s*/', '', $content);
        $content = preg_replace('/```\s*/', '', $content);
        
        // Eliminar espacios y líneas en blanco al inicio y final
        $content = trim($content);
        
        // Si el contenido no empieza con <div class='respuesta'>, envolverlo
        if (!preg_match('/^<div class=[\'"]respuesta[\'"]>/', $content)) {
            // Extraer el título y la descripción si existen
            preg_match('/<h3[^>]*>(.*?)<\/h3/s', $content, $titleMatch);
            preg_match('/<p[^>]*>(.*?)<\/p>/s', $content, $descMatch);
            
            $title = $titleMatch[1] ?? 'Perfil Generado';
            $desc = $descMatch[1] ?? $content;
            
            $content = "<div class='respuesta'>\n" .
                      "    <h3 class='titulo-perfil'>" . $title . "</h3>\n" .
                      "    <p class='descripcion'>" . $desc . "</p>\n" .
                      "</div>";
        }
        
        return $content;
    }
    
    // Limpiar y estructurar el contenido
    $respuesta = cleanContent($responseContent);

} catch (\GuzzleHttp\Exception\RequestException $e) {
    $errorDetails = [
        'message' => $e->getMessage(),
        'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : 'No response'
    ];
    
    error_log('Error en la API: ' . print_r($errorDetails, true));
    $respuesta = '<div class="respuesta">
        <h3 class="titulo-perfil text-danger">Error al generar el perfil</h3>
        <p class="descripcion">' . htmlspecialchars($errorDetails['message']) . '</p>
    </div>';
}
?>

 
<style>
    /* Estilos personalizados para Choices */
    .custom-choices {
        margin-bottom: 1rem;
    }
    .choices__inner {
        min-height: 45px;
        padding: 5px 7.5px 5px;
        background-color: #fff;
    }
    .choices__list--multiple .choices__item {
        background-color: #3498db;
        border: 1px solid #2980b9;
        padding: 4px 10px;
        margin: 2px;
        color: #fff;
    }
    .choices__list--multiple .choices__item.is-highlighted {
        background-color: #2980b9;
        border: 1px solid #2980b9;
    }
    .choices[data-type*="select-multiple"] .choices__button {
        border-left: 1px solid #2980b9;
    }
    .choices__list--dropdown .choices__item--selectable.is-highlighted {
        background-color: #f1f1f1;
    }
    .choices__list--dropdown {
        z-index: 999;
    }

    /* Estilos para el título y descripción */
    .titulo-perfil {
        font-size: 1.2rem;
        margin-bottom: 1rem;
        color: #2c3e50;
    }
    .descripcion {
        font-size: 1.1rem;
        line-height: 1.6;
        color: #34495e;
    }
</style>

<div class="container mt-4"> 

    <!-- Contenedor para la respuesta HTML -->
    <?php if(isset($respuesta)): ?>
    <div class="alert alert-info mb-4">
        <?php 
        // Asegurarnos de que el HTML se muestre correctamente
        echo $respuesta;
        ?>
    </div>
    <?php endif; ?>

    <!-- Estilos específicos para la respuesta -->
    <style>
        .respuesta {
            font-family: 'Arial', sans-serif;
            padding: 15px;
        }
        .titulo-perfil {
            color: #2c3e50;
            font-size: 1.8rem;
            margin-bottom: 1.5rem;
            font-weight: 600;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
        }
        .descripcion {
            font-size: 1.1rem;
            line-height: 1.6;
            color: #34495e;
            margin-bottom: 1rem;
        }
        .custom-checkbox {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 5px;
            padding: 5px;
        }
        .custom-checkbox .form-check-input {
            margin: 0;
            float: none;
        }
        .custom-checkbox .form-check-label {
            margin: 0;
            padding-left: 5px;
        }
        .text-danger {
            color: #e74c3c !important;
        }
        @media (max-width: 768px) {
            .container {
                padding: 0 !important;
                margin: 0 !important;
                max-width: 100% !important;
            }
            .form-container {
                padding: 10px !important;
            }
            .row {
                margin: 0 !important;
            }
            .col-sm-12, .col-md-6, .col-md-4 {
                padding: 0 5px !important;
            }
            .accordion {
                margin: 0 !important;
            }
            .accordion-body {
                padding: 10px !important;
            }
            .alert {
                margin: 0 !important;
                border-radius: 0 !important;
            }
            .form-control, .form-select {
                margin-bottom: 5px;
            }
            .bg-light {
                padding: 10px !important;
                border-radius: 0 !important;
            }
            .mb-3, .mb-4 {
                margin-bottom: 0.5rem !important;
            }
            .g-3 {
                --bs-gutter-x: 0.5rem !important;
                --bs-gutter-y: 0.5rem !important;
            }
        }
    </style>

    <form action="" id="panel" method="POST" class="bg-light p-4 rounded form-container panel" style="position: relative;">
        <!-- Datos principales en vista móvil -->
        <div class="row g-3 mb-3">
            <!-- Grupo 1: Nombre, Edad, País -->
            <div class="col-xl-8 col-lg-12">
                <div class="row">
                    <div class="col-lg-4 col-sm-12">
                        <input type="text" class="form-control" required id="nombre" name="nombre" placeholder="Nombre" value="<?php echo isset($_POST['nombre']) ? htmlspecialchars($_POST['nombre']) : ''; ?>">
                    </div>
                    <div class="col-lg-4 col-sm-12">
                        <input type="number" class="form-control" id="edad" name="edad" placeholder="Edad" value="<?php echo isset($_POST['edad']) ? htmlspecialchars($_POST['edad']) : ''; ?>">
                    </div>
                    <div class="col-lg-4 col-sm-12">
                        <input type="text" class="form-control" id="pais" name="pais" placeholder="País" value="<?php echo isset($_POST['pais']) ? htmlspecialchars($_POST['pais']) : ''; ?>">
                    </div>
                </div>
            </div>
            
            <!-- Grupo 2: Checkboxes -->
            <div class="col-xl-4 col-lg-12 mt-lg-3 mt-xl-0">
                <div class="row g-0">
                    <div class="col-6">
                        <div class="form-check custom-checkbox">
                            <input class="form-check-input" type="checkbox" id="whatsapp" name="whatsapp" <?php echo isset($_POST['whatsapp']) ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="whatsapp">WhatsApp</label>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-check custom-checkbox">
                            <input class="form-check-input" type="checkbox" id="salidas" name="salidas" <?php echo isset($_POST['salidas']) ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="salidas">Salidas</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-3 mb-3">
            <div class="col-md-12">
                <div class="accordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#sobreMi">
                                Sobre mí
                            </button>
                        </h2>
                        <div id="sobreMi" class="accordion-collapse collapse">
                            <div class="accordion-body">
                                <div class="row g-3 mb-3">
                                    <div class="col-md-6">
                                        <label for="personalidad" class="form-label">Personalidad:</label>
                                        <select name="personalidad" id="personalidad" class="form-select">
                                            <option value="introvertida" <?php echo (isset($_POST['personalidad']) && $_POST['personalidad'] == 'introvertida') ? 'selected' : ''; ?>>Introvertida</option>
                                            <option value="extrovertida" <?php echo (isset($_POST['personalidad']) && $_POST['personalidad'] == 'extrovertida') ? 'selected' : ''; ?>>Extrovertida</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="caracter" class="form-label">Carácter:</label>
                                        <select name="caracter" id="caracter" class="form-select">
                                            <option value="fuerte y dominante" <?php echo (isset($_POST['caracter']) && $_POST['caracter'] == 'fuerte y dominante') ? 'selected' : ''; ?>>Fuerte y dominante</option>
                                            <option value="dulce y amable" <?php echo (isset($_POST['caracter']) && $_POST['caracter'] == 'dulce y amable') ? 'selected' : ''; ?>>Dulce y amable</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="row g-3 mb-3">
                                    <div class="col-md-6">
                                        <label for="gustos" class="form-label">Gustos e intereses:</label>
                                        <select name="gustos[]" id="gustos" class="form-select choices-select" multiple>
                                            <?php
                                            $gustosOptions = [
                                                'música', 'deporte', 'viajes', 'lectura', 'cine', 'Una cena romántica', 
                                                'conversaciones profundas', 'baile', 'Dar un buen masaje', 
                                                'Recibir un masaje', 'Reir y bromear', 'moda', 
                                                'una noche loca de pasión', 'Salir de fiesta en buena compañia'
                                            ];
                                            $selectedGustos = isset($_POST['gustos']) ? $_POST['gustos'] : [];
                                            foreach($gustosOptions as $gusto): ?>
                                                <option value="<?php echo $gusto; ?>" <?php echo in_array($gusto, $selectedGustos) ? 'selected' : ''; ?>><?php echo ucfirst($gusto); ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="disgustos" class="form-label">No me gusta:</label>
                                        <select name="disgustos[]" id="disgustos" class="form-select choices-select" multiple>
                                            <?php
                                            $disgustosOptions = [
                                                'fumar', 'alcohol', 'drogas', 'ruido excesivo', 'desorden', 
                                                'impuntualidad', 'egoísmo', 'mentiras', 'mala higiene', 
                                                'mala educación', 'Que me hagan perder el tiempo'
                                            ];
                                            $selectedDisgustos = isset($_POST['disgustos']) ? $_POST['disgustos'] : [];
                                            foreach($disgustosOptions as $disgusto): ?>
                                                <option value="<?php echo $disgusto; ?>" <?php echo in_array($disgusto, $selectedDisgustos) ? 'selected' : ''; ?>><?php echo ucfirst($disgusto); ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-3 mb-3">
            <div class="col-md-12">
                <div class="accordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#aspectoFisico">
                                Aspecto Físico
                            </button>
                        </h2>
                        <div id="aspectoFisico" class="accordion-collapse collapse">
                            <div class="accordion-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="altura" class="form-label">Altura (cm):</label>
                                        <input type="number" name="altura" id="altura" class="form-control" placeholder="Ej: 170">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="color_pelo" class="form-label">Color de pelo:</label>
                                        <select name="color_pelo" id="color_pelo" class="form-select">
                                            <option value="">Selecciona</option>
                                            <option value="rubio">Rubio</option>
                                            <option value="moreno">Moreno</option>
                                            <option value="castaño">Castaño</option>
                                            <option value="pelirrojo">Pelirrojo</option>
                                            <option value="canoso">Canoso</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="color_ojos" class="form-label">Color de ojos:</label>
                                        <select name="color_ojos" id="color_ojos" class="form-select">
                                            <option value="">Selecciona</option>
                                            <option value="marrones">Marrones</option>
                                            <option value="azules">Azules</option>
                                            <option value="verdes">Verdes</option>
                                            <option value="grises">Grises</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="medidas" class="form-label">Medidas (opcional):</label>
                                        <input type="text" name="medidas" id="medidas" class="form-control" placeholder="Ej: 90-60-90">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="complexion" class="form-label">Complexión:</label>
                                        <select name="complexion" id="complexion" class="form-select">
                                            <option value="">Selecciona</option>
                                            <option value="delgada">Delgada</option>
                                            <option value="atlética">Atlética</option>
                                            <option value="mediana">Mediana</option>
                                            <option value="rellenita">Rellenita</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Texto adicional colapsado -->
        <div class="row g-3 mb-3">
            <div class="col-md-12">
                <div class="accordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#textoAdicional">
                                 Contenido personalizado e instrucciones adicionales
                            </button>
                        </h2>
                        <div id="textoAdicional" class="accordion-collapse collapse">
                            <div class="accordion-body">
                                <div class="mb-3"> 
                                    <textarea name="text" id="text" class="form-control" cols="30" rows="2"><?php echo isset($_POST['text']) ? htmlspecialchars($_POST['text']) : ''; ?></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-auto">
                <button type="submit" class="btn btn-info mb-5">
                    <i class="fas fa-magic me-2"></i>Generar con Inteligencia artificial
                </button>
            </div>
        </div>
        <br><br><br>
    </form>
    <br><br><br>
    <div class="mt-4 mb-5">
     </div>
</div>

 
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Inicializar todos los selectores de Choices
        const elements = document.querySelectorAll('.choices-select');
        elements.forEach(element => {
            const choices = new Choices(element, {
                removeItemButton: true,
                maxItemCount: 5,
                searchEnabled: true,
                placeholder: true,
                placeholderValue: element.id === 'gustos' ? 'Selecciona tus gustos (máximo 5)' : 'Selecciona lo que no te gusta (máximo 5)',
                noResultsText: 'No se encontraron resultados',
                itemSelectText: 'Click para seleccionar',
                classNames: {
                    containerOuter: 'choices custom-choices'
                }
            });
        });
    });
</script>