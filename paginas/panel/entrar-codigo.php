<?php
 
$email = $_GET["email"];

$usuario = getfun("getUsuario(email:$email)")["data"];
 
if($usuario != false){  
    
    $_SESSION['error'] = false;
}else {
    $_SESSION['error'] = "No se encontro una cuenta con ese email, registrese";
    //app\redirect("/panel/entrar");
}
  
if ( isset($_POST['code']) ){
    $code = $_POST["code"]; 
 
    $ahora = date('Y-m-d H:i:s');

    $expired_time =  addTimeToTimestamp($usuario->code_time, 10, "minutes");


    if($code == $usuario->code){
        if($ahora > $expired_time){
            $_SESSION['error'] = "
        <div class='alert alert-warning' role='alert'> 
        El código <strong>$code</strong> ha expirado <br>
        </div>
        ";
        }else{

            Flex()->user = $usuario;

            /* echo "
             <div class='alert alert-success' role='alert'>
             Código correcto, Preparando su panel: <br>
             </div>image.png
             <div hx-get='&fun=timer()&api' hx-swap='outerHTML' hx-trigger='load' hx-target='this'>
             <div>
             ";
             */

           // redireccionar a panel/inicio
        flex()->redirect("/panel/inicio/"); 
        exit();
           

        }
        // tiempo

    }else{
        $_SESSION['error'] = "
        <div class='alert alert-danger' role='alert'> 
        El código <strong>$code</strong> NO es correcto <br>
        </div>
        ";
    }

}


?>

<div class="vh-100 d-flex justify-content-center align-items-center">
  <div class="container">
    <div class="row d-flex justify-content-center">
      <div class="col-12 col-md-8 col-lg-6">
        <div class="border border-3 border-danger"></div>
        <div class="card bg-white">
          <div class="card-body p-5">

            <form class="mb-3 mt-md-4" action="" method="post">
              <!-- <h2 class="mb-2 text-uppercase">Citas</h2> -->
              <h2 class=" mb-5">CÓDIGO ENVIADO</h2>
              <p>Revise su correo <strong> <?= $usuario->email ?> </strong></p>
              <p>* Por seguridad expirá en 10 min. </p>

              <div class="mb-3">
                <label for="code" class="form-label ">Introduzca el código</label>
                <input type="number" class="form-control mb-4" name="code" required min="1111" max="9999" id="code" placeholder="">
                <div id="response">
                    <?= $_SESSION['error'] ?>
                </div>
                </div>
                <div class="d-grid">
              <button class="btn btn-success" type="submit">Entrar</button>
            </div>
            </form>
           

            <div>
              <p class='mb-0 text-center'>¿Código expirado o no recibido? <a href="/panel/x&fun=reenviarcodigo(id:<?=$usuario->id?>)" hx-swap="afterend" hx-get="/panel/x&fun=reenviarcodigo(id:<?=$usuario->id?>)" class='text-primary fw-bold'>Reenviar de nuevo</a></p>
            
              <p class="mb-0 text-center">¿Email incorrecto? <a href="/panel/registro" class="text-primary fw-bold"> Crear cuenta</a></p>

              <br>
              <p class="text-center">Si tiene problemas para acceder <a href="/paginas/contacto.html"> </br>contante con soporte</a></p>
            </div>

          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .main {
    background-color: black !important;
  }

  .card {
    border-radius: 0px 0px 5px 5px;
  }

  h2 {
    color: black;
    text-align: center;
  }

  @media (min-width: 1200px) {

    .p-xl-4 {
      padding: 0px !important;
    }
  }
</style>