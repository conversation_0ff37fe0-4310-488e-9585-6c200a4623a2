<?php

$id = app\uri(2);
$id_user = Flex()->user->id;
// Security check
$usuario_anuncio = getfun("getUsuarioAnuncio(id:$id)")["data"];
if($id_user != $usuario_anuncio->id_usuario){
    echo "<h2 style='color: black'>No tiene permisos para editar este anuncio</h2>";
    die();
}

$paises = flex()->fetchAll("SELECT name as label, alpha_2 as value FROM paises");
$paisesJS = json_encode($paises);
$categorias = flex()->fetchAll("SELECT * FROM categorias ORDER BY id ASC"); // Crea OBJ con los datos para listar en html

$anuncio = getfun("getAnuncio(\"id\":$id)")["data"];
$servicios = explode(",", $anuncio->servicios);

?>
<form id="detalle" class="panel" action="&fun[]=anuncioActualizar&fun[]=redirect(url:/panel/inicio)" method="POST">

<input type="hidden" name="id" value="<?= $id ?>">
<!-- block1 -->
<div class="row justify-content-center">
    <div class="col-12 col-lg-4 bordergrey me-lg-2">
        <div class="row">

            <div class="col-8">
                <label for="nombre" class="form-label">Nombre: </label>
                <input type="text" name="nombre" id="nombre" class="form-control" placeholder="Nombre" value="<?= $anuncio->nombre ?? '' ?>">
            </div> <!-- col -->
            <div class="col-4">
                <label for="edad" class="form-label">Edad: </label>
                <input type="text" name="edad" id="edad" class="form-control" placeholder="edad" value="<?= $anuncio->edad ?? '' ?>">
            </div> <!-- col -->

        </div> <!-- row -->

        <div class="row">
            <div class="col-8">
                <label for="pais" class="form-label mt-2">País o nacionalidad: </label>
                <input type="text" autofocus="false" class="form-control" id="pais" name="pais" placeholder="Escribe tu pais ..." autocomplete="off" aria-describedby="helpId" value="<?= $anuncio->pais ?? '' ?>">
                <input type="hidden" id="pais_value" name="id_pais" value="<?= $anuncio->id_pais ?? '' ?>" />
            </div> <!-- col -->

            <div class="col-4">
                <!-- -----  CATEGORIA ---- -->
                <label for="categoria" class="form-label mt-2">Categoria: </label>
                <select class="form-select" name="id_categoria" id="categoria">
                    <?php foreach ($categorias as $categoria) : ?>
                        <!-- Edición recuperar categoria del actual anuncio -->
                        <?php if ($id) : ?>
                            <option <?= $categoria->id == $anuncio->id_categoria ? 'selected' : '' ?> value="<?= $categoria->id ?>">
                                <?= $categoria->nombre ?>
                            <?php else : ?>
                                <!-- Nuevo poner por defecto a chica -->
                            <option <?= $categoria->nombre == 'chica' ? 'selected' : '' ?> value="<?= $categoria->id ?>">
                                <?= $categoria->nombre ?>
                            <?php endif ?>
                            </option>
                        <?php endforeach ?>
                </select>
            </div> <!-- col -->
        </div> <!-- row -->

        <div class="row"> <!-- Pago y salidas -->
                    <div class="col-3">
                        <div class="form-check form-switch">
                            <label class="form-check-label" for="salidas">Salidas</label>
                            <input class="form-check-input" type="checkbox" id="salidas" name="salidas" <?= @$anuncio->salidas ? 'checked' : '' ?>>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="form-check form-switch">
                            <label class="form-check-label" for="visa">VISA</label>
                            <input class="form-check-input" type="checkbox" id="visa" name="visa" <?= @$anuncio->visa ? 'checked' : '' ?>>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="form-check form-switch">
                            <label class="form-check-label" for="bizum">Bizum</label>
                            <input class="form-check-input" type="checkbox" id="bizum" name="bizum" <?= @$anuncio->bizum ? 'checked' : '' ?>>
                        </div>
                    </div>
                     
                </div>


    </div><!-- col bordergrey -->

    <div class="col-12 col-lg-4 telefonos bordergrey mt-2 mt-lg-0">
                <div class="row">
                    <div class="col-5">Teléfonos: </div>
                    <div class="col-6 text-center">Whastapp</div>
                    <hr class="mt-2">
                </div>
                <div class="row">
                    <div class="col-5">

                        <label for="telefono" class="form-label">Teléfono 1: </label>
                        <input type="tel" name="telefono" id="telefono" class="form-control" placeholder="Teléfono 1" value="<?= $anuncio->telefono ?? '' ?>">

                    </div>
                    <div class="col-6">
                        <div class="form-check form-switch">
                            <label class="form-check-label" for="hayWhatsapp1">Activar en tel 1</label>
                            <input class="form-check-input" type="checkbox" id="hayWhatsapp1" name="hayWhatsapp1" <?= @$anuncio->hayWhatsapp1 ? 'checked' : '' ?>>
                        </div>
                    </div>
                </div>

                <div class="row mt-2">
                    <div class="col-5">

                        <label for="telefono2" class="form-label">Teléfono 2: </label>
                        <input type="tel" name="telefono2" id="telefono2" class="form-control" placeholder="Telefono 2" value="<?= $anuncio->telefono2 ?? '' ?>">

                    </div>


                    <div class="col-6">
                        <div class="form-check form-switch">
                            <label class="form-check-label" for="hayWhatsapp2">Activar en tel 2</label>
                            <input class="form-check-input" type="checkbox" id="hayWhatsapp2" name="hayWhatsapp2" <?= @$anuncio->hayWhatsapp2 ? 'checked' : '' ?>>
                        </div>
                    </div>
                </div>

        </div> <!-- row titulo y texto -->

</div><!-- row block 1 -->
 <?php 
    $visible = isset($_GET['step']) ? "display:none" : "";
 ?>

<div class="row mt-3 justify-content-center">
    <div class="col-10 col-lg-6 mt-3 mx-auto text-center">
        <button class="btn btn-success" style="<?=$visible?>" type="submit">Guardar y salir</button>
    </div>
</div>
  
</form>

<!-- PAISES -->
<script>
    const paises = <?= $paisesJS ?>;
    const field = document.getElementById('pais');
    let isClicked = false

    const ac = new Autocomplete(field, {
        data: [{
            label: "I'm a label",
            value: 42
        }],
        maximumItems: 5,
        threshold: 1,
        onSelectItem: ({
            label,
            value
        }) => {
            $pais_value = document.getElementById("pais_value");
            $pais_value.setAttribute("value", value);

        }
    });

    // perder foco con click en body
    ac.setData(paises)

    document.getElementById("nombre").focus()

    field.addEventListener("blur", () => {
        esPaisValido = paises.some((element) => element.label === field.value);
        if (!esPaisValido) {
            field.value = ""
            $pais_value = document.getElementById("pais_value");
            $pais_value.setAttribute("value", '');
        }

        //isClicked=false;

    })

 
</script>

<style>
    .form-switch {
        transform: scale(.9);
        flex-direction: column;
        margin-top: 1px;
        padding: 0px 7px;
    }

    .form-check-label {
        padding-bottom: 15px;
    }

    .bordergrey {
        border: solid 1px #d9cdcd;
        padding: 10px 15px;
        border-radius: 3px;

    }

    .form-switch-horizonal {
        flex-direction: row;
    }

    .form-check-label-horizonal {
        padding-bottom: 2px !important;
    }

    main.admin {
        padding-top: 0px;
    }
</style>