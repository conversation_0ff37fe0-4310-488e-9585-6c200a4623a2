<?php 


// POST
function subirFotosParaVerificar($opt = []){

    $uploadFiles = $_FILES['fotos']['size'][0] > 0 ? true : false;

    global $pdo;
    $path = $_SERVER['DOCUMENT_ROOT'] . '/' . app\Config::$uploads . '/fotos/';
    $num_files = count($_FILES['fotos']['tmp_name']);
    $id_anuncio = isset($opt['id']) ? $opt['id'] : $_POST['id'];
    $fotos_existentes = getFotos(["id"=>$id_anuncio]);

    $sql_ordenar = "UPDATE fotos SET pos=pos + $num_files WHERE id_anuncio=$id_anuncio";
    $pdo->query($sql_ordenar);

    if ($uploadFiles) {
        for ($i = 0; $i < $num_files; $i++) {

            $type = $_FILES['fotos']['type'][$i];
            $size = $_FILES['fotos']['size'][$i];

            if ($type != 'image/jpeg') {
                msgbar('Formato de archivo no permitido');
            }

            if ($size > 5000000) { // 5mb = 5000000 | 10mb = 10000000
                msgbar('El archivo es demasiado grande');
            }


            $filename = $id_anuncio . '_' . $i . '_'. date('Ymd_His') . ".jpg";

            $fullpath = $path . $filename;

            if (move_uploaded_file($_FILES['fotos']['tmp_name'][$i], $fullpath)) {
                msgbar("El fichero es válido y se subió con éxito");
            } else {
                msgbar("¡Posible error");
            }
            // Subida de todas las fotos a la tabla fotos

            resizeImage(app\Config::$uploads . "/fotos/$filename",app\Config::$uploads . "/fotos/$filename",700,700);

            $sql_fotos = "INSERT INTO fotos VALUES(0, $id_anuncio, '0', '$filename', $i+1, 0) ";
            $pdo->query($sql_fotos);
        }
    }

    actualizarPortada(["id"=>$id_anuncio]);
  //  app\redirect("/panel/galeria/$id_anuncio/&step=2&edit");

    return [$_POST, $_FILES, $fotos_existentes];
}

 

function videoSubirParaRevisar($opt = []){

    $id = $opt['id'];
    $pathVideo = $_SERVER['DOCUMENT_ROOT'] . '/' . app\Config::$uploads . '/videos/' . $id . ".mp4";
    $sql = "UPDATE anuncios SET videoAprobado=0 WHERE id=$id";
 
    // Validar el tamaño del servidor
    $server_limit = (intval(ini_get('post_max_size')) < 100 || intval(ini_get('upload_max_filesize')) < 100);  
    if ($server_limit) {
        showAlerts("El servidor no permite subir archivos mayores a 100mb");
        return false;
    }

    // Validar el tipo de archivo
    $type = $_FILES['video']['type'];   
    if ($type != "video/mp4") {
        showAlerts('El formato no es permitido');
        return false;
    }

    // Detectar el tamaño del video subido
    $size = $_FILES['video']['size']; 
    if ($size > 100000000) { // 100mb = 100000000
        showAlerts('El archivo es demasiado grande');
        unlink($_FILES['video']['tmp_name']); // Elimina el archivo temporal
        return false;
    }

    // Subir el video
    if (move_uploaded_file($_FILES['video']['tmp_name'], $pathVideo)) {
        showAlerts("El fichero es válido y se subió con éxito");
    } else {
        showAlerts("¡Posible error");
        unlink($_FILES['video']['tmp_name']); // Elimina el archivo temporal
        return false;
    } 

    // Actualizar el estado del video
    flex()->query($sql); 
    return true;
 
 }

 

function borrarFotoPorVerificar($opt = []){
    global $pdo;
    $id = $opt['id'];
    $select = "SELECT * FROM fotos WHERE id = $id";
    $foto = $pdo->query($select)->fetch();
    $id_anuncio = $foto->id_anuncio;
    // $num_files = $pdo->query("SELECT COUNT(*) FROM fotos WHERE id_anuncio = $id_anuncio")->fetchColumn();
    $pos = $foto->pos;

    // Eliminar archivo
    if(file_exists("uploads/fotos/$foto->filename")){
        msgbar("foto $foto->filename ha sido eliminada");
        unlink("uploads/fotos/$foto->filename");
    }else{
        msgbar("No se pudo borrar la foto");
    }
    // Eliminar Registro db de la foto
    $sql = "DELETE from fotos WHERE id = $id";
    $pdo->query($sql);

    // Reordenar las fotos
    $sql_ordenar = "UPDATE fotos SET pos=pos - 1 WHERE id_anuncio=$id_anuncio AND pos > $pos";
    $pdo->query($sql_ordenar);

    actualizarPortada(["id"=>$id_anuncio]);
   // app\redirect("/panel/galeria/$id_anuncio/&step=2&edit");
    return [$sql, $foto->filename];

}

function getFotosPorVerificar($opt){
    global $pdo;
    $id=$opt['id'];
    $fotos = $pdo->query("SELECT * FROM fotos WHERE id_anuncio=$id AND estado=0 ORDER BY pos ASC")->fetchAll();
    return $fotos;
}


function reordenarFotos($opt = []){ 
    $id_anuncio = $opt['id_anuncio'];
    $id_foto = $opt['id_foto'];
    $pos = $opt['pos'];
    
    $sql_ordenar = "UPDATE fotos SET pos=pos + 1 WHERE id_anuncio=$id_anuncio AND pos < $pos";
    $sql_primero = "UPDATE fotos SET pos=1 WHERE id=$id_foto";
    flex()->query($sql_ordenar);
    flex()->query($sql_primero);
    actualizarPortada(["id"=>$id_anuncio]);   
    return ["sql"=>[$sql_ordenar, $sql_primero],"data"=>$opt];
}

function eliminarFoto($opt = []){
    $id = $opt['id'];
    $select = "SELECT * FROM fotos WHERE id = $id"; 
    $foto = flex()->query($select)->fetch();
    $id_anuncio = $foto->id_anuncio;
    // $num_files = $pdo->query("SELECT COUNT(*) FROM fotos WHERE id_anuncio = $id_anuncio")->fetchColumn();
    $pos = $foto->pos;

    // Eliminar archivo
    if(file_exists("uploads/fotos/$foto->filename")){
        msgbar("foto $foto->filename ha sido eliminada");
        unlink("uploads/fotos/$foto->filename");
    }else{
        msgbar("No se pudo borrar la foto");
    }
    // Eliminar Registro db de la foto
    $sql = "DELETE from fotos WHERE id = $id";
    flex()->query($sql);

    // Reordenar las fotos
    $sql_ordenar = "UPDATE fotos SET pos=pos - 1 WHERE id_anuncio=$id_anuncio AND pos > $pos";
    flex()->query($sql_ordenar);

    actualizarPortada(["id"=>$id_anuncio]); 
    return [$sql, $foto->filename];
}

function destacarFotoPanel($opt = []){
    $id = $opt['id'];
    $id_anuncio = $opt['id_anuncio'];
    $set = $opt['set']; // 1 on 0 off

    $sql = "UPDATE fotos SET destacado = $set WHERE id = $id";

    $fotosSeleccionadas = flex()->query("SELECT * FROM fotos WHERE id_anuncio = $id_anuncio AND destacado = 1")->fetchAll();

    if ($set == 1 and count($fotosSeleccionadas) >= 2) {
        echo json_encode(["message" => "Limite de fotos superado"]);
        http_response_code(401);
        exit;
    }

    try {
        flex()->query($sql);
        actualizarPortada(["id"=>$id_anuncio]);
        // pushMessage("Foto $id seleccionada total ".  ++$fotosSeleccionadas, $tipo = "success");
    } catch (Exception $e) {
        // pushMessage("Ocurrio un error al seleccionar la foto $id", $tipo = "error");
    }

    echo json_encode(["message" => "ok"]);

   // return ["sql"=>$sql];
}

function destacarFotoById($opt = []){
    $anuncio = $opt['anuncio'];
     $id = $opt['id'];
     $set = $opt['set'];
     $sql = "UPDATE fotos SET destacado = $set WHERE id = $id";

     // Contando el numero de fotos destacadas para asegurarnos que no tenga ya dos seleccionadas
     $fotosSeleccionadas = flex()->query("SELECT * FROM fotos WHERE id_anuncio = $anuncio AND destacado = 1")->fetchAll();  
     $limite = $set == 1 && count($fotosSeleccionadas) >= 2 ? "Solo se pueden seleccionar 2 fotos destacadas" : false;

     if ($limite){
        echo '<script>toast("' . $limite . '"); setTimeout(function(){ window.location.reload(); }, 1000);</script>';
        return;
     } 
     flex()->query($sql); 
}

