<?php 

$id_anuncio = flex()->uri(-1); 

$fn = [
   "1" => "&fun[]=anuncioActualizar",
   "2" => "&fun[]=galeria",
   "3" => "&fun[]=textos",
   "4" => "&fun[]=setHorario",
   "5" => "&fun[]=setCoords"
];

?>

</div> <!-- .container -->
   <div class="footer"> 
       <div class="contenido">

   <?php 

   if(isset($_GET["step"])){

       include "includes/footer_panel_menu_steps.php"; 
   } 

   if(isset($_GET["edit"])){
       include "includes/footer_panel_menu_edit.php"; 
   } 

   if (!isset($_GET["step"]) && !isset($_GET["edit"])):?>
   
               </div> <!-- .contenido --> 
           </div> <!-- footer --> 
       </div> <!-- .main que agrupa un posible sidebar y container -->
   
   <?php endif ?>

<script>
   /*
function addTagToLinks() {

const a = document.querySelectorAll('a[href*="&fun"]');
a.forEach(el => el.setAttribute("href", el.getAttribute('href') + '&tag=a'))

const submit = document.querySelectorAll('form[action*="&fun"]');
submit.forEach(el => el.setAttribute("action", el.getAttribute('action') + '&tag=submit'))

}
addTagToLinks();
*/
</script>