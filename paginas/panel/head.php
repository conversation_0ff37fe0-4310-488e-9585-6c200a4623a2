<!DOCTYPE html>
<html lang="es">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" /> 
 <!-- Google Tag Manager or NO ROBOTS-->  
    <meta name="robots" content="noindex"> 
    <meta name="viewport" content="width=device-width, initial-scale=1.0"> 
    <!-- Etiqueta canonical -->
    
    <title> Panel</title>
  
    <!-- LIBRERIAS --> 
    <script src="/js/<EMAIL>"></script> 
    <script src="/js/surreal.js"></script>
    <script src="/js/autocomplete.js"></script>
  
    <!-- BOOTSTRAP 5 (Admin & portada ) -->
    <link href="/css/bootstrap.min.css" rel="stylesheet">
    <script src="/js/bootstrap.bundle.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"/>
    <script src="https://unpkg.com/htmx.org@1.9.12"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/choices.js@9.0.1/public/assets/styles/choices.min.css">
    <!-- Choices.js JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/choices.js@9.0.1/public/assets/scripts/choices.min.js"></script>
 
    <link rel="stylesheet" href="/css/loading.css"/>
  <!-- MI PROPIO CODIGO -->
  <link rel="stylesheet" href="/css/panel.css?rel=<?=time()?>"/>

    <!-- tapar cara -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.6.2/cropper.min.js" integrity="sha512-JyCZjCOZoyeQZSd5+YEAcFgz2fowJ1F1hyJOXgtKu4llIa0KneLcidn5bwfutiehUTiOuK87A986BZJMko0eWQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.6.2/cropper.css" integrity="sha512-087vysR/jM0N5cp13Vlp+ZF9wx6tKbvJLwPO8Iit6J7R+n7uIMMjg37dEgexOshDmDITHYY5useeSmfD1MYiQA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <script>
      

function toast(msg, tipo){ 
   (async ()=>{
       await Swal.fire({
           html: msg,   
           toast: true,
           timer: 2000,
           position: 'center',
           showConfirmButton: false,
           icon: tipo}
       )})(); 
 }
 
    </script>




</head>
<body>

<div class="main" style="padding-top:70px"> 
   <div class="container-fluid">

