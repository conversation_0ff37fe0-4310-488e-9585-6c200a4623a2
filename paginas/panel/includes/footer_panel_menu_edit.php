 
<div class="bolas">
    <div data="1" class="pasobtn <?=flex()->view == 'panel/detalle' ? 'superado' : '' ?>">
        <div class="bola step1">1</div>
        <div class="boton2">
            <i class="fa-solid fa-address-card fa-xl"></i>
            <span>Detalle</span>
        </div>
    </div>
    <div data="2" class="pasobtn<?=flex()->view == 'panel/galeria' ? ' superado' : '' ?>">
        <div class="bola step2">2</div>
        <div class="boton2">
            <i class="fa-regular fa-images fa-xl"></i>
            <span>Galeria</span>
        </div>
    </div>
    <div data="3" class="pasobtn<?=flex()->view == 'panel/textos' ? ' superado' : '' ?>">
        <div class="bola step3">3</div>
        <div class="boton2">
            <i class="fa-brands fa-readme fa-xl"></i>
            <span>Textos</span>
        </div>
    </div>

    <div data="4" class="pasobtn<?=flex()->view == 'panel/horario' ? ' superado' : ''?>">
        <div class="bola step4">4</div>
        <div class="boton2">
            <i class="fa-regular fa-clock fa-xl"></i>
            <span>Horario</span>
        </div>
    </div>
    <div data="5" class="pasobtn<?=flex()->view == 'panel/mapa' ? ' superado' : ''?>">
        <div class="bola step5">5</div>
        <div class="boton2">
            <i class="fa-solid fa-location-dot fa-xl"></i>
            <span>Mapa</span>
        </div>
    </div>
</div> <!-- .bolas -->
 

<script> 

   
   
    const fn = {
        "1": "&fun[]=anuncioActualizar",
        "2": "",
        "3": "&fun[]=textos",
        "4": "&fun[]=setHorario",
        "5": "&fun[]=setCoords"
    }
    const steps = {
        "1": "detalle",
        "2": "galeria",
        "3": "textos",
        "4": "horario",
        "5": "mapa"
    };

      // Obtener el key del array steps por el valor de la propiedad 
      const step = Object.keys(steps).find(key => steps[key] == '<?=flex()->view?>'); 
       
 
        /* --------------------------------- SI ES ES UNA EDICIÓN SIN SER PASO A PASO --------------------------------- */
    
    
        
     // -------- Submit and redirection clic pasos botones - /panel/galeria/2&fun[]=anuncioActualizar&fun[]=redirect(url:/panel/detalle/2@edit)
     any(".pasobtn").on('click', ev => {
        // actualizamos el submit al elemento clickeado
        let destino = ev.currentTarget.getAttribute("data");
        let action = `<?= flex()->path?>${fn[step]}&fun[]=redirect(url:/panel/${steps[destino]}/<?= $id_anuncio ?>/@edit)`;
    
        me('.panel').setAttribute("action", action); 
      //  console.log(action);  
        me(".panel").submit();
    });

    

</script>


<style>
    .boton2 {
        padding-top: 12px;
        display: flex;
        height: 50px;
        flex-direction: column;
        justify-content: space-around;
        font-size: 0.9rem;
        width: 60px;
        position: relative;
        left: -13px;
        margin-top: 7px;
        cursor: pointer;
    }

    .activa .boton2 {
        background-color: #E3E9FF;
        border-radius: 5px;
        border: solid 1px #819CFF;
    }

    .boton2:hover {
        background-color: #F3F5FC;
        border-radius: 5px;
        border: solid 1px #819CFF;
    }

    .bolas .pasobtn {
        text-align: center;
        text-decoration: none;
    }

    .bolas .activa span {
        font-weight: 600;

    }


    .footer {

        position: fixed;
        background-color: white;
        width: 100%;
        margin: 0 auto;
        bottom: 60px;
        border: solid 1px #B0C2FF;
        box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
        padding-bottom: 10px;

    }

    .bolas {
        display: flex;
        justify-content: space-around;
        width: 99%;
        position: relative;
        margin: 0 auto;
        left: 2%;
        top: 2px;

    }


    .bolas i {
        color: #C9C9C9;
    }

    .bolas .superado i {
        color: #0d6efd;
    }

    .bolas span {
        color: #8F8F8F;
        padding: 1px 3px;
    }

    .bolas .superado span {
        color: #0d6efd;
    }

    .bola {
        display:none;
        color: white;
        text-align: center;
        line-height: 1.9rem;
        font-weight: normal;
        font-size: 1.2rem;
        width: 30px;
        height: 30px;
        border-radius: 30px;
    }

    .bola {
        background-color: #C9C9C9;
        cursor: pointer;
    }

    .bola:hover {
        background-color: #8AA4FF;
    }

    .superado .bola {
        background-color: #0d6efd;
    }

    .paso {
        color: #0d6efd;
        ;
        width: 100%;
        margin: 0 auto;
        text-align: center;
        padding: 4px;
        font-size: 1.3rem;
    }

    .progress {
        --bs-progress-height: 0.3rem;
        position: relative;
        top: 20px;
        width: 80%;
        margin: 0 auto;
        text-align: center;
        overflow: visible;
    }

    .footer .contenido {
        max-width: 400px;
        margin: 0 auto;
    }
</style>



</body>

</html>