
<?php

$id = flex()->uri(-1);

    $fotos = getfun("getFotos(\"id\":$id)");
    $anuncio = getfun("getAnuncio(\"id\":$id)")["data"];
    
    $fotosporverificar = getfun("getFotosPorVerificar(id:$id)");

?>

<!-- form falso -->
<form class="panel" method="POST"></form>

<!-- Notificación de Bootstrap 5 -->
<div class="alert alert-warning alert-dismissible fade show text-center m-auto mb-3" style="max-width: 960px" role="alert">
    OPCIONES DEL ANUNCIO CONTRATADO: <hr><strong>Limite de fotos: </strong> <?=$anuncio->fotos?> </br> <strong> Soporte de video: </strong> <?=$anuncio->videoContratado ? 'Si' : 'NO' ?>
</div>
<div class="alert alert-info m-auto pb-0" style="max-width: 960px" role="alert">
    <ul style="margin-left: 5px">
        <li>Las fotos y videos se moderarán para pasar a la sección de verificados</li>
        <li>Si supera el límite de fotos, se ocultara las últimas fotos.</li>
        <li>Para aumentar límites o contratar video, consulte con soporte.</li>
    </ul>
</div>



<img id="spinner" class="htmx-indicator" src="/files/spinner1.svg" />

<!-- GALERIA DEF FOTOS DEL ANUNCIO -->
<div class="row mt-2 justify-content-center" style="max-width: 960px; margin: 0 auto; border: solid 1px gainsboro; box-shadow: 1px 2px 2px gainsboro">

    <p class="mt-2 text-center">Su anuncio tiene <?=count($fotos)?> Fotos aprobadas </p>

    <?php if(is_array($fotos)):

        foreach ($fotos as $foto) :

            include "paginas/panel/galeria_modal_taparcara.php";
            ?>
        <div class="col-3 text-center metabox m-2" style="width: 200px;">
        <div class="cabecera d-flex justify-content-between">
            <a href="/panel/x/galeria/anuncio/<?=$foto->id_anuncio?>/foto/<?=$foto->id?>/pos/<?=$foto->pos?>" hx-swap="none" hx-post="/panel/x/galeria/anuncio/<?=$foto->id_anuncio?>/foto/<?=$foto->id?>/pos/<?=$foto->pos?>"><i class="bi bi-upload"></i></a>
            <span class=""><?= $foto->pos ?> </span>
            <a href="/panel/x/galeria/anuncio/<?=$foto->id_anuncio?>/foto/<?=$foto->id?>/pos/<?=$foto->pos?>" hx-swap="none" hx-post="/panel/x/galeria/foto/<?=$foto->id?>/eliminar"><i class="bi bi-x-lg"></i></a>
        </div>


        <div class="content d-flex justify-content-center flex-column align-items-center" style="height: 150px; border: solid 1px #d5d5d5">
            <img style="max-width: 140px; max-height: 140px" src="/uploads/fotos/<?=$foto->filename?>?rel=<?=time()?>"/>
        </div>



        <!--
                <input class="destacado" onchange='destacadoFoto(<?="$foto->id, $foto->id_anuncio"?>)' name="destacado" id="destacado<?= $foto->id ?>" <?= $foto->destacado ? 'checked' : '' ?> type="checkbox">
                <label for="destacado<?= $foto->id ?>">Destacado</label> <br>
        -->

        <!-- Destacar foto -->
        <div class="row justify-content-start align-items-end">
                    <div class="col">
                        <div class="form-check form-switch">
                            <label class="form-check-label me-4" for="destacado"> Destacar </label>
                            <input class="form-check-input" onchange='destacadoFoto(<?="$foto->id, $foto->id_anuncio"?>)' type="checkbox" id="destacado<?=$foto->id?>" name="destacado"
                                <?=@$foto->destacado ? 'checked' : ''?>>
                        </div>
                    </div>
        </div> <!-- destacar foto-->

          <!-- Destacar foto -->
          <div class="row justify-content-start align-items-end">
                    <div class="col">
                        <div class="form-check form-switch">
                            <label class="form-check-label me-4" for="tapar<?=$foto->id?>"> Tapar cara </label>
                            <input class="form-check-input"
                                   hx-post="/panel/x/galeria/fotoid/<?=$foto->id?>/filename/<?=$foto->filename?>/tapar"
                                   hx-swap="none"
                                   hx-indicator="#spinner"
                                   type="checkbox" id="tapar<?=$foto->id?>" name="tapar"
                                <?=file_exists("uploads/fotos/$foto->filename.bk") ? 'checked' : ''?>>
                        </div>
                    </div>
        </div> <!-- destacar foto-->

        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#taparcara<?=$foto->id?>">Tapar cara</button>


        </div><!-- col foto -->
        <?php endforeach;

    endif;
    ?>
 </div>


<!-- GALERIA DEF FOTOS DEL ANUNCIO -->
<div class="row mt-4 pb-4 justify-content-center" style="max-width: 960px; margin: 0 auto; border: solid 1px gainsboro; box-shadow: 1px 2px 2px gainsboro">
    <?php if( count($fotosporverificar) ): ?>

    <div class="col-12 mx-auto mt-4">
        <div class="col-6 text-center mx-auto">
            <div class="alert alert-danger text-center" role="alert">
                Fotos nuevas pendientes de aprobar
            </div>
        </div>
    </div>

            <?php foreach ($fotosporverificar as $foto) : ?>
                <div class="col-3 text-center metabox m-2" style="width: 200px;">
                    <div class="content d-flex justify-content-center flex-column align-items-center" style="height: 150px; border: solid 1px #d5d5d5">
                        <img style="max-width: 140px; max-height: 140px" src="/uploads/fotos/<?= $foto->filename?>" alt="">
                    </div>
                        <a type="button" class="btn btn-danger mt-2" href='&fun=borrarFotoPorVerificar("id":<?=$foto->id?>)'>Borrar</a>

                </div><!-- col foto -->
            <?php endforeach; ?>

        <?php else: ?>

        <div class="col-12 mx-auto mt-4">
            <div class="col-6 text-center mx-auto">
                <div class="alert alert-warning text-center" role="alert">
                    No hay fotos pendientes de verificar
                </div>
            </div>
        </div>

    <?php endif;?>

    <div class="row mt-3">

        <div class="col-4 m-auto">
            <div class="d-grid gap-2">
                <!-- Button trigger modal -->
                <button type="button" class="btn btn-success d-block" data-bs-toggle="modal" data-bs-target="#agregarfotos">
                    Agregar fotos
                </button>
            </div>
        </div>

    </div> <!-- row -->

</div>


<?php if ($anuncio->videoContratado): ?>

    <div class="row mt-4 pb-4 justify-content-center" style="max-width: 960px; margin: 0 auto; border: solid 1px gainsboro; box-shadow: 1px 2px 2px gainsboro">

        <div class="col-6">
            <p class="mt-2 text-center">Video</p>


            <?php if($anuncio->videoAprobado):?>
                <div class="alert alert-info text-center" role="alert">
                    El video esta aprobado
                </div>
            <?php else: ?>
                <div class="alert alert-danger text-center" role="alert">
                    El video esta pendiente de ser revisado
                </div>
            <?php endif;?>

            <div class="text-center">
                <video style="max-width: 400px; max-height: 400px;" controls src="//<?=$_SERVER['SERVER_NAME']?>/uploads/videos/<?=$id?>.mp4"></video>
            </div>
        </div>



        <!--  VIDEO -->
        <div class="col-11 col-lg-6 metabox pt-3 m-2" style="position: relative">
            <form action='&fun=videoSubirParaRevisar("id":<?=$id ?>)' enctype='multipart/form-data' method='POST'>

                <?php if( videoExiste($id) ):?>
                    <div class="alert alert-warning w-100 text-center" role="alert">
                        Cambiar el video sobreescribira el actual. No se mostrara ningun video hasta su revisión.
                    </div>
                <?php else: ?>
                    <div class="alert alert-info w-75 text-center" role="alert">
                        Ningun video subido, suba su primer video para revisión
                    </div>
                <?php endif;?>

                <input type="file" class="form-control" name="video" id="video">
                <!-- CHECKBOX ACTIVAR VIDEO -->
                <div class="col mt-3 mb-2 justify-content-between">
                    <div class="col-6 mx-auto text-center">
                            <button type="submit" class="btn btn-success d-block mx-auto"><?=videoExiste($id) ? 'Cambiar video' : 'Subir video'?></button>

                    </div>

                </div>
            </form>
        </div> <!-- col -->

    </div> <!-- row  -->

<?php endif; ?>




<!-- Modal -->
<div class="modal fade" id="agregarfotos" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="agregarfotos" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action='&fun=subirFotosParaVerificar()' enctype='multipart/form-data' method='POST'>
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="agregarfotos">Agregar fotos</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">

                    <!-- AGREGAR FOTOS -->
                    <div class="metabox m-2 pt-3 pb-2">
                            <input type="hidden"  name="id" value="<?=$id?>">
                            <input type="file" class="form-control" name="fotos[]" id="fotos" multiple>

                    </div>  <!-- col -->
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-success">Aceptar</button>
                </div>
            </form>
        </div>
    </div>
</div>



<script>

 async function ajax(id){
    const data = new FormData();
    data.append('id', id);
    console.log(`click hecho en ${id}`)
    const response = await fetch(`&api&fun=ajax()`, {
        method: "POST",
        body: data
    } );
    const content = await response.json();
    console.log(content);
 }

 async function taparCara(filename){

     /*
    const data = new FormData();
    data.append('filename', filename);
    url = `&api&fun=taparCara()`;
    console.log(filename);
    try {
         const response = await fetch(url, {
            method: 'POST',
            body: data
        })
         const json = await response.json();


     } catch (err) {
         console.log(err);

     }
*/

 }




 function reload(){


   //  app\redirect("/panel/galeria/$id_anuncio/&step=2&edit");
 }

 async function destacadoFoto(id, id_anuncio) {
    el = this.event.currentTarget;
    const set = this.event.currentTarget.checked ? 1 : 0;
    const data = new FormData();
    data.append('id', id);
    data.append('set', set);
    console.log("destacadoFoto",id,set);
    url = `&api&fun=destacadoFoto()`;
     try {
         const response = await fetch(url, {
            method: 'POST',
            body: data
        })
         const json = await response.json();
         if(response.status != 200){
            toast("Superado el limite de 2 fotos, deseleccione primero")
            el.click();
         }


     } catch (err) {
         console.log(err);


     }
   // location.reload();
 }


</script>

    <style>
        .container-fluid{
            position: relative;
            top: -50px;
            padding-bottom: 200px;
        }

        .htmx-indicator{
            display:none;
            position: absolute;
            z-index: 999;
            color: white;
            background: rgba(165, 42, 42, 0.53);
            opacity: 0.3;
            position: absolute;
            top: 0;
            left: 0;
            padding:calc(40vh) calc(40vw);
            width: 100vw;
            height: 100%;
        }


        .htmx-request .htmx-indicator{
            display:block;
        }
        .htmx-request.htmx-indicator{
            display:block;
        }


    </style>

