
<?php 
  if(isset($_POST["email"]) && isset($_POST["tel"])){
    
    $email = $_POST["email"];
    $tel = $_POST["tel"];

    $usuario = getfun("getUsuario(\"email\":\"$email\")")["data"];

    if(!$usuario){   
        $create = getfun("userCreate(\"email\":\"$email\",\"tel\":\"$tel\")");
        $id = $create["data"]["id"];
        if ($create){ 
            flex()->redirect("/panel/entrar-codigo/&email=$email&registro");
        }
    }else{
      $_SESSION["error"] = 
      "
        <div class='alert alert-danger' role='alert'> 
          Ya existe una cuenta con el correo $email,
        </div>  
        <div class='d-grid text-center'><br> 
          <a class='btn btn-secondary mx-auto' href='/panel/entrar'> acceda a su cuenta</a> 
        </div>
      ";
    } 
  }
?>

<div class="vh-100 d-flex justify-content-center align-items-center">
  <div class="container">
    <div class="row d-flex justify-content-center">
      <div class="col-12 col-md-8 col-lg-5">
        <div class="border border-3 border-danger"></div>
        <div class="card bg-white">
          <div class="card-body p-5">

            <form class="mb-3 mt-md-4" action="" method="POST">
              <!-- <h2 class="mb-2 text-uppercase">Citas</h2> -->
              <h2 class=" mb-5">Registrese</h2>

              <?php if( flex()->user->rol == 0):?>
             
                       <?= $_SESSION["error"] ?? "" ?>
              
                      <div class="mb-3">
                        <label for="email" class="form-label ">Email</label>
                        <input type="text"  class="form-control mb-4" required name="email" id="email" placeholder="Su email">
                        <div id="response"></div> 
                        <label for="tel" class="form-label ">Teléfono</label>
                        <input type="number" class="form-control mb-4" required name="tel" id="tel" placeholder="Número de teléfono">
                    
                    </div>
                    <div class="d-grid">
                      <button class="btn btn-danger mb-3" type="submit">Registrarse</button>
                    
                    </form>
                  

                    <div>
                      
                      <p class="mb-0 text-center">¿Ya tiene una cuenta? <a href="/panel/entrar" class="text-primary fw-bold"> Acceda</a></p>
                    </div>
            <?php else: ?>
                <p class="mb-0 text-center">Ya has iniciado sesión, <a href="/panel/inicio" class="text-primary fw-bold"> accede</a></p><br>
                <p> O también puedes <a href="<?="$flex->path&fun[]=cerrarSesion&fun[]=redirect(url:/panel/entrar)"?>" class="text-danger fw-bold"> Cerrar sesión</a></p>
            <?php endif; ?>




          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .main {
    background-color: black !important;
  }

  .card {
    border-radius: 0px 0px 5px 5px;
  }

  h2 {
    color: black;
    text-align: center;
  }

  @media (min-width: 1200px) {

    .p-xl-4 {
      padding: 0px !important;
    }
  }
</style>

<?php
unset($_SESSION["error"]);
?>