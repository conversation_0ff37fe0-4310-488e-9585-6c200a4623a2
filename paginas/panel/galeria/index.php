
<?php
 
  
$id = flex()->uri(-1);

    $fotos = getfun("getFotos(\"id\":$id)");
    $anuncio = getfun("getAnuncio(\"id\":$id)")["data"];
    
    $fotosporverificar = getfun("getFotosPorVerificar(id:$id)");

    // Modal para agregar fotos 
    include("includes/galeriaModalAgregarFotos.php");
?>
 
 <!-- form falso necesario para los steps y realizar la redireccion (solo en galeria) -->
<form class="panel" action="" method="POST"></form>

<!-- Notificación de Bootstrap 5 -->
<div class="alert alert-warning alert-dismissible fade show text-center m-auto mb-3" style="max-width: 960px" role="alert">
    OPCIONES DEL ANUNCIO CONTRATADO: <hr><strong>Limite de fotos: </strong> <?=$anuncio->fotos?> </br> <strong> Soporte de video: </strong> <?=$anuncio->videoContratado ? 'Si' : 'NO' ?>
</div>
<div class="alert alert-info m-auto pb-0" style="max-width: 960px" role="alert">
    <ul style="margin-left: 5px">
        <li>Las fotos y videos se moderarán para pasar a la sección de verificados</li>
        <li>Si supera el límite de fotos, se ocultara las últimas fotos.</li>
        <li>Para aumentar límites o contratar video, consulte con soporte.</li>
    </ul>
</div>

 
<!-- GALERIA DEF FOTOS DEL ANUNCIO -->
<div class="row mt-2 justify-content-center" style="max-width: 960px; margin: 0 auto; border: solid 1px gainsboro; box-shadow: 1px 2px 2px gainsboro">

    <p class="mt-2 text-center">Su anuncio tiene <?=count($fotos)?> Fotos aprobadas </p>

    <?php if(is_array($fotos)):

        foreach ($fotos as $foto) :
            ?>
        <div class="col-3 text-center metabox m-2" style="width: 200px;">
        <div class="cabecera d-flex justify-content-between">
            <a href="&fun[]=reordenarFotos(id_anuncio:<?=$foto->id_anuncio?>,id_foto:<?=$foto->id?>,pos:<?=$foto->pos?>)&fun[]=redirect()"><i class="bi bi-upload"></i></a>
            <span class=""><?= $foto->pos ?> </span>
            <a href="&fun[]=eliminarFoto(id:<?="$foto->id,id_anuncio:$foto->id_anuncio,pos:$foto->pos"?>)&fun[]=redirect()"><i class="bi bi-x-lg"></i></a>
        </div>


        <div class="content d-flex justify-content-center flex-column align-items-center" style="height: 150px; border: solid 1px #d5d5d5">
            <img style="max-width: 140px; max-height: 140px" src="/uploads/fotos/<?=$foto->filename?>?rel=<?=time()?>"/>
        </div>


        <!-- Destacar foto -->
        <div class="row justify-content-start align-items-end">
                    <div class="col">
                        <div class="form-check form-switch">
                            <label class="form-check-label me-4" for="destacado"> Destacar </label>
                            <input class="form-check-input" hx-post="&fun[]=destacarFotoById(anuncio:<?=$id?>,id:<?=$foto->id?>,set:<?=$foto->destacado ? 0 : 1 ?>)&x&action" type="checkbox" id="destacado<?=$foto->id?>" name="destacado"
                                <?=@$foto->destacado ? 'checked' : ''?>>
                        </div>
                    </div>
        </div> <!-- destacar foto-->

        <a class="btn btn-success" href="<?="/panel/galeria-taparcara/id/$id/fotoid/$foto->id/file/$foto->filename"?>">Tapar cara</a>


        </div><!-- col foto -->
        <?php endforeach;

    endif;
    ?>
 </div>


<!-- GALERIA DEF FOTOS DEL ANUNCIO -->
<div class="row mt-4 pb-4 justify-content-center" style="max-width: 960px; margin: 0 auto; border: solid 1px gainsboro; box-shadow: 1px 2px 2px gainsboro">
    <?php if( count($fotosporverificar) ): ?>

    <div class="col-12 mx-auto mt-4">
        <div class="col-6 text-center mx-auto">
            <div class="alert alert-danger text-center" role="alert">
                Fotos nuevas pendientes de aprobar
            </div>
        </div>
    </div>

            <?php foreach ($fotosporverificar as $foto) : ?>
                <div class="col-3 text-center metabox m-2" style="width: 200px;">
                    <div class="content d-flex justify-content-center flex-column align-items-center" style="height: 150px; border: solid 1px #d5d5d5">
                        <img style="max-width: 140px; max-height: 140px" src="/uploads/fotos/<?= $foto->filename?>" alt="">
                    </div>
                        <a type="button" class="btn btn-danger mt-2" href='&fun[]=borrarFotoPorVerificar(id:<?=$foto->id?>)&fun[]=redirect()'>Borrar</a>

                </div><!-- col foto -->
            <?php endforeach; ?>

        <?php else: ?>

        <div class="col-12 mx-auto mt-4">
            <div class="col-6 text-center mx-auto">
                <div class="alert alert-warning text-center" role="alert">
                    No hay fotos pendientes de verificar
                </div>
            </div>
        </div>

    <?php endif;?>

    <div class="row mt-3">

        <div class="col-4 m-auto">
            <div class="d-grid gap-2">
                <!-- Button trigger modal -->
                <button type="button" class="btn btn-primary d-block" data-bs-toggle="modal" data-bs-target="#agregarfotos">
                    Agregar fotos
                </button>
                <a href="/panel/inicio" class="btn btn-success d-block mt-4">
                    Guardar y salir
                </a>
            </div>
        </div>

    </div> <!-- row -->

</div>


<?php if ($anuncio->videoContratado): ?>

    <div class="row mt-4 pb-4 justify-content-center" style="max-width: 960px; margin: 0 auto; border: solid 1px gainsboro; box-shadow: 1px 2px 2px gainsboro">

        <div class="col-6">
            <p class="mt-2 text-center">Video</p>


            <?php if($anuncio->videoAprobado):?>
                <div class="alert alert-info text-center" role="alert">
                    El video esta aprobado
                </div>
            <?php else: ?>
                    <?php if(videoExiste($id)) : ?>
                        <div class="alert alert-danger text-center" role="alert">
                            El video esta pendiente de ser revisado
                        </div> 
                    <?php endif; ?>
            <?php endif; ?>

            <div class="text-center">
                <?php if(videoExiste($id)) : ?>
                    <video style="max-width: 400px; max-height: 400px;" controls src="//<?=$_SERVER['SERVER_NAME']?>/uploads/videos/<?=$id?>.mp4"></video>
                <?php endif; ?>
            </div>
        </div>



        <!--  VIDEO (ASEGURATE DE CONFIGURAR EN SERVIDOR upload_max_filesize y post_max_size en 100mb, por defecto suele ser 8mb) -->
        <div class="col-12 col-lg-6 metabox pt-3 m-2" style="position: relative">
          <!--  <form action='&fun[]=videoSubirParaRevisar(id:<?=$id?>)&x&action' enctype='multipart/form-data' method='POST'> -->
            <form action='&fun[]=videoSubirParaRevisar(id:<?=$id?>)&x&action' enctype='multipart/form-data' method='POST'>

                <?php if( videoExiste($id) ):?>
                    <div class="alert alert-warning w-100 text-center" role="alert">
                        Cambiar el video sobreescribira el actual. No se mostrara ningun video hasta su revisión.
                    </div>
                <?php else: ?>
                    <div class="alert alert-info w-100 text-center" role="alert">
                        Ningun video subido, suba su primer video (100mb max) para revisión
                    </div>
                <?php endif;?> 

                <input type="file" class="form-control" name="video" id="video">
                <!-- CHECKBOX ACTIVAR VIDEO -->
                <div class="col mt-3 mb-2 justify-content-between">
                    <div class="col-6 mx-auto text-center">
                            <button type="submit" class="btn btn-success d-block mx-auto"><?=videoExiste($id) ? 'Cambiar video' : 'Subir video'?></button>

                    </div>

                </div>
            </form>
        </div> <!-- col -->

    </div> <!-- row  -->

<?php endif; ?>
 
    <style>
        .container-fluid{
            position: relative;
            top: -50px;
            padding-bottom: 200px;
        }

        .htmx-indicator{
            display:none;
            position: absolute;
            z-index: 999;
            color: white;
            background: rgba(165, 42, 42, 0.53);
            opacity: 0.3;
            position: absolute;
            top: 0;
            left: 0;
            padding:calc(40vh) calc(40vw);
            width: 100vw;
            height: 100%;
        }


        .htmx-request .htmx-indicator{
            display:block;
        }
        .htmx-request.htmx-indicator{
            display:block;
        }


    </style>

