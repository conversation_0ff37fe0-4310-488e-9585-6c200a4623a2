<?php


$anuncio = $_POST["anuncio"];
$fotoid = $_POST["fotoid"];
// Variables
$filename = $_POST["filename"];
$file_path = file_path($filename);

    echo $file_path;


// Tamaño de la imagen
$imgWidth = intval($_POST["imgWidth"]);
$imgHeight = intval($_POST["imgHeight"]);
// Coordenadas seleccion
$xCoord = intval($_POST["xCoord"]);
$yCoord = intval($_POST["yCoord"]);
$intensidad = intval($_POST["intensidad"] * 2);
$filter = $_POST["filter"];
// Tamaño de la seleccion
$selectWidth = $_POST["selectWidth"];
$selectHeight = $_POST["selectHeight"];

// test si todas las variables tienen el valor
// echo "filename: $filename, file_path: $file_path, imgWidth: $imgWidth, imgHeight: $imgHeight, xCoord: $xCoord, yCoord: $yCoord, intensidad: $intensidad, filter: $filter, selectWidth: $selectWidth, selectHeight: $selectHeight";


$backup_file_path = backup_file_path($filename);


if ( restaurar($file_path, $backup_file_path) ) exit();


/* ------------- FUNCIONES EJECUCIÓN ----------------- */

backup($file_path, $backup_file_path);

$image = recuperarImagen($file_path, $backup_file_path);

tapar_imagen($image, $xCoord, $yCoord, $selectWidth, $selectHeight, $intensidad, $filter);

/* -------------- FUNCTION DECLARATIONS ---------------------- */


function backup($file_path, $backup_file_path) {
    if (!file_exists($backup_file_path)) {
        copy($file_path, $backup_file_path);
    }
}

function restaurar($file_path, $backup_file_path) {
    global $anuncio;
    global $fotoid;
    global $filename;
    if (isset($_POST["restaurar"]) && file_exists($backup_file_path)) {
        unlink($file_path);
        rename($backup_file_path, $file_path);
        backup($file_path, $backup_file_path);
        header("Location: /panel/galeria-taparcara/id/$anuncio/fotoid/$fotoid/file/$filename");
        return true;
    }
    header("Location: /panel/galeria-taparcara/id/$anuncio/fotoid/$fotoid/file/$filename");
    return false;
}

function file_path($filename) {
    return $_SERVER["DOCUMENT_ROOT"]."/uploads/fotos/$filename";
}

function backup_file_path($filename) {
    $name = explode(".", $filename)[0];
    return $_SERVER["DOCUMENT_ROOT"]."/uploads/fotos/$name.bk.jpg";
}

function recuperarImagen($file_path, $backup_file_path) {
    if (file_exists($backup_file_path)) {
        // Usar original
        return imagecreatefromjpeg($backup_file_path);
    }
    return imagecreatefromjpeg($file_path);
}

function tapar_imagen($image, $xCoord, $yCoord, $selectWidth, $selectHeight, $intensidad, $filter) {
    global $anuncio;
    global $filename;
    global $fotoid;
    global $file_path;
    // debugear argumentos
    // echo "xCoord: $xCoord, yCoord: $yCoord, intensidad: $intensidad, filter: $filter";

    // Crear un recorte
    $recorte = imagecrop($image, ['x' => $xCoord, 'y' => $yCoord, 'width' =>$selectWidth, 'height' => $selectHeight]);

    if ($filter == "pixelar") {
        $intensidad = $intensidad * 3;
        imagefilter($recorte, IMG_FILTER_PIXELATE, $intensidad);
    } else {
        //  imagefilter($recorte, IMG_FILTER_PIXELATE, $intensidad + 2);
        $intensidad = $intensidad * 20;

        for ($x = 1; $x <= $intensidad; $x++) {
            imagefilter($recorte, IMG_FILTER_GAUSSIAN_BLUR);
        }

        imagefilter($recorte, IMG_FILTER_SMOOTH, 500);

    }

    // Poner el recorte encima de la imagen de fondo en las posiciones x e y
    imagecopy($image, $recorte, $xCoord, $yCoord, 0, 0, imagesx($recorte), imagesy($recorte));

    // Guardar la imagen de fondo modificada
    imagejpeg($image, $file_path);

    imagedestroy($recorte);
    imagedestroy($image);

  header("Location: /panel/galeria-taparcara/id/$anuncio/fotoid/$fotoid/file/$filename");


}