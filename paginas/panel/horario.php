<?php


    $id = app\uri(2); 
    $horario = getfun("getHorario(\"id\":$id)")["data"]; 
    $rango1 = $horario[0];
    $rango2 = $horario[1];
    $rango3 = $horario[2];

?>

<form id="panel" class="panel" action="&fun[]=setHorario&fun[]=redirect(url:/panel/inicio)" method="POST">

<input type="hidden" name="id" value=<?=$id?>>

<div class="row text-center">
 

    <div class="row horarios text-center">
        <h5 class="mt-2 text-center">Horario de trabajo del anuncio ID: <?=$id?></h5>
        <p>Para indicar 24h coloque un horario de 0h a 0h</p>
        <span>Rango 1</span>
        <div class="col-12 col-xxl-7 text-center text-xxl-end">

            <input type="checkbox" class="form-check-input" name="dias[0][active]" value="true" checked="checked" style="pointer-events:none">

            <!------------------------------------------------------------------  rango 1 los dias -->
            <select class='form-select d-inline-block' name='dias[0][dia_inicio]' style='width:120px'>
                <option value='0' <?= @$rango1[0] == '0' ? 'selected' : "" ?>> Lunes</option>
                <option value='1' <?= @$rango1[0] == '1' ? 'selected' : "" ?>> Martes</option>
                <option value='2' <?= @$rango1[0] == '2' ? 'selected' : "" ?>> Miercoles</option>
                <option value='3' <?= @$rango1[0] == '3' ? 'selected' : "" ?>> Jueves</option>
                <option value='4' <?= @$rango1[0] == '4' ? 'selected' : "" ?>> Viernes</option>
                <option value='5' <?= @$rango1[0] == '5' ? 'selected' : "" ?>> Sabado</option>
                <option value='6' <?= @$rango1[0] == '6' ? 'selected' : "" ?>> Domingo</option>
            </select>




            A:
            <select class='form-select  d-inline-block' name='dias[0][dia_fin]' style='width:120px'>
                <option value='0' <?= @$rango1[1] == '0' ? 'selected' : "" ?>> Lunes</option>
                <option value='1' <?= @$rango1[1] == '1' ? 'selected' : "" ?>> Martes</option>
                <option value='2' <?= @$rango1[1] == '2' ? 'selected' : "" ?>> Miercoles</option>
                <option value='3' <?= @$rango1[1] == '3' ? 'selected' : "" ?>> Jueves</option>
                <option value='4' <?= @$rango1[1] == '4' ? 'selected' : "" ?>> Viernes</option>
                <option value='5' <?= @$rango1[1] == '5' ? 'selected' : "" ?>> Sabado</option>
                <option value='6' <?= @$rango1[1] == '6' || !isset($rango1) ? 'selected' : "" ?>> Domingo
                </option>
            </select>

        </div><!-- ./rango 1 dias -->

        <!--------------------------------------------------------------  Rango 1 HORAS -->
        <div class="col-12 col-xxl-5  text-center text-xxl-start">

            De:<input class='form-control d-inline-block' type="number" min="0" max="23" name='dias[0][hora_inicio]' value='<?= @$rango1[2] ?? 0 ?>' style='width:70px; text-align:center'>
            A:<input class='form-control ms-1 me-2 d-inline-block' type="number" min="0" max="23" name='dias[0][hora_fin]' value='<?= @$rango1[3] ?? 0 ?>' style='width:70px; text-align:center'>

        </div>






        <div class="row horarios text-center">
            <span>Rango 2</span>
            <div class="col-12 col-xxl-7 text-center text-xxl-end">


                <input type="checkbox" class="form-check-input" name="dias[1][active]" value="true" <?= @$rango2["active"] ? "checked" : "" ?>>

                <select class='form-select d-inline-block' name='dias[1][dia_inicio]' style='width:120px'>
                    <option value='0' <?= @$rango2[0] == '0' ? 'selected' : "" ?>> Lunes</option>
                    <option value='1' <?= @$rango2[0] == '1' ? 'selected' : "" ?>> Martes</option>
                    <option value='2' <?= @$rango2[0] == '2' ? 'selected' : "" ?>> Miercoles</option>
                    <option value='3' <?= @$rango2[0] == '3' ? 'selected' : "" ?>> Jueves</option>
                    <option value='4' <?= @$rango2[0] == '4' ? 'selected' : "" ?>> Viernes</option>
                    <option value='5' <?= @$rango2[0] == '5' ? 'selected' : "" ?>> Sabado</option>
                    <option value='6' <?= @$rango2[0] == '6' ? 'selected' : "" ?>> Domingo</option>
                </select>




                A:
                <select class='form-select  d-inline-block' name='dias[1][dia_fin]' style='width:120px'>
                    <option value='0' <?= @$rango2[1] == '0' ? 'selected' : "" ?>> Lunes</option>
                    <option value='1' <?= @$rango2[1] == '1' ? 'selected' : "" ?>> Martes</option>
                    <option value='2' <?= @$rango2[1] == '2' ? 'selected' : "" ?>> Miercoles</option>
                    <option value='3' <?= @$rango2[1] == '3' ? 'selected' : "" ?>> Jueves</option>
                    <option value='4' <?= @$rango2[1] == '4' ? 'selected' : "" ?>> Viernes</option>
                    <option value='5' <?= @$rango2[1] == '5' ? 'selected' : "" ?>> Sabado</option>
                    <option value='6' <?= @$rango2[1] == '6' || !isset($rango1) ? 'selected' : "" ?>> Domingo
                    </option>
                </select>

            </div><!-- rango 1 dias -->

            <div class="col-12 col-xxl-5  text-center text-xxl-start">
                <!-- rango 1 parte 2 -->

                <label class="form-label my-3 my-xxl-0" for="desdelas1">De: </label>
                <input class='form-control d-inline-block' id="desdelas1" type="number" min="0" max="23" name='dias[1][hora_inicio]' value='<?= @$rango2[2] ?? 0 ?>' style='width:70px; text-align:center'>
                A:<input class='form-control ms-1 me-2 d-inline-block' type="number" min="0" max="23" name='dias[1][hora_fin]' value='<?= @$rango2[3] ?? 0 ?>' style='width:70px; text-align:center'>

            </div>

        </div>



        <div class="row horarios text-center">
            <span>Rango 3</span>
            <div class="col-12 col-xxl-7 text-center text-xxl-end">

                <!-- checkbox activo -->
                <input type="checkbox" class="form-check-input" name="dias[2][active]" value="true" <?= @$rango3["active"] ? "checked" : "" ?>>

                <!-- dias -->
                <select class='form-select d-inline-block' name='dias[2][dia_inicio]' style='width:120px'>
                    <option value='0' <?= @$rango3[0] == '0' ? 'selected' : "" ?>> Lunes</option>
                    <option value='1' <?= @$rango3[0] == '1' ? 'selected' : "" ?>> Martes</option>
                    <option value='2' <?= @$rango3[0] == '2' ? 'selected' : "" ?>> Miercoles</option>
                    <option value='3' <?= @$rango3[0] == '3' ? 'selected' : "" ?>> Jueves</option>
                    <option value='4' <?= @$rango3[0] == '4' ? 'selected' : "" ?>> Viernes</option>
                    <option value='5' <?= @$rango3[0] == '5' ? 'selected' : "" ?>> Sabado</option>
                    <option value='6' <?= @$rango3[0] == '6' ? 'selected' : "" ?>> Domingo</option>
                </select>


                A: <select class='form-select  d-inline-block' name='dias[2][dia_fin]' style='width:120px'>
                    <option value='0' <?= @$rango3[1] == '0' ? 'selected' : "" ?>> Lunes</option>
                    <option value='1' <?= @$rango3[1] == '1' ? 'selected' : "" ?>> Martes</option>
                    <option value='2' <?= @$rango3[1] == '2' ? 'selected' : "" ?>> Miercoles</option>
                    <option value='3' <?= @$rango3[1] == '3' ? 'selected' : "" ?>> Jueves</option>
                    <option value='4' <?= @$rango3[1] == '4' ? 'selected' : "" ?>> Viernes</option>
                    <option value='5' <?= @$rango3[1] == '5' ? 'selected' : "" ?>> Sabado</option>
                    <option value='6' <?= @$rango3[1] == '6' || !isset($rango3) ? 'selected' : "" ?>> Domingo
                    </option>
                </select>

            </div><!-- rango 3 dias -->

            <div class="col-12 col-xxl-5  text-center text-xxl-start">
                <!-- rango 3 HORAS 2 -->

                De:<input class='form-control d-inline-block' type="number" min="0" max="23" name='dias[2][hora_inicio]' value='<?= @$rango3[2] ?? 0 ?>' style='width:70px; text-align:center'>
                A:<input class='form-control ms-1 me-2 d-inline-block' type="number" min="0" max="23" name='dias[2][hora_fin]' value='<?= @$rango3[3] ?? 0 ?>' style='width:70px; text-align:center'>

            </div>

            <input class="mt-3 btn btn-success" type="submit" value="Guardar">

        </div>


    </div> <!-- row padre -->



</form>