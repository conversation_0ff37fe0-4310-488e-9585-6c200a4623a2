<?php

$id_usuario = $_SESSION['user']->id;
// $usuario = $pdo->query("SELECT * from usuarios WHERE id = $id_usuario")->fetch();

$usuario = Flex()->fetch("SELECT * from usuarios WHERE id = $id_usuario");
$anuncios_de_usuario = Flex()->fetchAll("SELECT id, nombre, portadas from anuncios WHERE id_usuario = $usuario->id AND estado_anuncio=1");
$anuncios_de_usuario_caducados = Flex()->fetchAll("SELECT id, nombre, portadas from anuncios WHERE id_usuario = $usuario->id AND estado_anuncio=0");

// Usuario del anuncio 
    //$anuncios_de_usuario = $pdo->query("SELECT id, nombre, portadas from anuncios WHERE id_usuario = $usuario->id AND estado_anuncio=1")->fetchAll();
    //$anuncios_de_usuario_caducados = $pdo->query("SELECT id, nombre, portadas from anuncios WHERE id_usuario = $usuario->id AND estado_anuncio=0")->fetchAll();


?>
 <div class="vstack gap-2 col-5 mx-auto">
   <a class="btn btn-outline-success mx-auto" href="&fun[]=anuncioCrear()&x&action&fun[]=redirect(url:/panel/detalle/829/@step=1)">Crear nuevo</a> <br> 
</div>
    <h3 class="text-center">Publicados</h3>
    
    <div class="row justify-content-center" style="max-width: 1024px; margin: 0 auto">
 
    <?php foreach($anuncios_de_usuario as $anuncio): ?>

        <?php   
       // Si tiene portadas 
       $portadas = json_decode($anuncio->portadas, true);  
       $portadas = $portadas['anuncio'];
   
       if (  isset($portadas[0]) AND isset($portadas[1]) ){ 
           $portadasJSON= str_replace('"',"'",json_encode($portadas)); 
           $portada = "/".app\Config::$uploads . "/fotos/" . $portadas[0];
       }elseif( isset($portadas[0]) AND !isset($portadas[1])){
           $portadas = [$portadas[0],$portadas[0]];
           $portadasJSON= str_replace('"',"'",json_encode($portadas));  
           $portada = "/".app\Config::$uploads . "/fotos/" . $portadas[0];
       }else{
           $portadas = ["vacia.jpg","vacia.jpg"];
           $portadasJSON= str_replace('"',"'",json_encode($portadas));  
           $portada = "/".app\Config::$files . "/vacia.jpg";
       }
        ?>
        <div class="col-12 col-sm-6 col-md-4 d-flex justify-content-center p-2">
            <div class="card" style="box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px; width: 320px">
            <img src="<?= $portada ?>" data-active-image="0" data-images="<?= $portadasJSON ?>" style="height: 240px; object-fit: cover; object-position: 100% 0;" class="imgAnuncio card-img-top" alt="...">
                <div class="card-body">
                   
                    <div class="d-flex justify-content-between">
                        <div class="nombre mt-2">
                            <?= "$anuncio->nombre ($anuncio->id)" ?>
                        </div>
                        <div class="boton">
                            <div class="dropdown">
                                <a class="btn dropdown-toggle text-center" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa-solid fa-pen-to-square"></i>
                                    Editar
                                </a>
 
                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                    <li><a class="dropdown-item" href="/panel/detalle/<?=$anuncio->id?>/&edit"><span><i class="fa-solid fa-pen-to-square pe-2"></i></span>Detalles</a></li> 
                                    <div class="dropdown-divider"></div>
                                    <li><a class="dropdown-item" href="/panel/galeria/<?=$anuncio->id?>/&edit"><span><i class="fa-solid fa-images pe-2"></i></span>Galeria</a></li> 
                                    <div class="dropdown-divider"></div>
                                    <li><a class="dropdown-item" href="/panel/textos/<?=$anuncio->id?>/&edit"><span><i class="fa-solid fa-comment pe-2"></i></span>Textos</a></li> 
                                    <div class="dropdown-divider"></div>
                                    <li><a class="dropdown-item" href="/panel/horario/<?=$anuncio->id?>/&edit"><span><i class="fa-solid fa-clock pe-2"></i></span>Horario</a></li>  
                                    <div class="dropdown-divider"></div>
                                    <li><a class="dropdown-item" href="/panel/mapa/<?=$anuncio->id?>/&edit"><span><i class="fa-solid fa-map pe-2"></i></span>Mapa</a></li>  
                                </ul>

                            </div> 
                        </div>
                    </div> 
                    <span class="card-text">Caduca en: <?=anuncioCaducaEn(["id"=>$anuncio->id])?></span> 
                    
                    <div id="dropstart" class="btn-group dropstart"></div>
 

                </div>
            </div> <!-- card -->


        </div> <!-- col -->

        <?php endforeach ?> 
        </div> <!-- row -->
 
    <br><h3 class="text-center">No publicados</h3>

    <div class="row justify-content-center" style="max-width: 1024px; margin: 0 auto">
 
    
    <?php foreach($anuncios_de_usuario_caducados as $anuncio): ?>
 
        <?php
        // Si tiene portadas 
        $portadas = json_decode($anuncio->portadas, true);  
        $portadas = $portadas['anuncio'];
    
        if (  isset($portadas[0]) AND isset($portadas[1]) ){ 
            $portadasJSON= str_replace('"',"'",json_encode($portadas)); 
            $portada = "/".app\Config::$uploads . "/fotos/" . $portadas[0];
        }elseif( isset($portadas[0]) AND !isset($portadas[1])){
            $portadas = [$portadas[0],$portadas[0]];
            $portadasJSON= str_replace('"',"'",json_encode($portadas));  
            $portada = "/".app\Config::$uploads . "/fotos/" . $portadas[0];
        }else{
            $portadas = ["vacia.jpg","vacia.jpg"];
            $portadasJSON= str_replace('"',"'",json_encode($portadas));  
            $portada = "/".app\Config::$files . "/vacia.jpg";
        }
 
        ?>

        <div class="col-12 col-sm-6 col-md-4 d-flex justify-content-center p-2">
            <div class="card" style="box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px; width: 320px">
            <img src="<?= $portada ?>" data-active-image="0" data-images="<?=$portadasJSON?>" style="height: 240px; object-fit: cover; object-position: 100% 0;" class="imgAnuncio card-img-top" alt="...">
                <div class="card-body">
                   
                    <div class="d-flex justify-content-between">
                        <div class="nombre mt-2">
                            <?= "$anuncio->nombre ($anuncio->id)" ?>
                        </div>
                        <div class="boton">
                            <div class="dropdown">
                                <a class="btn dropdown-toggle text-center" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa-solid fa-pen-to-square"></i>
                                    Editar
                                </a>
 
                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                    <li><a class="dropdown-item" href="/panel/detalle/<?=$anuncio->id?>&edit"><span><i class="fa-solid fa-pen-to-square pe-2"></i></span>Detalles</a></li> 
                                    <div class="dropdown-divider"></div>
                                    <li><a class="dropdown-item" href="/panel/galeria/<?=$anuncio->id?>&edit"><span><i class="fa-solid fa-images pe-2"></i></span>Galeria</a></li> 
                                    <div class="dropdown-divider"></div>
                                    <li><a class="dropdown-item" href="/panel/textos/<?=$anuncio->id?>&edit"><span><i class="fa-solid fa-comment pe-2"></i></span>Textos</a></li> 
                                    <div class="dropdown-divider"></div>
                                    <li><a class="dropdown-item" href="/panel/horario/<?=$anuncio->id?>&edit"><span><i class="fa-solid fa-clock pe-2"></i></span>Horario</a></li>  
                                    <div class="dropdown-divider"></div>
                                    <li><a class="dropdown-item" href="/panel/mapa/<?=$anuncio->id?>&edit"><span><i class="fa-solid fa-map pe-2"></i></span>Mapa</a></li>
                                    <div class="dropdown-divider"></div>
                                    <li><a class="dropdown-item text-danger" href="&fun[]=anuncioEliminar(id:<?=$anuncio->id?>)&fun[]=redirect()&x&action" onclick="return confirm('¿Estás seguro de que deseas eliminar este anuncio?')"><span><i class="fa-solid fa-trash pe-2"></i></span>Eliminar</a></li>  
                                </ul>

                            </div> 
                        </div>
                    </div> 
                    <span class="card-text">Caduca en: <?=anuncioCaducaEn(["id"=>$anuncio->id])?></span> 
                    
                    <div id="dropstart" class="btn-group dropstart"></div>
 

                </div>
            </div> <!-- card -->


        </div> <!-- col -->

        <?php endforeach ?> 
        </div> <!-- row -->
<br><br><br><br>

 
            
        <script>
        function alternarFotos() {
        $imgAnuncios = Array.from(document.querySelectorAll(".imgAnuncio"));

        $imgAnuncios.forEach(
            ($imgAnuncio) => {
                $src = $imgAnuncio.getAttribute("src");

                $fotos = JSON.parse($imgAnuncio.getAttribute("data-images").replace(/'/g, '"'));
                $activeIndex = parseInt($imgAnuncio.getAttribute("data-active-image"));
                $activeFoto = $fotos[$activeIndex];
                let $newIndex = ($activeIndex >= $fotos.length - 1) ? 0 : $activeIndex + 1;
                $newSrc = $src.replace($activeFoto, $fotos[$newIndex]);

                //console.log($src, $newSrc);
                $imgAnuncio.setAttribute("src", $newSrc);
                $imgAnuncio.setAttribute("data-active-image", $newIndex);

            }
        )
    }
 
        setInterval(
            () => {
                alternarFotos();
            }, 1000
        )

        </script>
        

    <style>

        .card{
            color: #979797;
        }

        .dropdown .btn-secondary {
            background-color: none;
        }

        .boton{
            width:100px;  
            display:flex;
            justify-content: center;

        }

    </style>