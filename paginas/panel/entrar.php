<?php
 
/* 
    1. Recupera el campo de email y analiza su existencia en la DB. 
       a) Si existe continua
       b) Si no existe le avisa y le recuerda que debe registrarse. 
    2. Envia un email con un codigo de validación con un tiempo expiracion. 
    3. Redireccióna a la vista en donde introduccir el código.
  
*/
 
 if(isset($_POST["email"])){
 
  $email = $_POST["email"]; 
  $usuario = getfun("getUsuario(\"email\":\"$email\")")["data"];
  $id = $usuario->id;
  $email = $usuario->email;
 
  if($usuario != false){  
      userCodeCreate(["id"=>$usuario->id]); 
      $link = "/panel/entrar-codigo/&email=$email"; 
      flex()->redirect($link);
  }else {
      $_SESSION['error'] = "No se encontro una cuenta con ese email, registrese"; 
  }
 
 }

?>


<div class="vh-100 d-flex justify-content-center align-items-center">
  <div class="container">
    <div class="row d-flex justify-content-center">
      <div class="col-12 col-md-8 col-lg-5">
        <div class="border border-3 border-danger"></div>
        <div class="card bg-white">
          <div class="card-body p-5">

            <form action="" method="POST" class="mb-3 mt-md-4">
              <!-- <h2 class="mb-2 text-uppercase">Citas</h2> -->
              <h2 class=" mb-5">Acceder a mi cuenta</h2>

              <?php if( flex()->user->rol == 0):?>
                        <!-- SI NO TIENE SESIÓN INICIADA -->
                        <div class="mb-3">
                          <label for="email" class="form-label ">Email</label>
                          <input type="text" class="form-control mb-4" name="email" id="email" placeholder="Su email">
                          <div id="response"></div>
                        </div>
                      <div class="d-grid">
                        <button class="btn btn-danger" type="submit">Entrar</button>
                      </div>
                      </form>
                        <?php if(isset($_SESSION['error'])):?>
                          <div class='alert alert-danger' role='alert'>
                              <?=$_SESSION['error']?>
                          </div>
                            <?php unset($_SESSION['error'])?>
                        <?php endif; ?>

                      <div>
                        <p class="mb-0 text-center">¿No tiene una cuenta? <a href="/panel/registrarse" class="text-primary fw-bold"> Registrate</a></p>
                      </div>
              <?php else: ?>
                <!-- SI TIENE SESIÓN INICIADA FIN -->
                <p class="mb-0 text-center">Ya has iniciado sesión, <a href="/panel/inicio" class="text-primary fw-bold"> accede</a></p><br>
                <p> O también puedes <a href="<?="$flex->path&fun[]=cerrarSesion&fun[]=redirect(url:/panel/entrar)"?>" class="text-danger fw-bold"> Cerrar sesión</a></p>
              <?php endif; ?>

          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .main {
    background-color: black !important;
  }

  .card {
    border-radius: 0px 0px 5px 5px;
  }

  h2 {
    color: black;
    text-align: center;
  }

  @media (min-width: 1200px) {

    .p-xl-4 {
      padding: 0px !important;
    }
  }
</style>