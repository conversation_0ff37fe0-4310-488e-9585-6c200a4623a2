<?php
$file = "uploads/fotos/".$foto->filename;
// detect File size
$size = list($width, $height) = getimagesize($file);
$imgHeight = $size[1];
$imgWidth = $size[0];
?>


<!-- Modal -->
<div class="modal fade" id="taparcara<?=$foto->id?>" tabindex="-1" aria-labelledby="taparcaraLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="taparcaraLabel">Difuminación de cara <?=$foto->id?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="container">

                    <div class="img-container mb-2" style="margin: 0 auto">
                        <img id="img<?=$foto->id?>" src="/<?=$file?>?rel=<?= time() ?>">
                    </div>
                  <!--  <p class="text-center"><?= "Tamaño imágen: $imgWidth x $imgHeight" ?></p> -->
                    <form action="blurphp.php" method="GET">


                        <input id="filename" name="filename" type="hidden" value="1.jpg" readonly>

                        <input name="imgWidth" type="hidden" value="<?= $imgWidth ?>">
                        <input name="imgHeight" type="hidden" value="<?= $imgHeight ?>">


                        <div class="row justify-content-center p-1">
                            <div class="row mb-3" style="max-width: 320px; box-shadow: 1px 2px 2px #d1d1d1; border: solid 1px #bdc0c2; padding: 10px">
                                <div class="col-6 text-center" style="border-right: 1px solid #bdc0c2">
                                    <label for="intensidad" class="form-label">Intensidad:</label>
                                    <input type="range" class="form-range" id="intensidad" name="intensidad" min="1" max="3" value="2">
                                </div>
                                <div class="col-6">
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="radio" name="filter" value="desenfocar" id="desenfocar">
                                        <label class="form-check-label" for="desenfocar">
                                            Desenfocar.
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="filter" value="pixelar" id="pixelar" checked>
                                        <label class="form-check-label" for="pixelar">
                                            Pixelar.
                                        </label>
                                    </div>
                                </div>
                            </div>

                        </div>


                        <div class="row justify-content-center" style="display:none">
                            <p class="text-center">Selección</p>
                            <div class="col-12 col-md-5">
                                <div class="row text-center">
                                    <div class="col">
                                        <label for="selectWidth<?=$foto->id?>">Ancho:</label>
                                        <input class="ocultar" placeholder="anchura" name="selectWidth" id="selectWidth<?=$foto->id?>" style="width: 40px" readonly>
                                    </div>
                                    <div class="col">
                                        <label for="selectHeight<?=$foto->id?>">Alto:</label>
                                        <input class="ocultar" placeholder="altura" name="selectHeight" id="selectHeight<?=$foto->id?>" style="width: 40px" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row justify-content-center" style="display:none">
                            <div class="col-12 col-md-4">
                                <div class="row text-center">
                                    <div class="col">
                                        <label for="xCoord<?=$foto->id?>">X:</label>
                                        <input id="xCoord<?=$foto->id?>" style="width: 40px" name="xCoord">

                                    </div>
                                    <div class="col">
                                        <label for="yCoord<?=$foto->id?>">Y:</label>
                                        <input id="yCoord<?=$foto->id?>" style="width: 40px" name="yCoord">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="d-grid gap-2 col-4 mx-auto">
                        <button class="btn btn-warning" type="submit" id="blurButton">PREVISUALIZAR</button>
                        </div>

                    </form>




                </div>
            </div>
            <div class="modal-footer justify-content-center">
                    <form action="blurphp.php" method="GET">

                        <input type="hidden" name="restaurar" value="1">
                        <input type="hidden" name="filename" value="<?= $file ?>">
                        <button type="submit" class="btn btn-danger" >Restaurar</button>
                    </form>
                <a href="/panel/galeria/<?=$id?>/&step=2&edit"> <button type="button" class="btn btn-success">Guardar cambios</button></a>
            </div>
        </div>
    </div>
</div>


<!-- Tapar cara script -->


<script>
    const image<?=$foto->id?> = document.querySelector("#img<?=$foto->id?>");
    const cropper<?=$foto->id?> = new Cropper(image<?=$foto->id?>, {
        zoomable: false,
        zoomOnWheel: false,
        wheelZoomRatio: false,
        zoomOnTouch: false,
        minContainerWidth: 240,
        minContainerHeight: 240,
        responsive: true,

        crop(event) {
            // convertir a enteros

            document.getElementById("xCoord<?=$foto->id?>").value = parseInt(event.detail.x);
            document.getElementById("yCoord<?=$foto->id?>").value = parseInt(event.detail.y);
            document.getElementById("selectWidth<?=$foto->id?>").value = parseInt(event.detail.width);
            document.getElementById("selectHeight<?=$foto->id?>").value = parseInt(event.detail.height);
        },
    });
</script>


<style>
    .control-container {
        display: flex;
        justify-content: center;
        margin-top: 10px;
    }

    .control-container label {
        margin: 0 5px;
    }

    .cropper-container{
        margin: 0 auto;
    }

    #canvas {
        border: 1px solid #000;
    }

    .modal-content img {
        display: block;
        }

    .img-container{
        width: 95%;
        max-width: 95%;
        overflow: auto;
        height: 240px;
    }

</style>