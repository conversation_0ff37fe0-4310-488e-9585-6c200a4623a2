<?php
// &fun=getPortadas(53)

function getPortadas($opt = []){
  
    $id_anuncio = $opt['id'];
    $anuncio = getfun("getAnuncio(\"id\":\"$id_anuncio\")")["data"]; 

    $portadasSQL = json_decode($anuncio->portadas, true); 

        $portadas = $portadasSQL['anuncio']; 
        $portadasJSON = str_replace('"', "'", json_encode($portadas));
        $portada1 = isset($portadas[0]) ? "/" . app\Config::$uploads . "/fotos/" . $portadas[0] : "/" . app\Config::$files . "/vacia.jpg";
        $portada2 = isset($portadas[1]) ? "/" . app\Config::$uploads . "/fotos/" . $portadas[1] : "/" . app\Config::$files . "/vacia.jpg";

    return [$portada1, $portada2, $id_anuncio] ;

}

function getFotosContratadas($opt){
    global $pdo;
    $id = $opt['id'];
    $sql1 = "SELECT fotos FROM anuncios WHERE id=$id";
    $limite = flex()->fetch($sql1)->fotos;
     $sql = "SELECT * FROM fotos WHERE id_anuncio=$id AND estado=1 ORDER BY pos ASC LIMIT $limite";
     $fotos = flex()->fetchAll($sql);
    return $fotos;
}