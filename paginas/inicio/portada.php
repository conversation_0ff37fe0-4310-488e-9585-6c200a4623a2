 

<div class="portada-contenedor <?= $anuncio->estado_destacado == 1 ? 'destacado' : 'normal' ?>">

    <div class="titulo">
        <span class="distancia"><i class="bi bi-broadcast"></i> <?= round($anuncio->distancia, 1) ?><span
                class="value">km</span></span>
        <?= $anuncio->estado_destacado == 1 ? '<div class="sello"><img src="/files/sello_destacado.png"></div>' : '' ?>

        <span class="nombre"><?= $anuncio->nombre ?></span>
        <span class="edad"><i class="bi bi-person-fill"></i> <?= $anuncio->edad ?> años</span>


    </div>
    <?= $anuncio->estado_destacado == 1 && $anuncio->destacado_tipo == 1 ? '<img class="stars" src="/files/stars.svg">' : '' ?>
    <?= $anuncio->estado_destacado == 1 && $anuncio->destacado_tipo == 0 ? '<img class="stars" src="/files/star.svg">' : '' ?>

    <div class="cajanegra">
        <img src="/files/cajanegra.jpg" alt="">
    </div>
    <div class="interno">


        <div class="imgbox">

            <?php if ($anuncio->videoContratado) : ?>
            <span class="video animacion_derecha"><i class="bi bi-play-fill"></i><span class="value">VIDEO</span></span>
            <?php endif ?>

            <a href="../anuncio/<?= $anuncio->id ?>">
                <img class="foto imgAnuncio" loading="lazy" data-active-image="0" data-images="<?= $portadasJSON ?>"
                    src="<?= $portada ?>">
            </a>

            <?php  if ( getfun("trabajaAhora(\"id\":$anuncio->id)")[0] ) : ?>
            <span class="disponibilidad animacion_izquierda">DISPONIBLE</span>
            <?php  else : ?>
            <span class="disponibilidad animacion_izquierda nodisponible">NO DISPONIBLE</span>
            <?php  endif ?>


            <?php if ($anuncio->id_pais) : ?>
            <img class="bandera"
                src="https://raw.githubusercontent.com/lipis/flag-icons/main/flags/1x1/<?= $anuncio->id_pais ?>.svg"
                alt="">
            <?php endif ?>
        </div> <!-- imgbox-->
    </div> <!-- interno -->
    <div class="tags container-fluid">
        <div class="row">
            <div class="a col-6">
                <span><i class="bi bi-arrow-down-up"></i>
                    <?= getDiasRestantes($anuncio->fecha_publicacion, $anuncio->fecha_caducidad) ?></span>
                <span>
                    <i class="bi bi-hash"></i>
                    <span id="portada_mensaje" data-1="<?=categoriaSingular($anuncio->id_categoria)?>"
                        data-2="<?= ucfirst($anuncio->portada_mensaje) ?>"><?=categoriaSingular($anuncio->id_categoria) ?></span>

                </span>

            </div>
            <div class="b col-6">
                <span>
                    <i class="bi bi-car-front-fill"></i>
                    Salidas
                    <?php if ($anuncio->salidas) : ?>
                    <span class="salida">SI</span>
                    <?php else : ?>
                    <span class="salida">NO</span>
                    <?php endif ?>
                </span>
                <span><i class="bi bi-clock-history"></i><?= getHorarioHoyStr(['id'=>$anuncio->id]) ?></span>  
            </div>

        </div>


    </div>


    <?php if ($anuncio->estado_destacado == 1 && $anuncio->destacado_tipo == 0 ) : ?> 
    <div class="msg-dest"><span class="texto"> DESTACADO </span></div>
    <?php elseif ($anuncio->estado_destacado == 1 && $anuncio->destacado_tipo == 1 ) : ?> 
    <div class="msg-dest"><span class="texto"> SUPER DESTACADO </span></div>
    <?php else : ?>

    <?php endif ?>



</div> <!-- portada-contenedor  --> 

<?php if ($anuncio->estado_destacado == 1) :?>
        
       <?php if($anuncio->hayWhatsapp1): ?>
            <?php $telefono1 = str_replace(' ', '',  $anuncio->telefono) ?>
        <a class="llamar" href="<?="https://api.whatsapp.com/send?phone=34$telefono1&text=Hola,%20he%20visto%20tu%20anuncio%20en%20citas10"?>"> <img src="/files/whatsapp.png"></a>
       <?php endif ?>

       <?php if($anuncio->hayWhatsapp2): ?>
        <?php $telefono2 = str_replace(' ', '',  $anuncio->telefono2) ?>
        <a class="llamar" href="<?="https://api.whatsapp.com/send?phone=34$telefono2&text=Hola,%20he%20visto%20tu%20anuncio%20en%20citas10"?>"><img src="/files/whastapp.png"></a>
       <?php endif ?>

       <!-- llamada ----------------------------->
       <?php if( $anuncio->hayWhatsapp1 != true && $anuncio->hayWhatsapp2 != true): ?>

        <a class="llamar" href="<?="tel:$anuncio->telefono"?>"><img src="/files/llamar.svg"></a>

       <?php endif ?>


    <?php endif ?>