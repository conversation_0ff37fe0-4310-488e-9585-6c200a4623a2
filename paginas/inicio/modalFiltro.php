<!------------------------ ModalFiltro -------------------------- !-->

<div class="modal face" id="modalFiltro" ttabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><img class="icon" src="/files/filtro.svg" alt=""> Busqueda y filtros</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><img
                        src="/files/close.svg" alt=""> </button>
            </div>
            <div class="modal-body">
                <!-- Form buscador -->

                <form action="&fun=buscar()" method="POST">

                    <span class="menu-icon"><img class="icon" src="/files/buscar.svg" alt=""></span>
                    <input class="form-control" id="buscar" placeholder="Buscar..."
                        <?= isset($_GET['search']) && strlen($_GET['search'])>2 ? " value=\" " . $_GET['search'] . " \" " : '' ?>
                        aria-label="Search" name="search">

                    <?= isset($_GET['search']) ? '<a href="/"><i class="bi bi-x" style="font-size: 2.3rem; color:black"></i>' : '' ?>
                    </a>
                    <button class="btn" type="submit"> Buscar</button>

                </form>

                <!-- Categorias -->
                <ul>
                    <?php if ($_SESSION['filtros']['categoria']['id'] != 0) : ?>
                    <a href='&fun=filtros("categoria":0)'>
                        <li class=""> <span class="menu-icon"> <img
                                    src="/files/eye.svg" alt=""></span> Sin filtro</li>
                    </a>
                    <?php endif ?>
                    <a href='&fun=filtros("categoria":1)'>
                        <li class="<?=$_SESSION['filtros']['categoria']['id'] == 1 ? 'active' : '' ?>"> <span class="menu-icon"> <img
                                    src="/files/categoria_1.svg" alt=""></span> Chicas </li>
                    </a>
                    <a href='&fun=filtros("categoria":2)'>
                        <li class="<?=$_SESSION['filtros']['categoria']['id'] == 2 ? 'active' : '' ?>"> <span class="menu-icon"> <img
                                    src="/files/categoria_2.svg" alt=""></span> Chicos </li>
                    </a>
                    <a href='&fun=filtros("categoria":3)'>
                        <li class="<?=$_SESSION['filtros']['categoria']['id'] == 3 ? 'active' : '' ?>"> <span class="menu-icon"> <img
                                    src="/files/categoria_3.svg" alt=""></span> Transgénero </li>
                    </a>
                    <a href='&fun=filtros("categoria":4)'>
                        <li class="<?=$_SESSION['filtros']['categoria']['id'] == 4 ? 'active' : '' ?>"> <span class="menu-icon"> <img
                                    src="/files/categoria_4.svg" alt=""></span> Alojamientos </li>
                    </a>


                    <!-- Example single danger button -->
                   
                </ul>

            </div>


        </div>

    </div>
</div>


<script>
const buscarBtn = document.querySelector('.buscar') 
const buscarInput = document.getElementById('buscar')

buscarBtn.addEventListener('click', (e) => { 
    buscarInput.focus()
})
</script>