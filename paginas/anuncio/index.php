<?php
$id = (int) Flex()->uri(-1);
$anuncio = getfun("getAnuncio(\"id\":$id)")["data"];

$usuario = Flex()->fetch("SELECT * from usuarios WHERE id = $anuncio->id_usuario");
$anuncios_de_usuario = Flex()->fetchAll("SELECT id, nombre from anuncios WHERE id_usuario = $usuario->id AND estado_anuncio=1 AND id != $id AND id_usuario !=1");


/* ---------- en head_anuncio.php -------------- */

   //  if(  app\uri(1) <= 0 ) header("Location: /inicio/");

$fotos = getfun("getFotosContratadas(id:$id)");

$posicion = 0;
$dominio = "citas10.com";
$logodominio = 'citas10';
$tel_limpio = str_replace(' ', '',  $anuncio->telefono);

 if($anuncio->hayWhatsapp2) $tel_limpio = str_replace(' ', '',  $anuncio->telefono2);


$horario = getfun("getHorario(\"id\":$id)")["data"];
$rango1 = $horario[0];
$rango2 = $horario[1];
$rango3 = $horario[2];
$dias = ["Lunes", "Martes", "Miercoles", "Jueves", "Viernes", "Sabado", "Domingo"];
$sexo = [0, "Chica", "Chico", "Transgénero", "Alojamiento"];
$localizacion = json_decode($anuncio->gps, true);
$lat = $localizacion["lat"];
$lng = $localizacion["lng"];
$notas = getfun("getUserNotes(\"id\":$id)")["data"];



if ( videoExiste($id) && $anuncio->videoAprobado && $anuncio->videoContratado) {
     $time = time();
     echo "<h3 class='video'>VIDEO</h3>
    <div id='cajavideo'>
            <video controls>
            <source src='../../uploads/videos/$id.mp4?rel=$time' type='video/mp4'>
            </video>
    </div>";
}else{
     echo "<!-- NO SE HA ENCONTRADO VIDEO O ESTA DESACTIVDADO --> ";
}
?>


<style>

.notas li{
     display: block;
}

@media (min-width: 640px){

     .notas li{
          display:inline-block;
     }

}

</style>


<div class="principal">


     <!-- VIDEO -->



     <div class="galeria contenedor">
          <h3><i class="far fa-images"></i>&nbsp;Citas10 antes Navarrasex</h3>

          <!-- Flickity HTML init -->
          <div class="carousel js-flickity">
               <?php foreach ($fotos as $foto) : ?>
                    <div class='carousel-cell'><img alt='citas10 antes navarrasex fotos pamplona no putas' src='/uploads/fotos/<?= $foto->filename ?>?r="<?= rand(1, 1000000) ?>'></div>
               <?php endforeach ?>
          </div><!-- Flickity HTML end -->


     </div> <!-- galeria contenedor-->
     <div class="informacion contenedor">
          <h3><i class="far fa-address-card"></i> &nbsp; DATOS</h3>
          <div class="datos">

               <div class="datos1" style="flex: 1 0 65%">

                    <p>Nombre: <?= @$anuncio->nombre ?></p>
                    <p>SEXO: <?= $sexo[$anuncio->id_categoria] ?>
                    <div class="telefonos">

                         <div class="tele1" style="min-width: 170px">
                              <?php if ($anuncio->hayWhatsapp1) : ?>

                                   <a class='pop' href='https://api.whatsapp.com/send?phone=34<?=$tel_limpio?>&text=Hola,%20he%20visto%20tu%20anuncio%20en%20citas10' data-container='body' data-content=' CLICK AQUI ' data-trigger='focus' data-toggle='popover' data-placement='bottom'>
                                        <img class='wp' src='<?= "/files/whatsapp.png" ?>' width='32px' height='32px' style='border:none; position:relative; margin-right: 4px'>
                                   </a>
                              <?php endif ?>

                              <a id='botontel2' href='tel:<?= $anuncio->telefono ?>'>
                                   <p style="display:inline-block"><i class='fas fa-phone' style='color: white'></i> &nbsp; <?= $anuncio->telefono ?>&nbsp;</i></p>
                              </a>
                         </div>

                         <div class="tele2" style="min-width: 170px">
                              <?php if ($anuncio->hayWhatsapp2) : ?>
                                   <a class='pop' href='https://api.whatsapp.com/send?phone=34<?=$tel_limpio?>&text=Hola,%20he%20visto%20tu%20anuncio%20en%20citas10' data-container='body' data-content=' CLICK AQUI ' data-trigger='focus' data-toggle='popover' data-placement='bottom'>
                                        <img class='wp' src='<?= "/files/whatsapp.png" ?>' width='32px' height='32px' style='border:none; position:relative; margin-right: 4px'>
                                   </a>
                              <?php endif ?>


                              <?php if (strlen($anuncio->telefono2) > 4) : ?>
                                   <a id='botontel2' href='tel:<?= $anuncio->telefono2 ?>'>
                                        <p style="display:inline-block"><i class='fas fa-phone' style='color: white'></i> &nbsp; <?= $anuncio->telefono2 ?>&nbsp;</i></p>
                                   </a>
                              <?php endif ?>
                         </div>




                    </div><!-- telefonos-->

               </div>
               <div class="datos2" style="flex: 1 0 35%">

                    <p>PAÍS: <?= @$anuncio->pais ?></p>
                    <p>EDAD: <?= @$anuncio->edad ?> Años</p>
                    <p>SALIDAS: <?= @$anuncio->salidas ? "SI" : "NO" ?></p>

               </div> <!-- datos 2 -->

          </div> <!-- datos -->

          <h3><i class="far fa-clock"></i> &nbsp; HORARIO</h3>
          <div class="horario">


               <div class='lv'>
                    <h3> <?= $dias[$rango1[0]] . " a " . $dias[$rango1[1]] ?> </h3>
                    <?php if ($rango1[2] == 0 & $rango1[3] == 0) : ?>
                         <p> Las 24H </p>
                    <?php else : ?>
                         <p><?= "De las " . $rango1[2] . " a las " . $rango1[3] . "h" ?></p>
                    <?php endif ?>
               </div>

               <?php if ($rango2["active"]) : ?>
                    <div class='lv'>
                         <?php if ($rango2[0] == $rango2[1]) : ?>
                              <h3> <?= $dias[$rango2[0]] ?></h3>
                         <?php else : ?>
                              <h3> <?= $dias[$rango2[0]] . " a " . $dias[$rango2[1]] ?> </h3>
                         <?php endif ?>
                              <?php if ($rango2[2] == 0 & $rango2[3] == 0) : ?>
                                   <p> Las 24H </p>
                                   <?php else : ?>
                                        <p><?= "De las " . $rango2[2] . " a las " . $rango2[3] . "h" ?></p>
                              <?php endif ?>
                    </div>
               <?php endif ?>

               <?php if ($rango3["active"]) : ?>
                    <div class='lv'>
                         <?php if ($rango3[0] == $rango3[1]) : ?>
                              <h3> <?= $dias[$rango3[0]] ?></h3>
                         <?php else : ?>
                              <h3> <?= $dias[$rango3[0]] . " a " . $dias[$rango3[1]] ?> </h3>
                         <?php endif ?>
                         <?php if ($rango2[2] == 0 & $rango2[3] == 0) : ?>
                                   <p> Las 24H </p>
                                   <?php else : ?>
                                        <p><?= "De las " . $rango3[2] . " a las " . $rango3[3] . "h" ?></p>
                              <?php endif ?>
                    </div>
               <?php endif ?>

          </div> <!-- horario -->

          <!--
          <h3><i class="fas fa-check"></i> &nbsp; SERVICIOS</h3>
          <div class="servicios">



               <ul>
                    <?php
                    $servicios = explode(',', $anuncio->servicios);
                    foreach ($servicios as $servicio) {
                         echo "<li> $servicio </li>";
                    }
                    ?>

               </ul>

          </div>
          <h3><i class="fas fa-info"></i> &nbsp;INFORMACIÓN ADICIONAL</h3>

          <div class="adicional">

               <div class="notas">


                    <div class="row mt-2">


                         <div class="col-12" <?= $notas ? "style='border-bottom: solid 1px #910303; max-width: 99%; margin-bottom: 15px; margin: 0 auto'" : " " ?>>
                              <span style="position: relative; display: inline-block; margin-bottom: 20px; ">ACEPTO: Efectivo<?= $anuncio->bizum ? ", Bizum" : '' ?><?= $anuncio->visa ? ", <img style='max-width: 70px; border: none' src='/files/visa.svg'>" : '' ?>
                              </span>
                         </div>

                         <div class="col-12">

                              <?php if ($notas) : ?>
                                   <div class="row mt-4">
                                        <div class="col-12">

                                             <ul class="notas">
                                                  <?php foreach ($notas as $key => $value) : ?>


                                                       <?php
                                                       $tipo = $value["tipo"];
                                                       $texto = $value["texto"];
                                                       ?>
                                                       <?= $tipo == 'info' ? "<li class='$tipo' style='margin: 4px;overflow: hidden; height: 27px; background-color: #ededed; color: black; padding: 4px 8px 10px 13px; border-radius: 5px'> <i style='color: blue; position: relative; top: -4px; padding-right: 14px; border-right: solid 1px black; line-height: 200%; margin-right: 10px; ' class='fas fa-info'></i> <span style='top: -4px; position: relative'>$texto </span></li>" : '' ?>
                                                       <?= $tipo == 'no' ? "<li class='$tipo' style='margin: 4px; overflow: hidden; height: 27px; background-color: #ededed; color: black; padding: 4px 10px; border-radius: 5px'> <i style='color: red; position: relative; top: -4px; padding-right: 10px; border-right: solid 1px black; line-height: 200%; margin-right: 6px; ' class='fas fa-times-circle'></i> <span style='top: -4px; position: relative'>$texto </span></li>" : '' ?>
                                                       <?= $tipo == 'si' ? "<li class='$tipo' style='margin: 4px; overflow: hidden; height: 27px; background-color: #ededed; color: black; padding: 4px 10px; border-radius: 5px'> <i style='color: green; position: relative; top: -4px; padding-right: 10px; border-right: solid 1px black; line-height: 200%; margin-right: 6px; ' class='fas fa-check'></i> <span style='top: -4px; position: relative'>$texto </span></li>" : '' ?>



                                                  <?php endforeach ?>

                                             </ul>
                                        </div>
                                   </div>
                              <?php else : ?>

                              <?php endif ?>


                         </div>

                    </div>



               </div>




          </div>

                              -->

          <h3><i class="fas fa-file-alt"></i> &nbsp; <?= "Algo sobre $anuncio->nombre " ?></h3>
          <div class="texto" style="padding-top: 20px; min-height: 200px">
               <h4 style="text-align:center"><?= $anuncio->titulo ?></h4>
               <p>

                    <?= nl2br($anuncio->descripcion) ?>
               </p>
          </div>

          <div class="mapa mt-4 mb-4" style="margin: 0 auto; text-align: center; max-width: 100%; width: 100%">
               <img style="max-width: 100%" src="../../uploads/maps/<?= $id ?>.jpg?rel=<?=time()?>" alt=""><br>
               <a style='display: inline-block; margin-top: 15px; border-radius: 10px; text-decoration: none; color: white; border: solid 1px white; padding: 5px 10px ' href="https://www.google.com/maps/search/?api=1&query=<?= $lat ?>,<?= $lng ?>">
                    Pulse aqui para abrir en Google Maps
               </a>

          </div>
          <?php if(count($anuncios_de_usuario)): ?>

                    <h3 style="display: block">TAMBIÉN TE PUEDE INTERESAR</h3>
                    <div class="relacionados pb-5" style="position: relative; top: -11px">
                         <div class="row d-flex p-2 justify-content-center">

                         <?php foreach($anuncios_de_usuario as $anuncio_usuario){
                                        echo "<div style='position: relative; max-width: 320px' class='col-6 text-center m-1'>";
                                             echo "<a target='_blank' href='/anuncio/$anuncio_usuario->id/'><div style='position: relative; top: 2rem; font-size: 18px; color:white; padding-bottom: 5px; text-transform: uppercase; font-family: \"Courier New\", Courier, monospace'>
                                             $anuncio_usuario->nombre</div>";
                                             $portadas = getfun("getPortadas(\"id\":$anuncio_usuario->id)");
                                             echo " <div class='d-flex justify-content-center align-items-center' style='border: solid 5px #7e0000; height: 100%; border-top: 35px solid #7e0000'>
                                                       <div class='imagen '>
                                                            <img style='border: 3px solid #7e0000' class='' width='200' src='$portadas[0]'>
                                                       </div>
                                                  </div>
                                             </a> ";
                                        echo "</div>";
                         }?>

                    </div> <!-- row relacionados -->
                    </div><!-- relacionados -->

          <?php endif ?>



     </div> <!-- informacion contenedor -->






</div> <!-- principal -->

<script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.3.1/js/bootstrap.bundle.min.js"></script>
<!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/flickity/2.1.2/flickity.pkgd.min.js"></script> -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/flickity/2.2.0/flickity.pkgd.min.js"></script>




<!-- <li><a href="/paginas/privacidad.html"> PRIVACIDAD</a></li>
        <li><a href="/paginas/condiciones.html">CONDICIONES</li>
        <li><a href="/paginas/politica-de-cookies.html">COOKIES</li> -->

<!-- .main que agrupa un posible sidebar y container -->

<!-- Como estas amigo -->




<script>
     /*
  previewMantenimiento();
  setInterval(fetchMantenimientoAPI, 2000)
 */

 </script>

<!-- Cloudflare Web Analytics -->
<script defer src='https://static.cloudflareinsights.com/beacon.min.js' data-cf-beacon='{"token": "a8d9ed55e0d54774bd45f1198f4cd617"}'></script>
<!-- End Cloudflare Web Analytics -->

</body>

</html>