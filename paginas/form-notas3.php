

<?php
 
 
$notas = getfun("getUserNotes(\"id\":$id)")["data"];
 
?>




<form action='/form-notas/&fun=userNotesNew()' method='POST'>

    <input type="hidden" name="id" value="<?= $id ?>"> 
    <div class="row justify-content-center">

    <div class="col-8">

    <div class="row justify-content-center">

            <div class="col-1">

                <label for="notaUsuarioTipo" class="form-label">Tipo</label>
                <select class="form-select" name="notaUsuarioTipo" id="notaUsuarioTipo">
                    <!-- Edición recuperar categoria del actual anuncio -->
                    
                    <option value="info">Info</option> 
                    <option value="no"> No</option>
                    <option value="si"> Si</option>
                </select>
            </div>

            <div class="col-5">
                <label for="notaUsuarioTexto" class="form-label">Texto</label>
                <input type="text" class="form-control" name="notaUsuarioTexto" id="notaUsuarioTexto" placeholder="Nombre">
            </div>
        </div>

    </div>

    <div class="row">
        <div class="d-grid gap-2 col-3 mx-auto">
            <input class="mt-5 btn btn-success" type="submit" value="Guardar">
        </div>
    </div><!-- row submit -->

</div>



<?php  foreach($notas as $el => $nota):  ?>

    <p><?=$nota["tipo"]?> - <?=$nota["nombre"] ?> <a href='/form-notas/&fun=userNotesRemove("id":"<?=$id?>","el":"<?=$el?>")'>borrar</a> </p>

<?php endforeach ?>

 
