<script src="https://maps.googleapis.com/maps/api/js?key=<?= $MAPKEY ?>&libraries=places&callback=initMap&v=weekly" defer></script> 

 <div class="nav">

     <!------------------------------------------ VERSIÓN MÓVIL -------------------------------------------------------->
     <div class="mov-max">
          <a href="/" style="text-decoration: none">
            <div class="logo ms-2">
                <span>Citas</span>
                <span>10</span>
            </div>
         </a>
         <div class="ubicacion"> 
                 <div class="valor" data-bs-toggle="modal" data-bs-target="#modalModoGeo">
                     <div class="box"> 
                        <span> <img src="/files/locale-red.svg" alt=""> </span>  
                        <span id="direccionStrMovil"></span>
                     <!--    <img src="/files/dropdown-search.svg" alt="">-->
                     <span class="dropdown-locale"> <img src="/files/dropdown-red.svg" alt=""> </span>  

                     </div> 

                    

                 </div>
             </div>

         <div class="botones d-flex">

             <div class="box btn-orden" data-bs-toggle="modal" data-bs-target="#modalOrden">
             <?php if ($_SESSION["filtros"]["orden"]["slug"] == 'posicion') :?>
                 <img src="/files/orden.svg" alt="">
              <?php else: ?>
                    <img class="active" src="/files/orden_<?=$_SESSION["filtros"]["orden"]["slug"]?>.svg" alt="">
              <?php endif;?>
             </div>
             <div class="box btn-filtros <?= $_SESSION["filtros"]["categoria"]["id"] != 0 ? 'active' : 0  ?>" data-bs-toggle="modal" data-bs-target="#modalFiltro">
                <?php if ($_SESSION["filtros"]["categoria"]["id"] == 0) :?>
                 <img src="/files/filtro.svg" alt="">
                 <?php else: ?>
                    <img class="icon" src="/files/<?= "categoria_" . $_SESSION["filtros"]["categoria"]["id"] ?>.svg" alt="">
                <?php endif?>
             </div>
         </div>

     </div> <!-- mov-max -->


     <!--------------------------------------------- VERSIÓN DE PC --------------------------------------------------------------->
     <div class="nav-max">
         <a href="/" style="text-decoration: none">
            <div class="logo">
                <span>Citas</span>
                <span>10</span>
            </div> <!-- nav-max -->
         </a>
         <div class="interface">
            
             <div class="ubicacion"> 
                 <div class="valor" data-bs-toggle="modal" data-bs-target="#modalModoGeo">
                     <div class="box"> 
                        <span> <img src="/files/locale-red.svg" alt=""> </span>  
                        <span id="direccionStrPc"></span>
                     <!--    <img src="/files/dropdown-search.svg" alt="">-->
                     <span class="dropdown-locale"> <img src="/files/dropdown-red.svg" alt=""> </span>  

                     </div> 

                    

                 </div>
             </div>
             <div class="orden" data-bs-toggle="modal" data-bs-target="#modalOrden">
                 <div class="desc"> <img src="/files/orden.svg" alt=""> <span>Orden por:</span></div>
                 <!-- <div class="valor"><span> <img class="icon" src="/files/proximidad.svg" alt=""> </span> Últimos días <img class="dropdown-svg" src="/files/dropdown.svg" alt=""> </div> -->
                 <div class="valor <?=$_SESSION['filtros']['orden']['slug'] != 'posicion' ? 'active' : ''?>"><span> <img class="icon" src="/files/orden_<?=$_SESSION['filtros']['orden']['slug']?>.svg" alt=""> </span> <?= $_SESSION['filtros']['orden']['nombre'] ?>
                     <img class="dropdown-svg" src="/files/dropdown.svg" alt="">
                 </div>


                 
             </div>
             <div class="filtros" data-bs-toggle="modal" data-bs-target="#modalFiltro">
                 <div class="desc">
                     <div class="filtro"><span> <img class="icon" src="/files/filtro.svg" alt=""> </span>Filtrar por: </div>
                     <div class="buscar"> <img class="icon" src="/files/buscar.svg" alt=""> </div>
                 </div>
                 <div class="valor <?= $_SESSION["filtros"]["categoria"]["id"] != 0 ? 'active' : 0  ?>"><span> <img class="icon" src="/files/<?= "categoria_" . $_SESSION["filtros"]["categoria"]["id"] ?>.svg" alt=""> </span>
                     <?= $_SESSION["filtros"]["categoria"]["nombre"] ?> <img class="dropdown-svg" src="/files/dropdown.svg" alt=""></div>

             </div>
         </div><!-- interface -->

     </div> <!-- nav-max -->

 </div> <!-- nav -->

 <main class="public">
 <div class="container-lg"> 