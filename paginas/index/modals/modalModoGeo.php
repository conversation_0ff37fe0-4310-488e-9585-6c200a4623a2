<!------------------------ Programar renovar modal -------------------------- !-->
<form action="" method="POST">
    <div class="modal face"  id="modalModoGeo" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title">Editar posición</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><img src="/files/close.svg" alt=""></button>
             
        </div>
        <div class="modal-body text-center"> 
            <p>Calcular distancias desde: </p>
       
        <div class="d-flex justify-content-evenly">
             <button type="button" class="btn btn-success" id="geolocalizacion">
           Mi posición
            </button>
            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#modalDireccion">
           Una dirección
            </button> 
        </div>

<!-- Fila para seleccionar una ciudad en un select bootstrap 5 CENTRADO -->
        <div class="row mt-3 justify-content-center">
            <p>Esta filtrando los anuncio de la ciudad:</p>
            <div class="col-4">
                <select class="form-select" aria-label="Default select example">
                    <option value="1" selected>Pamplona</option>
                    <option value="2">Madrid</option>
                    
                </select>
            </div>
        </div>
       
        </div> 
        </div>
    </div>
    </div>
 </form>
