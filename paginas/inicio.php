<?php
 

$hora = date('H:i:s');
$destacados = isset($_GET['destacados']);
  
//$anuncios = getfun("getAnuncios()")["data"] ; 

$anuncios = getfun("getAnuncios()")["data"] ; 



//Agregar WHERE estado=1 a la view_portadas para optimizar INNER JOIN

$superDestacados = array_filter($anuncios, function ($anuncio) {
    return $anuncio->estado_destacado == 1 && $anuncio->destacado_tipo == 1;
});

$destacadosNormales = array_filter($anuncios, function ($anuncio) {
    return $anuncio->estado_destacado == 1 && $anuncio->destacado_tipo == 0;
});



shuffle($superDestacados);
shuffle($destacadosNormales);
  

 
  include app\part('modalFiltro'); 
  include app\part('modalDireccion'); 
  include app\part('modalModoGeo'); 
  include app\part('modalOrden');
?>
 

<script>
let autocomplete;
let currentGPS = <?=$_SESSION['userGPS']['js']?>; 
// console.log(currentGPS);

const initMap = () => {
    const $direccion = document.getElementById('direccion');
    const options = {
        componentRestrictions: {
            country: "es"
        },
        fields: ["address_components", "geometry", "icon", "name"],
        strictBounds: false
    }
    autocomplete = new google.maps.places.Autocomplete($direccion, options)
    autocomplete.addListener("place_changed", () => {
        const place = autocomplete.getPlace();
        const position = place.geometry.location;
        const {
            lat,
            lng
        } = position;
        const positionJSON = `${lat()},${lng()}`
        console.log(positionJSON);
        const $coords = document.getElementById('coords');
        $coords.value = positionJSON;
    });

    (

        async () => {

            const $direccionStrMovil = document.getElementById('direccionStrMovil');
            const $direccionStrPc = document.getElementById('direccionStrPc');

            const direccionSolicitada = `${currentGPS.lat}, ${currentGPS.lng}`;
            
            // Si nunca se guardo una dirección se guarda por primera vez la dirección por defecto
            if (localStorage.getItem("direccionUltima") == null )
            {
               localStorage.setItem("direccionUltima", direccionSolicitada);
               localStorage.setItem("direccionStr", "Pamplona, Navarra (Pulsa aquí para cambiar)");
            }
            
            const direccionUltima = localStorage.getItem("direccionUltima");
            var direccionStr = localStorage.getItem("direccionStr");

            // SI LA DIRECCIÓN SOLICITADA ES LA MISMA QUE LA DE LOCALSTORAGE ??
            console.log(`direccionUltima: ${direccionUltima} direccionSolicitada: ${direccionSolicitada}`);
    
            if (direccionUltima != direccionSolicitada)
            {
                console.log(" La dirección ha cambiado ");
                const direccion = await geocodeLatLng(currentGPS);  // Solo ejecutar esta si el usuario pulso en la barra
                 direccionStr = direccion.replace(", España", "").replace(", Navarra", "");  
                  // Actualizamos el localstorage
                localStorage.setItem("direccionUltima", direccionSolicitada);
                localStorage.setItem("direccionStr", direccionStr);
 
            } else{
                console.log("La dirección no ha cambiado"); 
            }  

            // Escribimos el texto en la navbar
            $direccionStrMovil.innerHTML = direccionStr;
            $direccionStrPc.innerHTML = direccionStr;  

        }
    )()

}

 window.initMap = initMap

const geocodeLatLng = async (latlng) => {
    console.log("geocoding", latlng);
    const geocoder = new google.maps.Geocoder();
    const response = await geocoder.geocode({
        location: latlng
    });
    if (response.results[0]) {
        console.log("objGeo",response.results[0])
        const result = {}
       
        const outputAddress = response.results[0].formatted_address;
        return outputAddress;
    }
    return "No se encontró ubicación";
}
const getLocation = (callback = null) => {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            async (position) => {
                const coords = position.coords;
                const {
                    latitude,
                    longitude
                } = coords;
                const positionText = `${latitude},${longitude}`
                const $coords = document.getElementById('coords');
                $coords.value = positionText;
                console.log("value: ",$coords.value);
                const direccion = await geocodeLatLng({
                    lat: latitude,
                    lng: longitude
                });
                const $direccion = document.getElementById('direccion');
                $direccion.value = direccion;
                if (callback) callback();
                 
            }
        )
    } else {
        console.log("ERROR")
    }
}
const $btnGeolocalizacion = document.getElementById("geolocalizacion");
$btnGeolocalizacion.addEventListener('click', (e) => {
    e.preventDefault();
    console.log("CLICK")
    getLocation(() => {
        $form = document.getElementById("formGPS");
        console.log("form: ",$form);
        $form.submit();
         
    });

});
</script>




<?php 
if (isset($_GET['search'])) : ?>

    <div class="alert alert-warning alert-dismissible fade show col-12 col-md-6 mx-auto mt-md-5 text-center"
        role="alert">
        Mostrando busqueda: <strong> <?= $_GET['search'] ?></strong>
        <a href="/">
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </a>
    </div>


<?php endif ?>



<!-- <embed type="image/svg+xml" src="image.svg" /> Cargar svg -->


<?php if ($_SESSION["filtros"]["orden"]["slug"] == 'posicion') : ?>
      
    <!-- LIBRERIAS -->
    <script src="/js/autocomplete.js"></script>
    <link rel="stylesheet" href="/css/aos.css">
    <script src="/js/<EMAIL>"></script>
<div class="row align-items-center contenedor destacados justify-content-center">

    <!-- FOR COMPONENTE SUPERDESTACADO -->

    <?php foreach ($superDestacados as $anuncio) : ?>
        <?php  
        $portadasSQL = json_decode($anuncio->portadas, true);
        $portadasAnuncio = $portadasSQL['anuncio'];
        $portadasDestacado = $portadasSQL['destacado'];
        $portadas = isset($portadasDestacado[0]) ? $portadasDestacado : $portadasAnuncio; 
        $portadasJSON = str_replace('"', "'", json_encode($portadas));
        $portada = isset($portadas[0]) ? "/uploads/fotos/" . $portadas[0] : "/" . app\Config::$files . "/vacia.jpg";
        ?>
    <div class="col-6 col-xl-4 padre-portada" data-aos="zoom-in">
        <?php include app\part('portada'); ?>
    </div> <!-- col-6 portada -->
    <?php endforeach ?>

</div> <!-- row destacados -->
<?php endif ?>
<?php if ($_SESSION["filtros"]["orden"]["slug"] == 'posicion') : ?>
<div class="row align-items-center contenedor destacados justify-content-center">

    <!-- FOR COMPONENTE DESTACADO -->

    <?php foreach ($destacadosNormales as $anuncio) : ?>
        <?php  
        $portadasSQL = json_decode($anuncio->portadas, true);
        $portadasAnuncio = $portadasSQL['anuncio'];
        $portadasDestacado = $portadasSQL['destacado'];
        $portadas = isset($portadasDestacado[0]) ? $portadasDestacado : $portadasAnuncio; 
        $portadasJSON = str_replace('"', "'", json_encode($portadas));
        $portada = isset($portadas[0]) ?  "/uploads/fotos/" . $portadas[0] : "/" . app\Config::$files . "/vacia.jpg";
        ?>
    <div class="col-6 col-xl-4" data-aos="zoom-in">
        <?php include app\part('portada'); ?>
    </div> <!-- col-6 portada -->
    <?php endforeach ?>

</div> <!-- row -->
<?php endif ?>

<div class="row align-items-center contenedor anuncios justify-content-center">

    <!-- FOR COMPONENTE PORTADA -->
        
    <?php foreach ($anuncios as $anuncio) : ?>
        <?php  
        $portadasSQL = json_decode($anuncio->portadas, true); 
        $portadas = $portadasSQL['anuncio']; 
        $portadasJSON = str_replace('"', "'", json_encode($portadas));
        $portada = isset($portadas[0]) ?  "/uploads/fotos/" . $portadas[0] : "/" . app\Config::$files . "/vacia.jpg";

        $horarios = getfun("getHorario(\"id\":$anuncio->id)")['data'];
        $rango1 = $horarios[0];
        $rango2 = $horarios[1];
        $rango3 = $horarios[2]; 
        
        ?>
    <div class="col-6 col-xl-4" data-aos="zoom-in">
        <?php include app\part('portada'); ?>
    </div> <!-- col-6 portada -->
    <?php endforeach ?>
 

    <h1 style="color: white; font-size: 16px; text-align: center; margin-bottom: 10px">De NAVARRASEX A CITAS 10</h1>

    <p style="color: white; font-size: 14px">
    Llegan nuevos tiempos y con ellos los cambios. Con la nueva ley para publicidad donde se prohíbe la publicidad que promueva la prostitución en Pamplona o en todo el territorio nacional. Hemos tomado la decisión de no realizar publicidad para putas en Pamplona y, con esta decisión hemos dejado atrás un periodo de casi dos décadas que bajo el dominio NAVARRASEX; Hemos sido los primeros en navarra y de los primeros de España, en llevar el sexo de pago a la web. Pero ese período a terminado y ponemos punto final a esta trayectoria de éxitos en darle un espacio para los trabajadores del sexo en Pamplona.
</p>

        <h2 style="color: white; font-size: 16px; text-align: center; margin-bottom: 10px">¿QUE NOS TRAE CITAS 10?</h2>
        <p style="color: white; font-size: 14px">
        Citas 10 nace para dar un espacio para las Acompañantes, Masajistas del bienestar, stripper, etc. A diferencia de NAVARRASEX los servicios que prestan estas personas no incluyen intercambio de sexo por dinero, lo que sería prostitución. Citas 10 nos trae un espacio para relacionarte con acompañantes también conocidas como ESCORTS en Pamplona. Que pueden acompañante a pasar una velada juntos conociendo la ciudad, ir a bailar, cenar o tal vez necesites una acompañante para acudir a cena de negocios o un viaje al que no quieres acudir solo. 
</p>
<h2 style="color: white; font-size: 16px; text-align: center; margin-bottom: 10px">APERTURA DE LA RIOJA - LOGROÑO</h2>
<p style="color: white; font-size: 14px; text-align: center; ">
        Visita nuestra web de la rioja para Logroño <a href="https://larioja.citas10.net">larioja.citas10.net</a>
</p>


<p style="color: white; font-size: 14px; text-align: center; margin-bottom: 10px"><strong>Aviso importante:</strong> </p> 
  <p style="color: white; font-size: 14px">De conformidad con lo dispuesto en la Ley Orgánica 10/2022 de 6 de septiembre, se avisa a los usuarios que en esta página web no se permiten anuncios de putas o escorts, limitandose a una aplicación de citas o encuentros</p>

 
</div> <!-- row -->

<script>
function alternarFotos() {
    $imgAnuncios = Array.from(document.querySelectorAll(".imgAnuncio"));

    $imgAnuncios.forEach(
        ($imgAnuncio) => {
            $src = $imgAnuncio.getAttribute("src");

            $fotos = JSON.parse($imgAnuncio.getAttribute("data-images").replace(/'/g, '"'));
            $activeIndex = parseInt($imgAnuncio.getAttribute("data-active-image"));
            $activeFoto = $fotos[$activeIndex];
            let $newIndex = ($activeIndex >= $fotos.length - 1) ? 0 : $activeIndex + 1;
            $newSrc = $src.replace($activeFoto, $fotos[$newIndex]);

            //console.log($src, $newSrc);
            $imgAnuncio.setAttribute("src", $newSrc);
            $imgAnuncio.setAttribute("data-active-image", $newIndex);

        }
    )
}



setInterval(
    () => {
        alternarFotos();
    }, 2000
)


function alternarPortadaMensaje() {
    const $portadaMensajes = Array.from(document.querySelectorAll("#portada_mensaje"));

    $portadaMensajes.forEach(
        ($el) => {

            let contenido = $el.textContent;
            const t1 = $el.getAttribute('data-1')
            const t2 = $el.getAttribute('data-2')
            if (!t2) return
            if (t1 == contenido) {
                $el.textContent = t2;
            } else {
                $el.textContent = t1;
            }

        }
    )

}
setInterval(
    () => {
        alternarPortadaMensaje();
    }, 1500
)



AOS.init();


// agrega hash a todos los links de anuncios

// previewMantenimiento();

// setInterval(fetchMantenimientoAPI, 2000) 

</script>