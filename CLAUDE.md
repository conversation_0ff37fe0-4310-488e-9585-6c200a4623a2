# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Frontend Development
- `npm start` - Start browser-sync for development with proxy to citas10.test
- `npm run tunnel` - Create localtunnel for external access

### Backend Development  
- `composer install` - Install PHP dependencies
- `php models/generate.php` - Generate model classes from database schema

### Database
- Development database: `dev.citas10` on `dev.citas10.net`
- Production database: `citas10` on `localhost`

## Architecture Overview

This is a PHP web application using FlexHTTP, a custom lightweight MVC framework. The architecture is designed around a flexible routing system with automatic model generation.

### Core Framework Components

**FlexHTTP Framework**: A custom MVC framework centered around the `Flex` class that handles:
- URL routing and parameter extraction
- Automatic view and controller loading
- Environment detection (dev/production)
- Dynamic function execution from URLs

**Key Classes**:
- `Flex` - Main framework class accessible via `flex()` function
- `Config` - Centralized configuration management
- Auto-generated model classes from database schema

### Directory Structure

```
/
├── includes/           # Framework core files
│   ├── Flex.php       # Main framework class
│   ├── framework.php  # Core functions and Config class
│   ├── phprouter.php  # Advanced routing system
│   ├── routes.php     # Route definitions
│   ├── funciones.php  # Application functions
│   └── auth.php       # Authentication system
├── controllers/       # Application controllers
├── models/           # Auto-generated model classes
│   └── generate.php  # Model generator script
├── paginas/          # Views/pages (MVC Views)
├── partials/         # Reusable components (head, nav, footer)
├── css/             # Stylesheets
├── js/              # JavaScript files
└── uploads/         # User uploaded files
```

### Routing System

The application uses a dual routing approach:

1. **File-based routing**: Maps URLs to files in `paginas/` directory
   - `/inicio` → `paginas/inicio.php`
   - `/admin/usuarios` → `paginas/admin/usuarios.php`

2. **Function-based routing**: Modern route definitions in `includes/routes.php`
   ```php
   get('/inicio/nombre/$nombre', function($nombre) { 
       echo "Hola $nombre"; 
   });
   ```

### URL Structure and URI Function

URLs follow the pattern: `http://site.com/[section]/[page]/[parameters]`

The `uri()` function is central to routing:
- `uri()` - Returns full path
- `uri(0)` - First segment (section)
- `uri(1)` - Second segment (page)
- `uri("a")` - All segments as array
- `uri("full")` - Complete REQUEST_URI

### Controller Loading

Controllers are loaded dynamically based on URL path:
- Main controller: `controllers/[section]Controller.php`
- Page controllers: `paginas/[section]/[page]Controller.php`
- Nested controllers: `paginas/[section]/[subsection]/[page]Controller.php`

### Model Generation

Models are automatically generated from database schema using `models/generate.php`. This script:
- Connects to the database
- Introspects table structure
- Generates PHP classes with typed properties
- Creates a `Models.php` file that includes all models

### Environment Detection

The framework automatically detects development vs production:
- Development: Windows OS, subdomains starting with `dev.`, or `.test`/`.dev` domains
- Production: All other environments

### Dynamic Function Execution

Functions can be executed via URL parameters:
- `?fun[]=functionName(param1:value1,param2:value2)`
- Multiple functions can be chained
- Special handling for `redirect` function (always executes last)

### Authentication System

Role-based authentication system:
- User roles stored in database
- Role-based access control for different sections
- Session management for user state

## Development Workflow

1. **Adding New Pages**: Create PHP files in `paginas/` directory following the routing structure
2. **Adding Controllers**: Create controller files that match the page structure
3. **Database Changes**: Run `php models/generate.php` to regenerate models after schema changes
4. **Frontend Changes**: Use `npm start` for live reload during development

## Important Notes

- The framework uses PHP 8.3+ features
- Database credentials are hardcoded in `includes/pdo.php` and `models/generate.php`
- Development mode enables debug toolbar and additional logging
- The application uses Composer for dependency management (PHPMailer, Faker, etc.)
- Browser-sync is configured for development with proxy to `citas10.test`